package com.yhl.scp.mds.bom.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mds.bom.dto.ProductBomDTO;
import com.yhl.scp.mds.bom.vo.ProductBomAlternativeVO;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;

import java.util.List;
import java.util.Map;

/**
 * <code>ProductBomService</code>
 * <p>
 * 物品BOM应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-13 16:24:31
 */
public interface MdsProductBomService extends BaseService<ProductBomDTO, ProductBomVO> {

    List<ProductBomVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link ProductBomVO}
     */
    List<ProductBomVO> selectAll();

    List<ProductBomVO> selectByBillBomVersionIds(List<String> bomVersionIds);

    void doLogicDeleteBatch(List<RemoveVersionDTO> deleteProductBomList);

    List<ProductBomVO> selectByComponentSequenceIds(List<String> componentSequenceIds);

    List<String> selectIoProductIdsByBomVersionId(String bomVersionIdList);

    List<ProductBomVO> selectComponentSequenceNotNull();

    PageInfo<ProductBomAlternativeVO> selectAlternative(String productCode, Integer pageNum, Integer pageSize);

}