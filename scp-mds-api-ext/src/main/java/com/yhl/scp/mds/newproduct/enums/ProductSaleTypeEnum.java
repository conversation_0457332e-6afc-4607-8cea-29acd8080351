package com.yhl.scp.mds.newproduct.enums;

import com.yhl.platform.common.enums.CommonEnum;

/**
 * <p>
 * 销售类型枚举
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-05-16 18:05:17
 */
public enum ProductSaleTypeEnum implements CommonEnum {

    /**
     * DX
     */
    DX("DX", "代销"),

    /**
     * ZCZX
     */
    ZCZX("ZCZX", "自产自销"),
    
    
    /**
     * DKP
     */
    DKP("DKP", "代开票"),

    /**
     * WTSC
     */
    WTSC("WTSC", "委托生产"),
    ;

    private String code;

    private String desc;

    ProductSaleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

}