package com.yhl.scp.mrp.material.distribute.domain.entity;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.biz.common.constants.StringConstants;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.biz.common.util.AsyncTaskUtils;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.service.ProductSubstitutionRelationshipService;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.inventory.service.InventoryFloatGlassShippedDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryOurFactoryDetailService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.distribute.infrastructure.dao.MaterialDemandDao;
import com.yhl.scp.mrp.material.distribute.infrastructure.dao.MaterialFulfillmentDao;
import com.yhl.scp.mrp.material.distribute.infrastructure.dao.MaterialSupplyDao;
import com.yhl.scp.mrp.material.distribute.service.MaterialDemandService;
import com.yhl.scp.mrp.material.distribute.service.MaterialFulfillmentService;
import com.yhl.scp.mrp.material.distribute.service.MaterialSupplyService;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanReplaceDao;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanReplaceService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanReplaceVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.substitutionRelationship.service.GlassSubstitutionRelationshipService;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DistributeDataSupport</code>
 * <p>
 * 齐套检查通用数据
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 14:55:07
 */
@Slf4j
public class DistributeDataSupport {

    protected static final String TYPE_FLAG = "RA.A";
    protected static final String AUTOMOBILE_TRANSPORTATION = "汽车运输";
    protected static final String CONTAINER_TRANSPORTATION = "集装箱直运";

    @Resource
    protected MpsFeign mpsFeign;
    @Resource
    protected DfpFeign dfpFeign;
    @Resource
    protected IpsNewFeign ipsNewFeign;
    @Resource
    protected IpsFeign ipsFeign;
    @Resource
    protected NewMdsFeign newMdsFeign;
    @Resource
    protected MaterialDemandDao materialDemandDao;
    @Resource
    protected MaterialDemandService materialDemandService;
    @Resource
    protected MaterialSupplyDao materialSupplyDao;
    @Resource
    protected MaterialSupplyService materialSupplyService;
    @Resource
    protected MaterialFulfillmentDao materialFulfillmentDao;
    @Resource
    protected MaterialFulfillmentService materialFulfillmentService;
    @Resource
    protected MaterialGrossDemandService materialGrossDemandService;

    @Resource
    protected GlassSubstitutionRelationshipService glassSubstitutionRelationshipService;
    @Resource
    protected MaterialArrivalTrackingService materialArrivalTrackingService;
    @Resource
    protected MaterialPlanReplaceService materialPlanReplaceService;
    @Resource
    protected MaterialPlanReplaceDao materialPlanReplaceDao;
    @Resource
    protected InventoryFloatGlassShippedDetailService inventoryFloatGlassShippedDetailService;
    @Resource
    protected InventoryQuayDetailService inventoryQuayDetailService;

    protected DistributeContext initData(List<OperationVO> operationVOS, String scenario) {
        // 创建 DistributeContext 对象
        DistributeContext context = new DistributeContext();
        context.setOperationVOS(operationVOS);
        context.setInsertMaterialDemandList(new ArrayList<>());
        context.setInsertMaterialFulfillmentList(new ArrayList<>());
        context.setInsertMaterialSupplyList(new ArrayList<>());
        context.setDemandProductList(new ArrayList<>());
        context.setReplaceMappingMap(new HashMap<>());
        context.setLogInfo(new ArrayList<>());

        // 物料相关数据获取
        CompletableFuture<Void> productFuture = AsyncTaskUtils.runAsync("物品", () -> {
            FeignDynamicParam productParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("id", "stock_point_code", "product_code", "product_name", "product_type", "product_classify"))
                    .queryParam(new HashMap<>())
                    .build();
            List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, productParam);
            Map<String, NewProductStockPointVO> productStockPointVOMap = StreamUtils.mapByColumn(productStockPointVOList, NewProductStockPointVO::getId);
            Map<String, NewProductStockPointVO> productCodeMap = StreamUtils.mapByColumn(productStockPointVOList, p -> StrUtil.join(StringConstants.SPLIT_STR_1,
                    p.getStockPointCode(), p.getProductCode()));
            context.setProductOnIdMap(productStockPointVOMap);
            context.setProductCodeMap(productCodeMap);
        });


        // 工艺路径相关数据获取
        CompletableFuture<Void> routingFuture = AsyncTaskUtils.runAsync("工艺路径相关数据获取", () -> {
            FeignDynamicParam routingParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("id", "product_id", "stock_point_id", "product_code", "stock_point_code"))
                    .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())).build();
            List<RoutingVO> routingVOList = newMdsFeign.selectRoutingByParamOnDynamicColumns(scenario, routingParam);
            Map<String, RoutingVO> routingVOMapOfProductCode = routingVOList.stream()
                    .collect(Collectors.toMap(RoutingVO::getProductCode, Function.identity(), (k1, k2) -> k2));

            context.setRoutingVOMapOfProductCode(routingVOMapOfProductCode);
        });

        // 工艺路径步骤相关数据获取
        CompletableFuture<Void> routingStepFuture = AsyncTaskUtils.runAsync("工艺路径步骤相关数据获取", () -> {
            FeignDynamicParam routingStepParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("id", "routing_id", "sequence_no", "next_routing_step_sequence_no", "yield"))
                    .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode())).build();
            List<RoutingStepVO> routingStepVOList = newMdsFeign.selectRoutingStepByParamOnDynamicColumns(scenario, routingStepParam);
            Map<String, RoutingStepVO> routingStepMapOfId = routingStepVOList.stream().collect(Collectors.toMap(RoutingStepVO::getId, Function.identity()));
            Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId = routingStepVOList.stream().collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));

            context.setRoutingStepMapOfId(routingStepMapOfId);
            context.setRoutingStepGroupOfRoutingId(routingStepGroupOfRoutingId);
        });

        // 工艺路径步骤输入数据获取
        CompletableFuture<Void> routingStepInputFuture = AsyncTaskUtils.runAsync("工艺路径步骤输入数据获取", () -> {
            FeignDynamicParam routingStepInputParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("id", "routing_step_id", "input_product_id", "yield", "scrap", "input_factor", "supply_type"))
                    .queryParam(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()))
                    .build();
            List<RoutingStepInputVO> routingStepInputVOList = newMdsFeign.selectRoutingStepInputByParamOnDynamicColumns(scenario, routingStepInputParam);
            Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId = routingStepInputVOList.stream()
                    .collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));

            context.setRoutingStepInputGroupOfStepId(routingStepInputGroupOfStepId);
        });

        // 需求操作过滤和反馈生产数据获取
        CompletableFuture<Void> demandOperationFuture = AsyncTaskUtils.runAsync("需求操作过滤和反馈生产数据获取", () -> {
            List<String> planStatusList = ListUtil.of(PlannedStatusEnum.STARTED.getCode(), PlannedStatusEnum.PLANNED.getCode());
            List<OperationVO> demandOperation = operationVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getParentId())
                    && planStatusList.contains(p.getPlanStatus())).collect(Collectors.toList());

            List<String> operationIdList = demandOperation.stream().map(OperationVO::getId).collect(Collectors.toList());
            List<String> orderIds = demandOperation.stream().map(OperationVO::getOrderId).distinct().collect(Collectors.toList());
            List<FeedbackProductionVO> feedbackProductionVOList = mpsFeign.selectFeedbackProductionByOperationIds(scenario, operationIdList);
            Map<String, List<FeedbackProductionVO>> feedbackProductionGroup = feedbackProductionVOList.stream()
                    .collect(Collectors.groupingBy(FeedbackProductionVO::getOperationId));
            List<WorkOrderVO> workOrderVOS = mpsFeign.selectWorkOrderByParams(scenario, ImmutableMap.of("ids", orderIds));

            context.setFeedbackProductionGroup(feedbackProductionGroup);
            context.setWorkOrderVOS(workOrderVOS);
        });

        // 替代需求数据获取
        CompletableFuture<Void> replaceDataFuture = AsyncTaskUtils.runAsync("替代需求数据获取", () -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<String> demandTypes = ListUtil.of(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode(), MrpDemandSourceEnum.MPS.getCode());
            List<MaterialGrossDemandVO> replaceDemandList = materialGrossDemandService.selectByParams(ImmutableMap.of("demandSourceList", demandTypes));
            // 先按 productFactoryCode + mainProductCode 分组，然后在每组内根据 mainProductCode 去重
            Map<String, List<MaterialGrossDemandVO>> replaceDemandMap = replaceDemandList.stream()
                    .filter(p -> StrUtil.isNotEmpty(p.getMainProductCode()) && p.getDemandSource().equals(MrpDemandSourceEnum.ALTERNATIVE_DEMAND.getCode()))
                    .collect(Collectors.groupingBy(
                            p -> StrUtil.join(StringConstants.SPLIT_STR_1, p.getProductFactoryCode(), p.getMainProductCode()),
                            LinkedHashMap::new,
                            Collectors.collectingAndThen(
                                    Collectors.toList(),
                                    list -> {
                                        Map<String, MaterialGrossDemandVO> uniqueMap = new LinkedHashMap<>();
                                        for (MaterialGrossDemandVO vo : list) {
                                            uniqueMap.putIfAbsent(vo.getMainProductCode(), vo);
                                        }
                                        return new ArrayList<>(uniqueMap.values());
                                    }
                            )
                    ));
            Map<String, List<MaterialGrossDemandVO>> finishedMaterialGrossDemand = StreamUtils.mapListByColumn(replaceDemandList, MaterialGrossDemandVO::getProductFactoryCode);
            List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS = newMdsFeign.getProductSubstitutionRelationshipVOByParams(scenario, ImmutableMap.of("rule", "A"));
            Map<String, List<ProductSubstitutionRelationshipVO>> productSubstitutionMap = StreamUtils.mapListByColumn(productSubstitutionRelationshipVOS, p -> StrUtil.join(StringConstants.SPLIT_STR_1,
                    p.getStockPointCode(), p.getProductCode(), p.getRawProductCode()));
            DynamicDataSourceContextHolder.clearDataSource();
            context.setReplaceDemandMap(replaceDemandMap);
            context.setProductSubstitutionMap(productSubstitutionMap);
            context.setFinishedMaterialGrossDemand(finishedMaterialGrossDemand);
        });

        // 原片临时替代数据获取
        CompletableFuture<Void> materialReplaceFuture = AsyncTaskUtils.runAsync("原片临时替代数据获取", () -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<MaterialPlanReplaceVO> materialPlanReplaceVOS = materialPlanReplaceService.selectAll();
            List<MaterialPlanReplaceVO> convertList = materialPlanReplaceDao.convertProductCode();
            Map<String, List<String>> replaceProductMap = convertList.stream()
                    .collect(Collectors.groupingBy(
                            MaterialPlanReplaceVO::getMasterProductCode,
                            Collectors.mapping(
                                    MaterialPlanReplaceVO::getReplaceProductCode,
                                    Collectors.toList()
                            )
                    ));
            DynamicDataSourceContextHolder.clearDataSource();
            context.setMaterialPlanReplaceVOS(materialPlanReplaceVOS);
            context.setReplaceProductMap(replaceProductMap);
        });


        // 库存和到货跟踪数据获取
        CompletableFuture<Void> inventoryFuture = productFuture.thenRun(() -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            Map<String, NewProductStockPointVO> productCodeMap = context.getProductCodeMap();
            if (productCodeMap != null) {
                List<InventoryBatchDetailVO> inventoryBatchDetail = getInventoryBatchDetail(scenario, productCodeMap);
                context.setInventoryBatchDetail(inventoryBatchDetail);
            }
            DynamicDataSourceContextHolder.clearDataSource();
        });

        // 到货跟踪数据获取
        CompletableFuture<Void> arrivalTrackingFuture = AsyncTaskUtils.runAsync("到货跟踪数据获取", () -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS = materialArrivalTrackingService
                    .selectVOByParams(ImmutableMap.of("arrivalStatusList", ListUtil.of(ArrivalStatusEnum.DELIVERED.getCode(),
                            ArrivalStatusEnum.PLAN_PRUCHASE.getCode())));
            DynamicDataSourceContextHolder.clearDataSource();
            context.setMaterialArrivalTrackingVOS(materialArrivalTrackingVOS);
        });


        // 在途和码头库存数据获取
        CompletableFuture<Void> inTransitFuture = AsyncTaskUtils.runAsync("在途和码头库存数据获取", () -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            // 汽运在途 & 海运在途
            List<InventoryFloatGlassShippedDetailVO> floatGlassShippedDetailVOS = inventoryFloatGlassShippedDetailService
                    .selectByParams(ImmutableMap.of("deliveryMethodList", ListUtil.of(AUTOMOBILE_TRANSPORTATION, CONTAINER_TRANSPORTATION),
                            "enabled", YesOrNoEnum.YES.getCode(), "storageFlag", YesOrNoEnum.NO.getCode()));
            context.setFloatGlassShippedDetailVOS(floatGlassShippedDetailVOS);

            // 码头库存
            List<InventoryQuayDetailVO> inventoryQuayDetailVOS = inventoryQuayDetailService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
            DynamicDataSourceContextHolder.clearDataSource();
            context.setInventoryQuayDetailVOS(inventoryQuayDetailVOS);
        });

        CompletableFuture<Void> planningHorizonFuture = AsyncTaskUtils.runAsync("计划期间", () -> {
            DynamicDataSourceContextHolder.setDataSource(scenario);
            PlanningHorizonVO planningHorizonVO = newMdsFeign.selectPlanningHorizon(scenario);
            context.setPlanningHorizonVO(planningHorizonVO);
            DynamicDataSourceContextHolder.clearDataSource();
        });

        // 等待所有异步任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                productFuture, routingFuture, routingStepFuture, routingStepInputFuture,
                demandOperationFuture, replaceDataFuture, materialReplaceFuture,
                inventoryFuture, arrivalTrackingFuture, inTransitFuture,planningHorizonFuture
        );
        // 等待所有任务完成
        allFutures.join();

        return context;
    }


    private List<InventoryBatchDetailVO> getInventoryBatchDetail(String scenario, Map<String, NewProductStockPointVO> productCodeMap) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<String> type = ListUtil.of(
                StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode(),
                StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode(),
                StockPointOrganizeTypeEnum.PURCHASE_ORGANIZATION.getCode()
        );
        List<String> stockPointCode = newMdsFeign.selectAllStockPoint(scenario)
                .stream().filter(p -> StrUtil.isNotEmpty(p.getOrganizeType()) &&
                        type.contains(p.getOrganizeType()))
                .filter(p -> StrUtil.isNotEmpty(p.getStockPointType()) && p.getStockPointType().equals(StockPointTypeEnum.BC.getCode()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        List<InventoryBatchDetailVO> inventoryBatchDetailVOS = dfpFeign.selectInventoryBatchDetailVOByParams03(scenario,
                ImmutableMap.of("stockPointCodeList", stockPointCode));
        Map<String, SubInventoryCargoLocationVO> subInventoryCargoLocation = getSubInventoryCargoLocation(scenario, inventoryBatchDetailVOS);
        Iterator<InventoryBatchDetailVO> iterator = inventoryBatchDetailVOS.iterator();
        while (iterator.hasNext()) {
            InventoryBatchDetailVO inventoryBatchDetailVO = iterator.next();
            String freightSpace = inventoryBatchDetailVO.getFreightSpace();
            if (StrUtil.isEmpty(freightSpace)) {
                iterator.remove();
                continue;
            }
            SubInventoryCargoLocationVO subInventoryCargoLocationVO = subInventoryCargoLocation.getOrDefault(freightSpace,new SubInventoryCargoLocationVO());
            String enabled = subInventoryCargoLocationVO.getEnabled() == null ? YesOrNoEnum.NO.getCode() : subInventoryCargoLocationVO.getEnabled();
            String key = StrUtil.join(StringConstants.SPLIT_STR_1, inventoryBatchDetailVO.getStockPointCode(), inventoryBatchDetailVO.getProductCode());
            // 货位是否可用
            if (!enabled.equals(YesOrNoEnum.YES.getCode()) || !productCodeMap.containsKey(key)) {
                iterator.remove();
                continue;
            }
            NewProductStockPointVO newProductStockPointVO = productCodeMap.get(key);
            inventoryBatchDetailVO.setSourceType(newProductStockPointVO.getProductClassify());
        }
        DynamicDataSourceContextHolder.clearDataSource();
        return inventoryBatchDetailVOS;
    }

    private String getRangeData() {
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "SUB_INVENTORY", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        return rangeData;
    }

    private Map<String, SubInventoryCargoLocationVO> getSubInventoryCargoLocation(String scenario, List<InventoryBatchDetailVO> inventoryBatchDetailVOS) {
        List<String> freightSpaces = inventoryBatchDetailVOS.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .distinct().collect(Collectors.toList());
        // 库存货位
        return mpsFeign.subInventoryCargoLocation(scenario, freightSpaces)
                .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (k1, k2) -> k1));
    }

}
