package com.yhl.scp.mrp.supplier.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.excel.ExcelUtils;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.FormatConversionUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.supplier.infrastructure.po.SupplierPurchaseRatioBasicPO;
import com.yhl.scp.mds.basic.supplier.vo.SupplierBasicVO;
import com.yhl.scp.mds.extension.routing.dto.RoutingDTO;
import com.yhl.scp.mds.extension.supplier.dto.SupplierPurchaseRatioDTO;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO;
import com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPurchaseRatioPO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.routing.vo.BomTreeNewVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mrp.enums.DemandPatternEnum;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.inventory.dto.SafetyInventoryDTO;
import com.yhl.scp.mrp.inventory.service.SafetyInventoryService;
import com.yhl.scp.mrp.inventory.vo.SafetyInventoryVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandVersionService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVersionVO;
import com.yhl.scp.mrp.supplier.convertor.MaterialSupplierPurchaseConvertor;
import com.yhl.scp.mrp.supplier.convertor.SupplierPurchaseRatioConvertor;
import com.yhl.scp.mrp.supplier.domain.entity.MaterialSupplierPurchaseDO;
import com.yhl.scp.mrp.supplier.domain.service.MaterialSupplierPurchaseDomainService;
import com.yhl.scp.mrp.supplier.dto.*;
import com.yhl.scp.mrp.supplier.infrastructure.dao.MaterialSupplierPurchaseDao;
import com.yhl.scp.mrp.supplier.infrastructure.dao.SupplierPurchaseRatioDao;
import com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.service.SupplierPurchaseRatioService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MrpMaterialSupplierPurchaseServiceImpl</code>
 * <p>
 * 材料采购应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 14:40:41
 */
@Slf4j
@Service
public class MaterialSupplierPurchaseServiceImpl extends AbstractService implements MaterialSupplierPurchaseService {

    @Resource
    private MaterialSupplierPurchaseDao materialSupplierPurchaseDao;

    @Resource
    private MaterialSupplierPurchaseDomainService materialSupplierPurchaseDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private SupplierPurchaseRatioDao supplierPurchaseRatioDao;

    @Resource
    private SupplierPurchaseRatioService supplierPurchaseRatioService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private SafetyInventoryService safetyInventoryService;

    @Resource
    private MaterialGrossDemandVersionService materialGrossDemandVersionService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO) {
        // 0.数据转换
        MaterialSupplierPurchaseDO materialSupplierPurchaseDO = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Do(materialSupplierPurchaseDTO);
        MaterialSupplierPurchasePO materialSupplierPurchasePO = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Po(materialSupplierPurchaseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialSupplierPurchaseDomainService.validation(materialSupplierPurchaseDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialSupplierPurchasePO);
        materialSupplierPurchaseDao.insert(materialSupplierPurchasePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO) {
        this.calculatedSpecific(Lists.newArrayList(materialSupplierPurchaseDTO));
        // 0.数据转换
        MaterialSupplierPurchaseDO materialSupplierPurchaseDO = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Do(materialSupplierPurchaseDTO);
        MaterialSupplierPurchasePO materialSupplierPurchasePO = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Po(materialSupplierPurchaseDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialSupplierPurchaseDomainService.validation(materialSupplierPurchaseDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialSupplierPurchasePO);
        materialSupplierPurchaseDao.update(materialSupplierPurchasePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialSupplierPurchaseDTO> list) {
        List<MaterialSupplierPurchasePO> newList = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialSupplierPurchaseDao.insertBatch(newList);
    }

    @Override
    public void doCreateBatchWithPrimaryKey(List<MaterialSupplierPurchaseDTO> list) {
        List<MaterialSupplierPurchasePO> newList = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialSupplierPurchaseDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialSupplierPurchaseDTO> list) {
        List<MaterialSupplierPurchasePO> newList = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialSupplierPurchaseDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialSupplierPurchaseDTO> list) {
        List<MaterialSupplierPurchasePO> newList = MaterialSupplierPurchaseConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialSupplierPurchaseDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialSupplierPurchaseDao.deleteBatch(idList);
        }
        return materialSupplierPurchaseDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialSupplierPurchaseVO selectByPrimaryKey(String id) {
        MaterialSupplierPurchasePO po = materialSupplierPurchaseDao.selectByPrimaryKey(id);
        return MaterialSupplierPurchaseConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MRP_MATERIAL_SUPPLIER_PURCHASE")
    public List<MaterialSupplierPurchaseVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "V_MRP_MATERIAL_SUPPLIER_PURCHASE_PAGE")
    public List<MaterialSupplierPurchaseVO> selectByPageNew(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByConditionNew(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MRP_MATERIAL_SUPPLIER_PURCHASE")
    public List<MaterialSupplierPurchaseVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialSupplierPurchaseVO> dataList = materialSupplierPurchaseDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialSupplierPurchaseServiceImpl target = springBeanUtils.getBean(MaterialSupplierPurchaseServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Expression(value = "V_MRP_MATERIAL_SUPPLIER_PURCHASE_PAGE")
    public List<MaterialSupplierPurchaseVO> selectByConditionNew(String sortParam, String queryCriteriaParam) {
        List<MaterialSupplierPurchaseVO> dataList = materialSupplierPurchaseDao.selectByConditionNew(sortParam, queryCriteriaParam);
        MaterialSupplierPurchaseServiceImpl target = springBeanUtils.getBean(MaterialSupplierPurchaseServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialSupplierPurchaseVO> selectByParams(Map<String, Object> params) {
        List<MaterialSupplierPurchasePO> list = materialSupplierPurchaseDao.selectByParams(params);
        return MaterialSupplierPurchaseConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialSupplierPurchaseVO> selectVOByParams(Map<String, Object> params) {
        return materialSupplierPurchaseDao.selectVOByParams(params);
    }

    @Override
    public List<MaterialSupplierPurchaseVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void export(HttpServletResponse response) {
        List<MaterialSupplierPurchasePO> materialSupplierPurchasePOS = materialSupplierPurchaseDao.selectByParams(new HashMap<>(2));
        List<MaterialSupplierPurchaseExportDTO> materialSupplierPurchaseExportDTOS = MaterialSupplierPurchaseConvertor.INSTANCE.po2ExportDtos(materialSupplierPurchasePOS);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("材料采购.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), MaterialSupplierPurchaseExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("材料采购")
                    .doWrite(materialSupplierPurchaseExportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 根据库存点编码查询
     *
     * @param stockPointCodeList
     * @return
     */
    @Override
    public List<MaterialSupplierPurchaseVO> selectMaterialSupplierPurchaseVOByStockPointCode(List<String> stockPointCodeList) {

        return materialSupplierPurchaseDao.selectMaterialSupplierPurchaseVOByStockPointCode(stockPointCodeList);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_SUPPLIER_PURCHASE.getCode();
    }

    @Override
    public List<MaterialSupplierPurchaseVO> invocation(List<MaterialSupplierPurchaseVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }

        // 根据ID查询比例数据
        List<String> supplierPurchaseRatioIdList = dataList.stream().map(MaterialSupplierPurchaseVO::getSupplierPurchaseRatioId).collect(Collectors.toList());
        List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioService.selectVOByParams(ImmutableMap.of("ids", supplierPurchaseRatioIdList));
        Map<String, SupplierPurchaseRatioVO> supplierPurchaseRatioVOMap = supplierPurchaseRatioVOList.stream().collect(Collectors.toMap(SupplierPurchaseRatioVO::getId, Function.identity(), (k1, k2) -> k2));
        for (MaterialSupplierPurchaseVO materialSupplierPurchaseVO : dataList) {
            if (supplierPurchaseRatioVOMap.containsKey(materialSupplierPurchaseVO.getSupplierPurchaseRatioId())) {
                materialSupplierPurchaseVO.setConsignment(supplierPurchaseRatioVOMap.get(materialSupplierPurchaseVO.getSupplierPurchaseRatioId()).getConsignment());
            }
        }

        return dataList;
    }

    @Override
    public void calculatedSpecific(List<MaterialSupplierPurchaseDTO> dtoList) {
        // 查询最新的毛需求版本
        MaterialGrossDemandVersionVO materialGrossDemandVersionVO = materialGrossDemandVersionService.selectLastVersion();
        if (null == materialGrossDemandVersionVO){
            return;
        }
        log.info("计算是否专用,毛需求版本ID:{}", materialGrossDemandVersionVO.getId());
        List<String> materialCodeList = dtoList.stream()
                .map(MaterialSupplierPurchaseDTO::getMaterialCode)
                .distinct().collect(Collectors.toList());
        // 查询毛需求
        List<MaterialGrossDemandVO> materialGrossDemandVOList = materialGrossDemandService.selectByParams(
                ImmutableMap.of("materialGrossDemandVersionId", materialGrossDemandVersionVO.getId(),
                        "productCodeList", materialCodeList));
        if (CollectionUtils.isEmpty(materialGrossDemandVOList)){
            return;
        }
        log.info("计算是否专用,查询出来的毛需求数量:{}", materialGrossDemandVOList.size());
        Map<String, List<MaterialGrossDemandVO>> grossDemandGroupOfProductCode = materialGrossDemandVOList.stream()
                .collect(Collectors.groupingBy(MaterialGrossDemandVO::getProductCode));
        for (MaterialSupplierPurchaseDTO supplierPurchaseDTO : dtoList) {
            if (!grossDemandGroupOfProductCode.containsKey(supplierPurchaseDTO.getMaterialCode())){
                continue;
            }
            List<MaterialGrossDemandVO> grossDemandVOList = grossDemandGroupOfProductCode.get(supplierPurchaseDTO.getMaterialCode());
            List<String> productFactoryCodeList = grossDemandVOList.stream()
                    .map(MaterialGrossDemandVO::getProductFactoryCode).distinct()
                    .collect(Collectors.toList());
            if (productFactoryCodeList.size() == 1){
                supplierPurchaseDTO.setSpecific(YesOrNoEnum.YES.getCode());
            }else {
                supplierPurchaseDTO.setSpecific(YesOrNoEnum.NO.getCode());
            }
        }


//        if (CollectionUtils.isEmpty(dtoList)) {
//            return;
//        }
//        BaseResponse<String> mdsScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
//        BaseResponse<String> dfpScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
//        List<String> materialCodeList = dtoList.stream().map(MaterialSupplierPurchaseDTO::getMaterialCode).distinct().collect(Collectors.toList());
//
//        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(mdsScenario.getData(), materialCodeList);
//        if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
//            return;
//        }
//        Map<String, NewProductStockPointVO> newProductStockPointVOMap = newProductStockPointVOS.stream().collect(Collectors.toMap(t -> t.getStockPointCode() + "-" + t.getProductCode(), Function.identity(), (o1, o2) -> o1));
//        Map<String, String> inputProductMap = new HashMap<>();
//        List<String> inputProductIdList = new ArrayList<>();
//        for (MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO : dtoList) {
//            String key = materialSupplierPurchaseDTO.getStockPointCode() + "-" + materialSupplierPurchaseDTO.getMaterialCode();
//            NewProductStockPointVO newProductStockPointVO = newProductStockPointVOMap.get(key);
//            if (newProductStockPointVO != null) {
//                inputProductIdList.add(newProductStockPointVO.getId());
//                inputProductMap.put(key, newProductStockPointVO.getId());
//            }
//        }
//        inputProductIdList = inputProductIdList.stream().distinct().collect(Collectors.toList());
//        BomTreeNewVO bomTreeNewVO = new BomTreeNewVO();
//        bomTreeNewVO.setInputProductIdList(inputProductIdList);
//        bomTreeNewVO.setProductType("FG");//PG代表成品
//        List<BomTreeNewVO> bomTreeNewVOS = newMdsFeign.selectRoutingByInputProductIds(mdsScenario.getData(), bomTreeNewVO);
//        if (CollectionUtils.isEmpty(bomTreeNewVOS)) {
//            return;
//        }
//        Map<String, List<BomTreeNewVO>> inputBomMap = bomTreeNewVOS.stream().collect(Collectors.groupingBy(BomTreeNewVO::getInputProductId));
//        Date now = new Date();
//        String startTime = DateUtils.dateToString(now, "yyyy-MM-dd");
//        //获取发货计划数据
//        List<DeliveryPlanVO2> deliveryPlanVO2List = dfpFeign.selectVO2ByPlanPeriod(dfpScenario.getData(), null, startTime, null);
//        List<String> deliveryPlanList = deliveryPlanVO2List.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList());
//
//        //获取一致性业务预测
//        List<DeliveryPlanVO2> cleanForecastDataList = dfpFeign.selectConsistenceDemandForecastDataByPlanPeriod(dfpScenario.getData(), null, startTime, null);
//        List<String> cleanForecastList = deliveryPlanVO2List.stream().map(DeliveryPlanVO2::getProductCode).distinct().collect(Collectors.toList());
//
//        for (MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO : dtoList) {
//            String key = materialSupplierPurchaseDTO.getStockPointCode() + "-" + materialSupplierPurchaseDTO.getMaterialCode();
//            //该材料对应的物品id
//            String inputProductId = inputProductMap.get(key);
//            List<BomTreeNewVO> treeNewVOS = inputBomMap.get(inputProductId);
//            boolean flag = false;
//            if (CollectionUtils.isEmpty(treeNewVOS)) {
//                continue;
//            }
//            //该材料对应的成品
//            List<String> productCodeList = treeNewVOS.stream().map(BomTreeNewVO::getProductCode).distinct().collect(Collectors.toList());
//            List<String> vehicleModelCodeList = treeNewVOS.stream().map(BomTreeNewVO::getVehicleModelCode).distinct().collect(Collectors.toList());
//            if (vehicleModelCodeList.size() == 1) {
//                flag = true;
//            }
//
//            int i = 0;
//            int j = 0;
//            for (String productCode : productCodeList) {
//                if (deliveryPlanList.contains(productCode)) {
//                    i++;
//                }
//                if (cleanForecastList.contains(productCode)) {
//                    j++;
//                }
//            }
//
//            if (flag && (i == 1 || j == 1)) {
//                materialSupplierPurchaseDTO.setSpecific(YesOrNoEnum.YES.getCode());
//            } else if (flag || i > 1 || j >= 1) {
//                materialSupplierPurchaseDTO.setSpecific(YesOrNoEnum.NO.getCode());
//            }
//        }
    }

    @Override
    public void doUpdateBySupplier(SupplierPurchaseRatioEditDTO dto) {
        commonCheck(dto);
        //更新供应商
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        SupplierPO supplierPO = newMdsFeign.selectSupplierByPrimaryKey(scenario.getData(), dto.getSupplierId());
        if (supplierPO == null) {
            throw new BusinessException("供应商不存在");
        }
        //        supplierPO.setSupplierDeliveryDate(dto.getSupplierDeliveryDate());
        //        supplierPO.setPlanDisplayCycle(dto.getPlanDisplayCycle());
        BasePOUtils.updateFiller(supplierPO);
        newMdsFeign.supplierUpdate(scenario.getData(), supplierPO);
        Map<String, Object> params = new HashMap<>();
        params.put("supplierId", dto.getSupplierId());
        List<SupplierPurchaseRatioPO> supplierPurchaseRatioPOS = supplierPurchaseRatioDao.selectByParams(params);
        if (CollectionUtils.isNotEmpty(supplierPurchaseRatioPOS)) {
            //更新材料采购基础数据
            List<String> supplierProductIds = supplierPurchaseRatioPOS.stream().map(SupplierPurchaseRatioBasicPO::getSupplierProductId).distinct().collect(Collectors.toList());
            List<MaterialSupplierPurchasePO> materialSupplierPurchasePOS = materialSupplierPurchaseDao.selectByPrimaryKeys(supplierProductIds);
            materialSupplierPurchasePOS.forEach(
                    t -> {
                        t.setRequestCargoPlanLockDay(dto.getRequestCargoPlanLockDay());
                        t.setOrderPlacementLeadTimeDay(dto.getOrderPlacementLeadTimeDay());
                        //                        t.setPurchaseLot(dto.getPurchaseLot());
                        t.setDemandPattern(dto.getDemandPattern());
                    }
            );
            BasePOUtils.updateBatchFiller(materialSupplierPurchasePOS);
            materialSupplierPurchaseDao.updateBatch(materialSupplierPurchasePOS);
            //更新对应供应商的分配比例
            if (null != dto.getPurchaseRatio()) {
                supplierPurchaseRatioPOS.forEach(t -> t.setPurchaseRatio(dto.getPurchaseRatio()));
            }
            BasePOUtils.updateBatchFiller(supplierPurchaseRatioPOS);
            supplierPurchaseRatioDao.updateBatch(supplierPurchaseRatioPOS);

            params = new HashMap<>();
            params.put("supplierProductIds", supplierProductIds);
            List<SupplierPurchaseRatioPO> allSupplierPurchaseRatioPOS = supplierPurchaseRatioDao.selectByParams(params);

            //重新计算采购比例
            Map<String, Pair<String, Date>> createMap = allSupplierPurchaseRatioPOS.stream().collect(Collectors.toMap(SupplierPurchaseRatioPO::getId, t -> Pair.of(t.getCreator(), t.getCreateTime())));
            List<SupplierPurchaseRatioDTO> supplierPurchaseRatioDTOS = SupplierPurchaseRatioConvertor.INSTANCE.pos2Dtos(allSupplierPurchaseRatioPOS);
            supplierPurchaseRatioService.calculatedDistributionRatio(supplierPurchaseRatioDTOS, null);
            List<SupplierPurchaseRatioPO> updateList = SupplierPurchaseRatioConvertor.INSTANCE.dto2Pos(supplierPurchaseRatioDTOS);
            updateList.forEach(
                    t -> {
                        t.setCreator(createMap.get(t.getId()).getLeft());
                        t.setCreateTime(createMap.get(t.getId()).getRight());
                    }
            );
            BasePOUtils.updateBatchFiller(updateList);
            supplierPurchaseRatioDao.updateBatch(updateList);
        }
    }

    @Override
    public BaseResponse<Void> handleSupplierPurchase(List<SrmSupplierPurchase> srmSupplierPurchases) {
        if (CollectionUtils.isEmpty(srmSupplierPurchases)) {
            return BaseResponse.success();
        }
        Set<String> vendorCodes = srmSupplierPurchases.stream()
                .map(SrmSupplierPurchase::getVendorCode)
                .collect(Collectors.toSet());
        Set<String> orgIds = srmSupplierPurchases.stream()
                .map(SrmSupplierPurchase::getOrgId)
                .collect(Collectors.toSet());
        List<MaterialSupplierPurchaseDTO> insertDtoS = new ArrayList<>();
        List<MaterialSupplierPurchaseDTO> updateDtoS = new ArrayList<>();
        List<SupplierPurchaseRatioPO> arrayRatioList = new ArrayList<>();
        HashMap<String, Object> supplierMap = MapUtil.newHashMap(3);
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());

        supplierMap.put("supplierCodes", vendorCodes);
        List<SupplierVO> supplierPOS = newMdsFeign.selectSupplierByParams(scenario.getData(), supplierMap);
        HashMap<String, Object> StockPointMap = MapUtil.newHashMap(3);
        StockPointMap.put("organizeIds", orgIds);
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario.getData(), StockPointMap);
        Map<String, NewStockPointVO> newStockPointVOSMap = CollectionUtils.isEmpty(newStockPointVOS) ?
                MapUtil.newHashMap() :
                newStockPointVOS.stream().collect(
                        Collectors.toMap(NewStockPointVO::getOrganizeId, Function.identity(), (v1, v2) -> v1));
        List<MaterialSupplierPurchasePO> oldPos = materialSupplierPurchaseDao.selectByMaterialSupplierPurchaseIds(srmSupplierPurchases);
        Map<String, MaterialSupplierPurchasePO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getOrganizationId() + "_" + t.getMaterialCode(),
                        Function.identity(), (v1, v2) -> v1));
        Map<String, MaterialSupplierPurchaseDTO> oldDTOsMapMaterialCode = new HashMap<>();
        //插入子表
        List<SupplierPurchaseRatioPO> insertRatioPoS = new ArrayList<>();
        List<SupplierPurchaseRatioPO> updateRatioPoS = new ArrayList<>();
        List<SupplierPurchaseRatioPO> oldRatioPos =
                supplierPurchaseRatioDao.selectSupplierProductIdSupplierId(srmSupplierPurchases);
        Map<String, SupplierPurchaseRatioPO> oldRatioPosMap = CollectionUtils.isEmpty(oldRatioPos) ?
                MapUtil.newHashMap() :
                oldRatioPos.stream().collect(Collectors.toMap(t -> t.getSupplierId() + "_" + t.getSupplierProductId(),
                        Function.identity(), (v1, v2) -> v1));
        Map<String, SupplierPurchaseRatioPO> oldPosSupplierIdRatio = new HashMap<>();
        for (SrmSupplierPurchase srmSupplierPurchase : srmSupplierPurchases) {
            MaterialSupplierPurchaseDTO dto = new MaterialSupplierPurchaseDTO();
            SupplierPurchaseRatioPO ratioPo = new SupplierPurchaseRatioPO();
            String oldId = srmSupplierPurchase.getOrgId() + "_" + srmSupplierPurchase.getItemCode();
            if (oldPosMap.containsKey(oldId)) {
                if (!oldDTOsMapMaterialCode.containsKey(oldId)) {
                    MaterialSupplierPurchasePO oldPo = oldPosMap.get(oldId);
                    BeanUtils.copyProperties(oldPo, dto);
                    generateDTO(dto, srmSupplierPurchase, newStockPointVOSMap);
                    oldDTOsMapMaterialCode.put(oldId, dto);
                    updateDtoS.add(dto);
                }
            } else {
                if (!oldDTOsMapMaterialCode.containsKey(oldId)) {
                    dto.setId(UUIDUtil.getUUID());
                    generateDTO(dto, srmSupplierPurchase, newStockPointVOSMap);
                    oldDTOsMapMaterialCode.put(oldId, dto);
                    insertDtoS.add(dto);
                }
            }
            if ( StringUtils.isEmpty(srmSupplierPurchase.getBeginDate()) || StringUtils.isEmpty(srmSupplierPurchase.getBeginDate())) {
                continue;
            }
            generateRatioPo(oldDTOsMapMaterialCode, srmSupplierPurchase, ratioPo, oldId);
            Date now = new Date();
            boolean isBetween = !ratioPo.getEndTime().before(now);
            if (!isBetween) {
                continue;
            }
            Optional<SupplierVO> matchingSupplier = supplierPOS.stream()
                    .filter(supplier -> srmSupplierPurchase.getVendorCode().equals(supplier.getSupplierCode())
                            && srmSupplierPurchase.getOrgId().equals(supplier.getOrganizationId()))
                    .findFirst();
            if (matchingSupplier.isPresent()) {
                ratioPo.setSupplierId(matchingSupplier.get().getId());
                String id = ratioPo.getSupplierId() + "_" + ratioPo.getSupplierProductId();
                if (oldPosSupplierIdRatio.containsKey(id)) {
                    SupplierPurchaseRatioPO oldPo = oldPosSupplierIdRatio.get(id);
                    if (ratioPo.getAgreementType().equals("一揽子协议")) {
                        insertRatio(oldPo, arrayRatioList, insertRatioPoS, updateRatioPoS, oldRatioPosMap,
                                oldPosSupplierIdRatio, ratioPo, now, matchingSupplier, id);
                    }
                } else {
                    insertRatio(null, arrayRatioList, insertRatioPoS, updateRatioPoS, oldRatioPosMap,
                            oldPosSupplierIdRatio, ratioPo, now, matchingSupplier, id);
                }

            }
        }
        log.info("insertDtoS数量：{},updateDtoS数量：{},insertRatioPoS数量：{},updateRatioPoS数量：{}", insertDtoS.size(),
                updateDtoS.size(), insertRatioPoS.size(), updateRatioPoS.size());
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            this.calculatedSpecific(insertDtoS);
            doCreateBatchWithPrimaryKey(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            this.calculatedSpecific(updateDtoS);
            doUpdateBatch(updateDtoS);
        }
        if (CollectionUtils.isNotEmpty(insertRatioPoS)) {
            BasePOUtils.insertBatchFiller(insertRatioPoS);
            supplierPurchaseRatioDao.insertBatch(insertRatioPoS);
        }
        if (CollectionUtils.isNotEmpty(updateRatioPoS)) {
            BasePOUtils.updateBatchFiller(updateRatioPoS);
            supplierPurchaseRatioDao.updateBatch(updateRatioPoS);
        }
        // Executor executor = Executors.newFixedThreadPool(5); // 根据需求调整线程数
        // CompletableFuture.runAsync(() -> {
        //     try {
        //         supplierPurchasePercent(arrayRatioList);
        //     } catch (Exception e) {
        //         log.error("重新分配百分比失败", e);
        //     }
        // }, executor);
        return BaseResponse.success();
    }

    private void supplierPurchasePercent(List<SupplierPurchaseRatioPO> arrayRatioList) {
        log.info("开始重新分配百分比");
        List<SupplierPurchaseRatioPO> updateRatioPoS2 = new ArrayList<>();
        Set<String> supplierProductIds = arrayRatioList.stream()
                .map(SupplierPurchaseRatioPO::getSupplierProductId)
                .collect(Collectors.toSet());
        HashMap<String, Object> supplierProductList = MapUtil.newHashMap(3);
        supplierProductList.put("supplierProductIds", supplierProductIds);
        List<SupplierPurchaseRatioPO> supplierProductIdsPos = supplierPurchaseRatioDao.selectByParams(supplierProductList);
        Map<String, Integer> oldPosMapRatio = supplierProductIdsPos.stream()
                .collect(Collectors.groupingBy(SupplierPurchaseRatioPO::getSupplierProductId, Collectors.collectingAndThen(Collectors.counting(), Long::intValue)));
        Map<String, Integer> oldPosMapRatioSize = new HashMap<>();
        for (SupplierPurchaseRatioPO ratioPO : supplierProductIdsPos) {
            String supplierProductId = ratioPO.getSupplierProductId();
            int size = oldPosMapRatio.get(supplierProductId);
            BigDecimal one = new BigDecimal(1);
            BigDecimal sizeBD = new BigDecimal(size);
            BigDecimal ratioBD = one.divide(sizeBD, 10, RoundingMode.HALF_UP); // 设置精度为10位小数
            // 检查是否为有限小数
            boolean isDivisible = ratioBD.stripTrailingZeros().scale() <= 10; // 如果小数部分不超过10位，则认为是有限小数
            BigDecimal ratio = new BigDecimal(1).divide(new BigDecimal(size), 2, RoundingMode.HALF_UP);
            if (!isDivisible) {
                BigDecimal total = BigDecimal.ZERO;
                Integer insertSize = oldPosMapRatioSize.get(supplierProductId);
                if (insertSize == null) {
                    insertSize = 0;
                }
                if (insertSize < size) {
                    ratioPO.setPurchaseRatio(ratio);
                } else {
                    for (int i = 0; i < size - 1; i++) {
                        total = total.add(ratio);
                    }
                    BigDecimal lastRatio = new BigDecimal(1).subtract(total);
                    ratioPO.setPurchaseRatio(lastRatio);
                }
                updateRatioPoS2.add(ratioPO);
                oldPosMapRatioSize.put(supplierProductId, insertSize++);
            } else {
                ratioPO.setPurchaseRatio(ratio);
                updateRatioPoS2.add(ratioPO);
            }
        }
        if (CollectionUtils.isNotEmpty(updateRatioPoS2)) {
            BasePOUtils.updateBatchFiller(updateRatioPoS2);
            supplierPurchaseRatioDao.updateBatch(updateRatioPoS2);
        }
        log.info("结束重新分配百分比");
    }

    private void insertRatio(SupplierPurchaseRatioPO oldRatioMapPo, List<SupplierPurchaseRatioPO> arrayRatioList,
                             List<SupplierPurchaseRatioPO> insertRatioPoS, List<SupplierPurchaseRatioPO> updateRatioPoS, Map<String, SupplierPurchaseRatioPO> oldRatioPosMap, Map<String, SupplierPurchaseRatioPO> oldPosSupplierIdRatio, SupplierPurchaseRatioPO ratioPo, Date now, Optional<SupplierVO> matchingSupplier, String id) {
        if (oldRatioMapPo != null) {
            arrayRatioList.remove(oldPosSupplierIdRatio.get(id));
            insertRatioPoS.remove(oldPosSupplierIdRatio.get(id));
        }

        oldPosSupplierIdRatio.put(id, ratioPo);
        arrayRatioList.add(ratioPo);
        if (oldRatioPosMap.containsKey(id)) {

            SupplierPurchaseRatioPO oldPo = oldRatioPosMap.get(id);
            if (oldPo.getEndTime() != null && "一揽子协议".equals(oldPo.getAgreementType()) && !
                    "一揽子协议".equals(ratioPo.getAgreementType()) && !oldPo.getEndTime().before(now)) {
                arrayRatioList.remove(oldPosSupplierIdRatio.get(id));
                updateRatioPoS.remove(oldPosSupplierIdRatio.get(id));
            } else {
                ratioPo.setId(oldPo.getId());
                ratioPo.setPurchaseRatio(oldPo.getPurchaseRatio());
                ratioPo.setHistoricalPurchaseQuantity(oldPo.getHistoricalPurchaseQuantity());
                ratioPo.setConsignment(oldPo.getConsignment());
                ratioPo.setCreator(oldPo.getCreator());
                ratioPo.setCreateTime(oldPo.getCreateTime());
                ratioPo.setVersionValue(oldPo.getVersionValue());
                ratioPo.setModifier(oldPo.getModifier());
                ratioPo.setModifyTime(oldPo.getModifyTime());
                ratioPo.setEnabled(oldPo.getEnabled());
                updateRatioPoS.add(ratioPo);
            }

        } else {
            ratioPo.setPurchaseRatio(BigDecimal.ZERO);
            insertRatioPoS.add(ratioPo);
        }
    }

    private void generateRatioPo(Map<String, MaterialSupplierPurchaseDTO> oldDTOsMapMaterialCode, SrmSupplierPurchase srmSupplierPurchase, SupplierPurchaseRatioPO ratioPo, String itemCode) {
        ratioPo.setSupplierProductId(oldDTOsMapMaterialCode.get(itemCode).getId());
        ratioPo.setItemStatus(srmSupplierPurchase.getStatus());
        ratioPo.setAgreementType(srmSupplierPurchase.getAgreementType());
        Date beginDate = DateUtils.stringToDate(srmSupplierPurchase.getBeginDate(), DateUtils.COMMON_DATE_STR3);
        if (beginDate != null) {
            Calendar calendarBegin = getCalendar(beginDate);
            ratioPo.setBeginTime(calendarBegin.getTime());
        }
        Date endDate = DateUtils.stringToDate(srmSupplierPurchase.getEndDate(), DateUtils.COMMON_DATE_STR3);
        if (endDate != null) {
            Calendar calendarEnd = getCalendar(endDate);
            ratioPo.setEndTime(calendarEnd.getTime());
        }
    }


    @Override
    public BaseResponse<Void> syncSupplierPurchase(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.SRM.getCode(),
                ApiCategoryEnum.SUPPLIER_PURCHASE.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<String> doUpload(MultipartFile file) {
        List<String> errorList = new ArrayList<>();

        List<MaterialSupplierPurchaseExcelDTO> fileList = null;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(MaterialSupplierPurchaseExcelDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(fileList)) {
            throw new BusinessException("文件数据为空");
        }
        int sum = fileList.size();
        // 检查非空
        for (int i = 0; i < fileList.size(); i++) {
            MaterialSupplierPurchaseExcelDTO dto = fileList.get(i);
            int row = i + 1;
            if (StringUtils.isEmpty(dto.getStockPointCode())) errorList.add("第" + row + "行，库存点编码不能为空");
            if (StringUtils.isEmpty(dto.getMaterialCode())) errorList.add("第" + row + "行，物料编码不能为空");
        }

        // 收集库存点编码 + 物料编码
        List<String> fileListProductList = fileList.stream()
                .map(MaterialSupplierPurchaseExcelDTO::getMaterialCode).distinct()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 收集供应商
        List<String> fileListSupplierList = fileList.stream()
                .map(MaterialSupplierPurchaseExcelDTO::getSupplierCode).distinct()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 获取物料
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("stock_point_code", "product_code", "product_width"))
                .queryParam(ImmutableMap.of("productCodes", fileListProductList))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        // 根据库存点编码 + 物料编码 分组
        Map<String, NewProductStockPointVO> productStockPointMap = newProductStockPointVOList.stream()
                .collect(Collectors.toMap(data -> data.getStockPointCode() + "&" + data.getProductCode(),
                        Function.identity(), (v1, v2) -> v1));

        // 获取供应商
        List<SupplierVO> supplierVOList = newMdsFeign.selectSupplierByParams(SystemHolder.getScenario(), ImmutableMap.of("supplierCodes", fileListSupplierList));
        // 根据供应商编码 分组
        Map<String, SupplierVO> supplierMap = supplierVOList.stream().collect(Collectors.toMap(SupplierBasicVO::getSupplierCode, Function.identity(), (v1, v2) -> v1));

        // 获取安全库存
        List<SafetyInventoryVO> safetyInventoryVOList = safetyInventoryService.selectAll();
        // 根据库存点编码 + 物料编码 分组
        Map<String, SafetyInventoryVO> stringSafetyInventoryVOMap = safetyInventoryVOList.stream()
                .collect(Collectors.toMap(data -> data.getStockPointCode() + "&" + data.getMaterialCode(),
                        Function.identity(), (v1, v2) -> v1));

        // 检查关联和非空
        Iterator<MaterialSupplierPurchaseExcelDTO> iterator = fileList.iterator();
        int row = 1;
        while (iterator.hasNext()) {
            MaterialSupplierPurchaseExcelDTO dto = iterator.next();
            boolean status = true;
            if (StringUtils.isEmpty(dto.getStockPointCode())) {
                status = false;
                errorList.add("第" + row + "行，库存点编码不能为空");
            }
            if (StringUtils.isEmpty(dto.getMaterialCode())) {
                status = false;
                errorList.add("第" + row + "行，物料编码不能为空");
            }
            if (StringUtils.isNotEmpty(dto.getStockPointCode()) &&
                    StringUtils.isNotEmpty(dto.getMaterialCode()) &&
                    !productStockPointMap.containsKey(dto.getStockPointCode() + "&" + dto.getMaterialCode())) {
                status = false;
                errorList.add("第" + row + "行库存点编码" + dto.getStockPointCode() + "物料编码" + dto.getMaterialCode() + "未在物料基础数据维护");
            }
            if (StringUtils.isEmpty(dto.getSupplierCode())) {
                status = false;
                errorList.add("第" + row + "行，供应商编码不能为空");
            }
            if (StringUtils.isNotEmpty(dto.getSupplierCode()) && !supplierMap.containsKey(dto.getSupplierCode())) {
                status = false;
                errorList.add("第" + row + "行，供应商编码未在供应商主数据维护");
            }
            if (!status) {
                iterator.remove();
            }
            row++;
        }

        // 映射枚举
        fileList.forEach(data -> {
            if (StringUtils.isNotEmpty(data.getSpecific()) && data.getSpecific().equals(YesOrNoEnum.YES.getDesc())) {
                data.setSpecific(YesOrNoEnum.YES.getCode());
            } else if (StringUtils.isNotEmpty(data.getSpecific()) && data.getSpecific().equals(YesOrNoEnum.NO.getDesc())) {
                data.setSpecific(YesOrNoEnum.NO.getCode());
            }

            if (StringUtils.isNotEmpty(data.getConsignment()) && data.getConsignment().equals(YesOrNoEnum.YES.getDesc())) {
                data.setConsignment(YesOrNoEnum.YES.getCode());
            } else if (StringUtils.isNotEmpty(data.getConsignment()) && data.getConsignment().equals(YesOrNoEnum.NO.getDesc())) {
                data.setConsignment(YesOrNoEnum.NO.getCode());
            }

            if (StringUtils.isNotEmpty(data.getEnabled()) && data.getEnabled().equals(YesOrNoEnum.YES.getDesc())) {
                data.setEnabled(YesOrNoEnum.YES.getCode());
            } else if (StringUtils.isNotEmpty(data.getEnabled()) && data.getEnabled().equals(YesOrNoEnum.NO.getDesc())) {
                data.setEnabled(YesOrNoEnum.NO.getCode());
            }
        });

        // 获取当前材料与供应商关系
        List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOList = this.selectVOByParams(new HashMap<>());
        // 主表根据库存点编码 + 物料编码 + 供应商 分组
        Map<String, MaterialSupplierPurchaseVO> materialSupplierPurchaseVOMap = materialSupplierPurchaseVOList.stream()
                .collect(Collectors.toMap(data -> String.join("&", data.getStockPointCode(), data.getMaterialCode(),data.getSupplierCode()),
                        Function.identity(), (v1, v2) -> v1));

        // 查询采购比例数据
        List<SupplierPurchaseRatioVO> purchaseRatioVOList = supplierPurchaseRatioService.selectVOByParams(new HashMap<>());
        // 按照supplier_product_id + 供应商代码分组
        Map<String, SupplierPurchaseRatioVO> supplierPurchaseRatioVOMap = purchaseRatioVOList.stream()
                .collect(Collectors.toMap(data -> String.join("&", data.getSupplierProductId(), data.getSupplierCode()),
                        Function.identity(), (v1, v2) -> v1));


        // 材料采购
        List<MaterialSupplierPurchaseDTO> materialSupplierPurchaseDTOAddList = new ArrayList<>();
        List<MaterialSupplierPurchaseDTO> materialSupplierPurchaseDTOUpdateList = new ArrayList<>();

        // 采购比例
        List<SupplierPurchaseRatioDTO> supplierPurchaseRatioDTOAddList = new ArrayList<>();
        List<SupplierPurchaseRatioDTO> supplierPurchaseRatioDTOUpdateList = new ArrayList<>();

        // 安全库存
        List<SafetyInventoryDTO> supplierInventoryDTOAddList = new ArrayList<>();
        List<SafetyInventoryDTO> supplierInventoryDTOUpdateList = new ArrayList<>();

        for (MaterialSupplierPurchaseExcelDTO dto : fileList) {
            MaterialSupplierPurchaseDTO materialSupplierPurchaseDTO = new MaterialSupplierPurchaseDTO();
            SupplierPurchaseRatioDTO supplierPurchaseRatioDTO = new SupplierPurchaseRatioDTO();
            SafetyInventoryDTO safetyInventoryDTO = new SafetyInventoryDTO();

            // 根据物料 + 库存点 + 供应商 分组
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = materialSupplierPurchaseVOMap
                    .get(String.join("&", dto.getStockPointCode(), dto.getMaterialCode(), dto.getSupplierCode()));

            if (null == materialSupplierPurchaseVO) {
                BeanUtils.copyProperties(dto, materialSupplierPurchaseDTO);
                String id = UUID.randomUUID().toString();
                materialSupplierPurchaseDTO.setId(id);
                // 要货模式转换枚举
                materialSupplierPurchaseDTO.setDemandPattern(DemandPatternEnum.getCodeByDesc(materialSupplierPurchaseDTO.getDemandPattern()));
                // 添加材料采购
                materialSupplierPurchaseDTOAddList.add(materialSupplierPurchaseDTO);

                // 获取供应商
                SupplierVO supplierVO = supplierMap.get(dto.getSupplierCode());
                if (null != supplierVO) {
                    BeanUtils.copyProperties(dto, supplierPurchaseRatioDTO);
                    supplierPurchaseRatioDTO.setSupplierProductId(id);
                    supplierPurchaseRatioDTO.setSupplierId(supplierVO.getId());
                    // 修改采购比例
                    supplierPurchaseRatioDTO.setPurchaseRatio(new BigDecimal(dto.getPurchaseRatio()));
                    // 添加新的材料采购等于创建新的材料采购比例
                    supplierPurchaseRatioDTOAddList.add(supplierPurchaseRatioDTO);
                }

                // 判断是否存在安全库存
                SafetyInventoryVO safetyInventoryVO = stringSafetyInventoryVOMap.get(dto.getStockPointCode() + "&" + dto.getMaterialCode());
                if (null == safetyInventoryVO) {
                    BeanUtils.copyProperties(dto, safetyInventoryDTO);
                    supplierInventoryDTOAddList.add(safetyInventoryDTO);
                } else {
                    BeanUtils.copyProperties(dto, safetyInventoryDTO);
                    safetyInventoryDTO.setId(safetyInventoryVO.getId());
                    supplierInventoryDTOUpdateList.add(safetyInventoryDTO);
                }
            } else {
                BeanUtils.copyProperties(dto, materialSupplierPurchaseDTO);
                materialSupplierPurchaseDTO.setId(materialSupplierPurchaseVO.getId());
                // 要货模式转换枚举
                materialSupplierPurchaseDTO.setDemandPattern(DemandPatternEnum.getCodeByDesc(materialSupplierPurchaseDTO.getDemandPattern()));
                // 修改材料采购
                materialSupplierPurchaseDTOUpdateList.add(materialSupplierPurchaseDTO);

                // 获取供应商
                SupplierVO supplierVO = supplierMap.get(dto.getSupplierCode());
                if (null == supplierVO) {
                    continue;
                }

                // 判断采购比例是否新增
                SupplierPurchaseRatioVO supplierPurchaseRatioVO = supplierPurchaseRatioVOMap.get(String.join("&", materialSupplierPurchaseDTO.getId(), dto.getSupplierCode()));
                if (null == supplierPurchaseRatioVO) {
                    BeanUtils.copyProperties(dto, supplierPurchaseRatioDTO);
                    supplierPurchaseRatioDTO.setSupplierProductId(materialSupplierPurchaseDTO.getId());
                    supplierPurchaseRatioDTO.setSupplierId(supplierVO.getId());
                    // 修改采购比例
                    supplierPurchaseRatioDTO.setPurchaseRatio(new BigDecimal(dto.getPurchaseRatio()));
                    // 添加新的材料采购等于创建新的材料采购比例
                    supplierPurchaseRatioDTOAddList.add(supplierPurchaseRatioDTO);
                } else {
                    BeanUtils.copyProperties(dto, supplierPurchaseRatioDTO);
                    supplierPurchaseRatioDTO.setId(supplierPurchaseRatioVO.getId());
                    supplierPurchaseRatioDTO.setSupplierProductId(materialSupplierPurchaseDTO.getId());
                    supplierPurchaseRatioDTO.setSupplierId(supplierVO.getId());
                    // 修改采购比例
                    supplierPurchaseRatioDTO.setPurchaseRatio(new BigDecimal(dto.getPurchaseRatio()));
                    // 修改材料采购等于修改材料采购比例
                    supplierPurchaseRatioDTOUpdateList.add(supplierPurchaseRatioDTO);
                }

                // 判断是否存在安全库存
                SafetyInventoryVO safetyInventoryVO = stringSafetyInventoryVOMap.get(dto.getStockPointCode() + "&" + dto.getMaterialCode());
                if (null == safetyInventoryVO) {
                    BeanUtils.copyProperties(dto, safetyInventoryDTO);
                    supplierInventoryDTOAddList.add(safetyInventoryDTO);
                } else {
                    BeanUtils.copyProperties(dto, safetyInventoryDTO);
                    safetyInventoryDTO.setId(safetyInventoryVO.getId());
                    supplierInventoryDTOUpdateList.add(safetyInventoryDTO);
                }
            }
        }

        // 添加材料采购（已有id）
        if (CollectionUtils.isNotEmpty(materialSupplierPurchaseDTOAddList)) {
            // 计算是否专用
            this.calculatedSpecific(materialSupplierPurchaseDTOAddList);
            Lists.partition(materialSupplierPurchaseDTOAddList, 500).forEach(this::doCreateBatchWithPrimaryKey);
        }

        // 修改材料采购（固定字段）
        if (CollectionUtils.isNotEmpty(materialSupplierPurchaseDTOUpdateList)) {
            // 计算是否专用
            this.calculatedSpecific(materialSupplierPurchaseDTOAddList);
            Lists.partition(materialSupplierPurchaseDTOUpdateList, 500).forEach(this::doUpdateBatchSelective);
        }

        // 添加采购比例
        if (CollectionUtils.isNotEmpty(supplierPurchaseRatioDTOAddList)) {
            Lists.partition(supplierPurchaseRatioDTOAddList, 500).forEach(supplierPurchaseRatioService::doCreateBatch);
        }

        // 修改采购比例（固定字段）
        if (CollectionUtils.isNotEmpty(supplierPurchaseRatioDTOUpdateList)) {
            Lists.partition(supplierPurchaseRatioDTOUpdateList, 500).forEach(supplierPurchaseRatioService::doUpdateBatchSelective);
        }

        // 添加安全库存
        if (CollectionUtils.isNotEmpty(supplierInventoryDTOAddList)) {
            Lists.partition(supplierInventoryDTOAddList, 500).forEach(safetyInventoryService::doCreateBatch);
        }

        // 修改安全库存（固定字段）
        if (CollectionUtils.isNotEmpty(supplierInventoryDTOUpdateList)) {
            Lists.partition(supplierInventoryDTOUpdateList, 500).forEach(safetyInventoryService::doUpdateBatchSelective);
        }

        int successCount = sum - errorList.size();
        int failureCount = errorList.size();
        String failureReason = "无";
        if (CollectionUtils.isNotEmpty(errorList)) {
            failureReason = String.join(",", errorList);
        }
        String message = String.format("总数 %d 条 ，成功 %d 条，失败 %d 条，失败原因：%s", sum, successCount, failureCount, failureReason);
        return BaseResponse.success("导入成功", message);
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {
        BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(mdsScenario.getData(), "PURCHASE_ORGANIZATION", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "材料与供应商关系模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("组织"));
        headers.add(Collections.singletonList("库存点说明"));
        headers.add(Collections.singletonList("物料"));
        headers.add(Collections.singletonList("物料名称"));
        headers.add(Collections.singletonList("物料类型"));
        headers.add(Collections.singletonList("计划员"));
        headers.add(Collections.singletonList("最小起订量"));
        headers.add(Collections.singletonList("包装批量"));
        headers.add(Collections.singletonList("是否专用"));
        headers.add(Collections.singletonList("要货计划锁定期(天)"));
        headers.add(Collections.singletonList("订单下达提前期(天)"));
        headers.add(Collections.singletonList("客户零件号"));
        headers.add(Collections.singletonList("要货模式"));
        headers.add(Collections.singletonList("采购比例"));
        headers.add(Collections.singletonList("供应商编码"));
        headers.add(Collections.singletonList("最小安全库存天数"));
        headers.add(Collections.singletonList("标准安全库存天数"));
        headers.add(Collections.singletonList("最大安全库存天数"));
        headers.add(Collections.singletonList("是否寄售"));
        headers.add(Collections.singletonList("是否启用"));


        // 准备示例数据
        List<List<Object>> exampleData = new ArrayList<>();
        // 第一行示例数据，与表头一一对应
        List<Object> exampleRow = new ArrayList<>();
        exampleRow.add(rangeData);
        exampleRow.add(rangeData);
        exampleRow.add("TEST");
        exampleRow.add("TEST");
        exampleRow.add(null);
        exampleRow.add(null);
        exampleRow.add(BigDecimal.ZERO);
        exampleRow.add(BigDecimal.ZERO);
        exampleRow.add(YesOrNoEnum.YES.getDesc());
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(null);
        exampleRow.add("三填一 (采购PO模式/要货模式/自给件)");
        exampleRow.add(BigDecimal.ONE.toString());
        exampleRow.add("TEST");
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(BigDecimal.ONE);
        exampleRow.add(YesOrNoEnum.NO.getDesc());
        exampleRow.add(YesOrNoEnum.YES.getDesc());
        exampleData.add(exampleRow);

        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(exampleData);  // 传入示例数据列表
    }

    /**
     * 修改方法公共参数校验
     *
     * @param dto
     */
    private void commonCheck(SupplierPurchaseRatioEditDTO dto) {
        if (dto == null) {
            throw new BusinessException("参数为空");
        }
        if (StringUtils.isEmpty(dto.getSupplierId())) {
            throw new BusinessException("供应商id不能为空");
        }
        // if (dto.getSupplierDeliveryDate() == null){
        //     throw new BusinessException("供应商可配送天数不能为空");
        // }
        // if (dto.getPurchaseRatio() == null){
        //     throw new BusinessException("分配比例不能为空");
        // }
        // if (dto.getPlanDisplayCycle() == null){
        //     throw new BusinessException("供应商要货计划展示周期不能为空");
        // }
        if (dto.getDemandPattern() == null) {
            throw new BusinessException("要货模式不能为空");
        }
        if (StringUtils.equals(dto.getDemandPattern(), DemandPatternEnum.PLAN_NEED.getCode())) {
            if (dto.getRequestCargoPlanLockDay() == null) {
                throw new BusinessException("要货计划锁定期不能为空");
            }
        }
        if (StringUtils.equals(dto.getDemandPattern(), DemandPatternEnum.PO.getCode())) {
            if (dto.getOrderPlacementLeadTimeDay() == null) {
                throw new BusinessException("订单下达提前期不能为空");
            }
        }

        //        if (dto.getPurchaseLot() == null) {
        //            throw new BusinessException("采购批量不能为空");
        //        }
        // dto.setPurchaseRatio(dto.getPurchaseRatio().divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
    }

    private void generateDTO(MaterialSupplierPurchaseDTO dto, SrmSupplierPurchase srmSupplierPurchase,  Map<String, NewStockPointVO> newStockPointVOSMap) {
        dto.setMaterialName(srmSupplierPurchase.getItemName());
        dto.setMaterialCode(srmSupplierPurchase.getItemCode());
        dto.setOrganizationId(srmSupplierPurchase.getOrgId());
        dto.setRecId(srmSupplierPurchase.getRecId());
        String orgId = srmSupplierPurchase.getOrgId();
        if (newStockPointVOSMap.containsKey(orgId)) {
            NewStockPointVO newProductStockPointVO = newStockPointVOSMap.get(orgId);
            dto.setStockPointCode(newProductStockPointVO.getStockPointCode());
            dto.setStockPointName(newProductStockPointVO.getStockPointName());
        }


    }

    private Calendar getCalendar(Date beginDate) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    /**
     * 数量转换为百分比字符串（1 为 100%）
     *
     * @param numberStr 待转换的数值字符串
     * @return 转换后的百分比字符串
     */
    public static String convertStringToPercentage(String numberStr) {
        if (numberStr == null || numberStr.isEmpty()) {
            throw new IllegalArgumentException("输入的字符串不能为null或空");
        }
        try {
            // 将字符串转换为BigDecimal
            BigDecimal number = new BigDecimal(numberStr);
            // 乘以100并转换为百分比字符串
            return number.multiply(new BigDecimal(100))
                    .setScale(0, RoundingMode.HALF_UP)
                    .toString() + "%";
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("输入的字符串必须是有效的数字格式", e);
        }
    }

    @Override
    public List<MaterialSupplierPurchaseVO> pageCustom(MaterialSupplierPurchaseParamDTO paramDTO) {
//        Map<String, Object> params = assembleParamMap(paramDTO);
        PageHelper.startPage(paramDTO.getPageNum(), paramDTO.getPageSize());
        return this.selectByCondition(FormatConversionUtils.getSortParam(paramDTO.getSortParam()), FormatConversionUtils.getQueryCriteriaParam(paramDTO.getQueryCriteriaParam()));
    }

//    private Map<String, Object> assembleParamMap(MaterialSupplierPurchaseParamDTO paramDTO) {
//        Map<String, Object> params = new HashMap<>();
//        params.put("stockPointCode", paramDTO.getStockPointCode());
//        params.put("materialCode", paramDTO.getMaterialCode());
//        params.put("materialName", paramDTO.getMaterialName());
//        params.put("supplierId", paramDTO.getSupplierId());
//        params.put("supplierCode", paramDTO.getSupplierCode());
//        params.put("supplierName", paramDTO.getSupplierName());
//        params.put("demandPattern", paramDTO.getDemandPattern());
//        params.put("planner", paramDTO.getPlanner());
//        params.put("partNumber", paramDTO.getPartNumber());
//        return params;
//    }
}
