package com.yhl.scp.mrp.originalFilm.service.impl;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.StopWatch;
import com.alibaba.excel.EasyExcelFactory;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.*;
import com.yhl.scp.biz.common.excel.CustomColumnWidthHandler;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao.MrpNewProductStockPointDao;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.material.plan.enums.MrpStockPointTypeEnum;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.service.GlassPurchasePlanDataService;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.materialDemand.service.MaterialGrossDemandService;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomDao;
import com.yhl.scp.mrp.mrpProductBom.infrastructure.dao.MrpProductBomVersionDao;
import com.yhl.scp.mrp.originalFilm.dto.*;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultVersionService;
import com.yhl.scp.mrp.originalFilm.vo.GlassPurchasePlanVO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultVersionVO;
import com.yhl.scp.mrp.utils.EasyExcelUtil;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.enums.OriginalFilmDemandSourceTypeEnum;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanInventoryShiftService;
import com.yhl.scp.mrp.originalFilm.convertor.OriginalFilmDemandConsultSummaryConvertor;
import com.yhl.scp.mrp.originalFilm.domain.entity.OriginalFilmDemandConsultSummaryDO;
import com.yhl.scp.mrp.originalFilm.domain.service.OriginalFilmDemandConsultSummaryDomainService;
import com.yhl.scp.mrp.originalFilm.infrastructure.dao.OriginalFilmDemandConsultSummaryDao;
import com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultSummaryPO;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultDetailService;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultSummaryService;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultDetailVO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultSummaryVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

/**
 * <code>OriginalFilmDemandConsultSummaryServiceImpl</code>
 * <p>
 * 原片需求征询汇总应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 09:27:49
 */
@Slf4j
@Service
public class OriginalFilmDemandConsultSummaryServiceImpl extends AbstractService implements OriginalFilmDemandConsultSummaryService {

    private final String EXPORTTYPE_CURRENT = "current";

    @Resource
    private OriginalFilmDemandConsultSummaryDao originalFilmDemandConsultSummaryDao;

    @Resource
    private OriginalFilmDemandConsultSummaryDomainService originalFilmDemandConsultSummaryDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private OriginalFilmDemandConsultDetailService originalFilmDemandConsultDetailService;

    @Resource
    private MaterialPlanInventoryShiftService materialPlanInventoryShiftService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private OriginalFilmDemandConsultVersionService originalFilmDemandConsultVersionService;

    @Resource
    private GlassInventoryShiftDataService glassInventoryShiftDataService;

    @Resource
    private GlassInventoryShiftDetailService glassInventoryShiftDetailService;

    @Resource
    private InventoryAlternativeRelationshipService inventoryAlternativeRelationshipService;

    @Resource
    private MaterialGrossDemandService materialGrossDemandService;

    @Resource
    private GlassPurchasePlanDataService glassPurchasePlanDataService;

    @Resource
    private MrpNewProductStockPointDao mrpNewProductStockPointDao;

    @Resource
    private MrpProductBomDao mrpProductBomDao;

    @Resource
    private MrpProductBomVersionDao mrpProductBomVersionDao;


    @Override
    public BaseResponse<Void> doCreate(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultSummaryDO originalFilmDemandConsultSummaryDO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Do(originalFilmDemandConsultSummaryDTO);
        OriginalFilmDemandConsultSummaryPO originalFilmDemandConsultSummaryPO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Po(originalFilmDemandConsultSummaryDTO);
        // 1.数据校验
        originalFilmDemandConsultSummaryDomainService.validation(originalFilmDemandConsultSummaryDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(originalFilmDemandConsultSummaryPO);
        originalFilmDemandConsultSummaryDao.insertWithPrimaryKey(originalFilmDemandConsultSummaryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultSummaryDO originalFilmDemandConsultSummaryDO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Do(originalFilmDemandConsultSummaryDTO);
        OriginalFilmDemandConsultSummaryPO originalFilmDemandConsultSummaryPO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Po(originalFilmDemandConsultSummaryDTO);
        // 1.数据校验
        originalFilmDemandConsultSummaryDomainService.validation(originalFilmDemandConsultSummaryDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(originalFilmDemandConsultSummaryPO);
        originalFilmDemandConsultSummaryDao.updateSelective(originalFilmDemandConsultSummaryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OriginalFilmDemandConsultSummaryDTO> list) {
        List<OriginalFilmDemandConsultSummaryPO> newList = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        originalFilmDemandConsultSummaryDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OriginalFilmDemandConsultSummaryDTO> list) {
        List<OriginalFilmDemandConsultSummaryPO> newList = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        originalFilmDemandConsultSummaryDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        //删除原片需求征询汇总数据
        int deleteNum = originalFilmDemandConsultSummaryDao.deleteBatch(idList);
        //删除原片需求征询汇总明细数据
        originalFilmDemandConsultDetailService.deleteByConsultSummaryIds(idList);
        //TODO 库存扣减处理逻辑
        return deleteNum;
    }

    @Override
    public OriginalFilmDemandConsultSummaryVO selectByPrimaryKey(String id) {
        OriginalFilmDemandConsultSummaryPO po = originalFilmDemandConsultSummaryDao.selectByPrimaryKey(id);
        return OriginalFilmDemandConsultSummaryConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_original_film_demand_consult_summary")
    public List<OriginalFilmDemandConsultSummaryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_original_film_demand_consult_summary")
    public List<OriginalFilmDemandConsultSummaryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OriginalFilmDemandConsultSummaryVO> dataList = originalFilmDemandConsultSummaryDao.selectByCondition(sortParam, queryCriteriaParam);
        OriginalFilmDemandConsultSummaryServiceImpl target = springBeanUtils.getBean(OriginalFilmDemandConsultSummaryServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> selectByParams(Map<String, Object> params) {
        List<OriginalFilmDemandConsultSummaryPO> list = originalFilmDemandConsultSummaryDao.selectByParams(params);
        return OriginalFilmDemandConsultSummaryConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ORIGINAL_FILM_DEMAND_CONSULT_SUMMARY.getCode();
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> invocation(List<OriginalFilmDemandConsultSummaryVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void insertWithPrimaryKey(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultSummaryPO originalFilmDemandConsultSummaryPO = OriginalFilmDemandConsultSummaryConvertor
                .INSTANCE.dto2Po(originalFilmDemandConsultSummaryDTO);
        // 2.数据持久化
        BasePOUtils.insertFiller(originalFilmDemandConsultSummaryPO);
        originalFilmDemandConsultSummaryDao.insertWithPrimaryKey(originalFilmDemandConsultSummaryPO);
    }

    @Override
    public BaseResponse<Void> doDemandCalculation(OriginalFilmDemandConsultSummaryDTO dto) {
        //数据校验
        originalFilmDemandConsultSummaryDomainService.validationForDemandCalculation(dto);
        StopWatch stopWatch = new StopWatch("原片浮法库存推移查询");
        stopWatch.start("查询计划员权限下的物料");
        // 查询用户下权限的物料
        List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                ImmutableMap.of("materialPlanner", SystemHolder.getUserId()));

        if (CollectionUtils.isEmpty(productStockPointVOList)) {
            throw new BusinessException("当前计划员无可用物料权限");
        }
        List<String> planUserProductCodeList = productStockPointVOList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        List<String> planUserProductCodeNewList = new ArrayList<>(planUserProductCodeList);
        for (String productCode : planUserProductCodeList) {
            // productCode 第三位替换为*
            planUserProductCodeNewList.add(productCode.substring(0, 2) + "*" + productCode.substring(3));
        }
        planUserProductCodeNewList = planUserProductCodeNewList.stream().distinct().collect(Collectors.toList());


        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(),
                "PURCHASE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("productColor", dto.getProductColor());
        queryMap.put("startProductThickness", dto.getStartProductThickness());
        queryMap.put("endProductThickness", dto.getEndProductThickness());

//		queryMap.put("stockPointCode", rangeData);
        //1.从库存推移中获取对应的明细数据
        List<GlassInventoryShiftDataVO> shiftList = new ArrayList<>();

        Lists.partition(planUserProductCodeNewList, 1000).forEach(productCodeList -> {
            queryMap.put("planUserProductCodeList", productCodeList);
            List<GlassInventoryShiftDataVO> glassInventoryShiftDataVOList = glassInventoryShiftDataService.selectForDemandCalculation(queryMap);
            if (CollectionUtils.isNotEmpty(glassInventoryShiftDataVOList)) {
                shiftList.addAll(glassInventoryShiftDataVOList);
            }
        });

        if (CollectionUtils.isEmpty(shiftList)) {
            throw new BusinessException("该条件下无原片需求数据可汇总!");
        }


        // 获取bomVersion
        List<ProductBomVO> productBomVOList = mrpProductBomDao.selectVOByParams(new HashMap<>());
        List<ProductBomVersionVO> productBomVersionVOList = mrpProductBomVersionDao.selectVOByParams(new HashMap<>());

//        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), new HashMap<>());
//        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), new HashMap<>());

        Map<String, GlassInventoryShiftDataVO> shiftDataMapOfId = shiftList.stream().collect(Collectors.toMap(GlassInventoryShiftDataVO::getId, Function.identity()));
        // 查询detail
        List<String> inventoryShiftDataIds = shiftList.stream().map(GlassInventoryShiftDataVO::getId).collect(Collectors.toList());
        List<GlassInventoryShiftDetailVO> glassInventoryShiftDetailVOList = glassInventoryShiftDetailService.selectByParams(
                ImmutableMap.of("inventoryShiftDataIds", inventoryShiftDataIds,
                        "startDate", dto.getStartDate(),
                        "endDate", dto.getEndDate()));
        Map<String, List<GlassInventoryShiftDetailVO>> shiftDetailGroup = glassInventoryShiftDetailVOList.stream().collect(Collectors.groupingBy(GlassInventoryShiftDetailVO::getInventoryShiftDataId));

        // 创建新的版本
        OriginalFilmDemandConsultVersionDTO originalFilmDemandConsultVersionDTO = new OriginalFilmDemandConsultVersionDTO();
        originalFilmDemandConsultVersionDTO.setId(UUID.randomUUID().toString());
        originalFilmDemandConsultVersionDTO.setVersionCode(null);
        originalFilmDemandConsultVersionDTO.setProductColor(dto.getProductColor());
        originalFilmDemandConsultVersionDTO.setProductThicknessRange(dto.getStartProductThickness() + "~" + dto.getEndProductThickness());
        originalFilmDemandConsultVersionDTO.setDemandedMonthStart(dto.getStartDate());
        originalFilmDemandConsultVersionDTO.setDemandedMonthEnd(dto.getEndDate());
        originalFilmDemandConsultVersionService.doCreate(originalFilmDemandConsultVersionDTO);


        Map<String, List<GlassInventoryShiftDataVO>> shiftMap = shiftList.stream()
                .collect(Collectors.groupingBy(e -> e.getProductColor() + "_" + e.getProductThickness()));

        //2.明细数据按照厚度，颜色，月份进行汇总统计
        List<OriginalFilmDemandConsultSummaryPO> batchAddSumaryList = new ArrayList<>();
        List<OriginalFilmDemandConsultDetailDTO> batchAddDetailList = new ArrayList<>();
        for (Entry<String, List<GlassInventoryShiftDataVO>> shiftEntry : shiftMap.entrySet()) {
            String[] splitKey = shiftEntry.getKey().split("_");
            OriginalFilmDemandConsultSummaryPO addSummary = new OriginalFilmDemandConsultSummaryPO();
            addSummary.setId(UUIDUtil.getUUID());
            addSummary.setOriginalFilmDemandConsultVersionId(originalFilmDemandConsultVersionDTO.getId());
            addSummary.setProductColor(splitKey[0]);
            addSummary.setProductThickness(new BigDecimal(splitKey[1]));
            addSummary.setDemandedQuantity(null);
            BigDecimal demandedQuantity = BigDecimal.ZERO;
            List<GlassInventoryShiftDataVO> dataVOList = shiftEntry.getValue();

            List<GlassInventoryShiftDetailVO> glassInventoryShiftDataVO = new ArrayList<>();
            for (GlassInventoryShiftDataVO inventoryShiftDataVO : dataVOList) {
                if (shiftDetailGroup.containsKey(inventoryShiftDataVO.getId())) {
                    glassInventoryShiftDataVO.addAll(shiftDetailGroup.get(inventoryShiftDataVO.getId()));
                }
            }

            // 按照物品和时间分组
            Map<String, List<GlassInventoryShiftDetailVO>> inventoryShiftGroup = glassInventoryShiftDataVO.stream()
                    .collect(Collectors.groupingBy(e -> e.getInventoryShiftDataId() + "_" + DateUtils.dateToString(e.getInventoryDate(), DateUtils.COMMON_DATE_STR3)));

            // 收集推移物料
//            List<String> productCodeList = inventoryShiftGroup.values().stream()
//                    .map(glassInventoryShiftDetailVOS -> glassInventoryShiftDetailVOS.get(0))
//                    .map(materialPlanInventoryShiftVO -> shiftDataMapOfId.get(materialPlanInventoryShiftVO.getInventoryShiftDataId()))
//                    .map(GlassInventoryShiftDataVO::getProductCode)
//                    .collect(Collectors.toList());
            // 获取物料对应成品和毛坯
//            Map<String, NewProductStockPointVO> blankProductMap = new HashMap<>();
//            Map<String, NewProductStockPointVO> finishedProductMap = new HashMap<>();
//            inventoryAlternativeRelationshipService.getBlankAndFinishedProduct(productCodeList, blankProductMap, finishedProductMap);

            for (Entry<String, List<GlassInventoryShiftDetailVO>> entry : inventoryShiftGroup.entrySet()) {
                List<GlassInventoryShiftDetailVO> materialPlanInventoryShiftVOList = entry.getValue();
                GlassInventoryShiftDetailVO materialPlanInventoryShiftVO = materialPlanInventoryShiftVOList.get(0);
                GlassInventoryShiftDataVO shiftDataVO = shiftDataMapOfId.get(materialPlanInventoryShiftVO.getInventoryShiftDataId());

                // BC 本厂库存
                // 过滤除本厂库存的
                List<GlassInventoryShiftDetailVO> bcShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.BC.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());
                BigDecimal bcOpeningInventorySum = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(bcShiftVoList)) {
                    bcOpeningInventorySum = bcShiftVoList.stream()
                            .map(GlassInventoryShiftDetailVO::getOpeningInventory)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // BCMT
                List<GlassInventoryShiftDetailVO> bcmtShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.BCMT.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());

                // MT 码头   码头库存
                List<GlassInventoryShiftDetailVO> mtShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.MT.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());
                BigDecimal mtOpeningInventorySum = mtShiftVoList.stream()
                        .map(GlassInventoryShiftDetailVO::getOpeningInventory)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // FF 浮法   浮法厂库存
                List<GlassInventoryShiftDetailVO> ffShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.FF.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());
                BigDecimal ffOpeningInventorySum = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(ffShiftVoList)) {
                    ffOpeningInventorySum = ffShiftVoList.stream()
                            .map(GlassInventoryShiftDetailVO::getOpeningInventory)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // MT 原片在途 transit_quantity_from_float
                BigDecimal mtTansitQuantityFromFloatSum = mtShiftVoList.stream()
                        .map(GlassInventoryShiftDetailVO::getTransitQuantityFromPort)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                //维护对应的明细数据
                OriginalFilmDemandConsultDetailDTO detailDTO = OriginalFilmDemandConsultDetailDTO.builder()
                        .consultSummaryId(addSummary.getId())
                        .demandSourceType(OriginalFilmDemandSourceTypeEnum.INVENTORY_SHIFT_DEMAND.getCode())
                        .originalFilmProductId(shiftDataVO.getProductCode())
                        .productCode(shiftDataVO.getProductFactoryCode())
                        .vehicleModelCode(shiftDataVO.getVehicleModelCode())
                        .demandedQuantity(bcmtShiftVoList.get(0).getDemandQuantity())
                        .demandedDate(materialPlanInventoryShiftVO.getInventoryDate())
                        .floatInventoryQuantity(ffOpeningInventorySum)
                        .portRoadInventory(mtTansitQuantityFromFloatSum)
                        .portInventory(mtOpeningInventorySum)
                        .factoryInventory(bcOpeningInventorySum)
                        .standardDemandedQuantity(materialPlanInventoryShiftVO.getDemandQuantity())
                        .usedAsReplaceQuantity(materialPlanInventoryShiftVO.getUsedAsReplaceQuantity())
                        .useReplaceQuantity(materialPlanInventoryShiftVO.getUseReplaceQuantity())
                        .build();
                // 计算单片面积，原片规格长 * 宽 / 1000000
                if (null != shiftDataVO.getProductLength() &&
                        null != shiftDataVO.getProductWidth() &&
                        shiftDataVO.getProductLength().compareTo(BigDecimal.ZERO) > 0 &&
                        shiftDataVO.getProductWidth().compareTo(BigDecimal.ZERO) > 0
                ) {
                    detailDTO.setArea(shiftDataVO.getProductLength()
                            .multiply(shiftDataVO.getProductWidth())
                            .divide(new BigDecimal("1000000"), 2, RoundingMode.HALF_UP));
                }
                // 计算单片重量，面积 * 厚度 * 2.5 / 1000
                if (null != detailDTO.getArea() && null != shiftDataVO.getProductThickness()){
                    BigDecimal weight = detailDTO.getArea().multiply(shiftDataVO.getProductThickness())
                            .multiply(new BigDecimal("2.5"))
                            .divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP);
                    detailDTO.setWeight(weight);
                }

                // 总面积，单片面积 * 需求量
//                BigDecimal areaSum = BigDecimal.ZERO;
//                if (detailDTO.getArea() != null && materialPlanInventoryShiftVO.getDemandQuantity() != null) {
//                    areaSum = detailDTO.getArea().multiply(materialPlanInventoryShiftVO.getDemandQuantity());
//                }
                // 计算重量，总面积 * 厚度 * 2.5 / 1000
//                if (null != shiftDataVO.getProductThickness()) {
//                    BigDecimal weight = areaSum.multiply(shiftDataVO.getProductThickness())
//                            .multiply(new BigDecimal("2.5"))
//                            .divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP);
//                    detailDTO.setWeight(weight);
//                }

                //净需求量=毛需求+替代规格需求量-库存数据(浮法厂库存,原片在途,码头库存,本厂库存,被替代量)
                BigDecimal netDemandedQuantity = bcmtShiftVoList.get(0).getDemandQuantity();
                if (detailDTO.getUsedAsReplaceQuantity() != null) {
                    netDemandedQuantity = netDemandedQuantity.add(detailDTO.getUsedAsReplaceQuantity());
                }
                if (detailDTO.getUseReplaceQuantity() != null) {
                    netDemandedQuantity = netDemandedQuantity.subtract(detailDTO.getUseReplaceQuantity());
                }
                detailDTO.setNetDemandedQuantity(netDemandedQuantity);
                demandedQuantity = demandedQuantity.add(detailDTO.getDemandedQuantity());

                // 获取毛坯（处理*物料）
                String mpblSpecData = glassPurchasePlanDataService.getMpblSpecData(shiftDataVO.getProductCode(), productBomVersionVOList, productBomVOList);
                detailDTO.setMpblSpec(mpblSpecData);
                batchAddDetailList.add(detailDTO);
            }
            addSummary.setDemandedQuantity(demandedQuantity);
            batchAddSumaryList.add(addSummary);
        }
        if (CollUtil.isNotEmpty(batchAddSumaryList)) {
            BasePOUtils.insertBatchFiller(batchAddSumaryList);
            originalFilmDemandConsultSummaryDao.insertBatchWithPrimaryKey(batchAddSumaryList);
            originalFilmDemandConsultDetailService.doCreateBatch(batchAddDetailList);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @SneakyThrows
    @Override
    public void exportNew(HttpServletResponse response,
                          Pagination pagination,
                          String sortParam,
                          String queryCriteriaParam,
                          String exportType) {

        List<OriginalFilmDemandConsultSummaryVO> consultSummaryList;
        if (StringUtils.isNotBlank(queryCriteriaParam) && queryCriteriaParam.contains("originalFilmDemandConsultVersionId")) {
            consultSummaryList = this.selectByPage(pagination, sortParam, queryCriteriaParam);
        } else {
            String versionId = originalFilmDemandConsultVersionService.selectFinallyVersion();
            consultSummaryList = this.selectByPage(pagination, sortParam, queryCriteriaParam)
                    .stream().filter(data -> data.getOriginalFilmDemandConsultVersionId().equals(versionId))
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(consultSummaryList)) throw new BusinessException("无可用数据");

        Map<String, String> originalFilmDemandSourceTypeEnumMap = Arrays.stream(OriginalFilmDemandSourceTypeEnum.values())
                .collect(Collectors.toMap(OriginalFilmDemandSourceTypeEnum::getCode, OriginalFilmDemandSourceTypeEnum::getDesc));

        List<String> consultSummaryIds = consultSummaryList.stream().map(OriginalFilmDemandConsultSummaryVO::getId).collect(Collectors.toList());
        List<OriginalFilmDemandConsultDetailVO> detailList = originalFilmDemandConsultDetailService.selectByDemandSourceIds(consultSummaryIds);
        List<String> demandedMonthList = detailList.stream()
                .sorted(Comparator.comparing(OriginalFilmDemandConsultDetailVO::getDemandedDate))
                .map(item -> DateUtils.dateToString(item.getDemandedDate(), DateUtils.YEAR_MONTH))
                .filter(Objects::nonNull)
                .distinct().sorted().collect(Collectors.toList());
        Map<String, List<OriginalFilmDemandConsultDetailVO>> originalFilmDemandConsultDetailMap = detailList.stream()
                .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getConsultSummaryId));

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("原片需求明细");

            // 创建表头
            createSummersHeaders(workbook, sheet, demandedMonthList);

            // 创建数据行
            int startRow = 1;
            for (OriginalFilmDemandConsultSummaryVO originalFilmDemandConsultSummaryVO : consultSummaryList) {

                List<OriginalFilmDemandConsultDetailVO> originalFilmDemandConsultDetailVOS = originalFilmDemandConsultDetailMap.get(originalFilmDemandConsultSummaryVO.getId());
                originalFilmDemandConsultDetailVOS = originalFilmDemandConsultDetailVOS.stream()
                        .filter(data -> null != data.getOriginalFilmProductCode())
                        .sorted(Comparator.comparing(OriginalFilmDemandConsultDetailVO::getOriginalFilmProductCode))
                        .collect(Collectors.toList());

                Map<String, List<OriginalFilmDemandConsultDetailVO>> detailMap = originalFilmDemandConsultDetailVOS.stream()
                        .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getOriginalFilmProductCode));

                for (Entry<String, List<OriginalFilmDemandConsultDetailVO>> entry : detailMap.entrySet()) {
                    Map<String, BigDecimal> demandedDateMap = entry.getValue().stream()
                            .collect(Collectors.groupingBy(
                                    data -> DateUtils.dateToString(data.getDemandedDate(), DateUtils.YEAR_MONTH),
                                    Collectors.reducing(
                                            BigDecimal.ZERO,
                                            OriginalFilmDemandConsultDetailVO::getDemandedQuantity,
                                            BigDecimal::add)));
                    createSummersData(workbook, sheet, startRow, originalFilmDemandConsultSummaryVO, entry, demandedMonthList,
                            originalFilmDemandSourceTypeEnumMap, demandedDateMap);
                    startRow = startRow + 1;
                }
            }

            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("原片需求明细.xlsx", "UTF-8"));

            // 获取输出流并写入
            try (ServletOutputStream os = response.getOutputStream()) {
                workbook.write(os);
                // 刷新输出流
                os.flush();
            }
        }
    }

    private void createSummersHeaders(Workbook workbook,
                                      Sheet sheet,
                                      List<String> demandedMonthList) {

        // 2. 创建表头
        // 表头样式
        CellStyle headerStyle = workbook.createCellStyle();
        // 水平居中
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        // 垂直居中
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        String headerRowName01 = "需求";
        String headerRowName02 = "毛需求";
        String headerRowName03 = "替代规格需求";
        String headerRowName04 = "被替代量";
        // 列标题
        Row headerRow = sheet.createRow(0);
        List<String> headersList = new ArrayList<>();
        Collections.addAll(headersList,
                "需求来源类型", "内部车型代码", "产品编码", "原片", "原片规格", "厚度", "面积", "重量",
                "浮法厂库存", "原片在途", "码头库存", "本厂库存");
        for (String demandMonth : demandedMonthList) {
            headersList.add(demandMonth + headerRowName01);
        }
        headersList.add("总需求量");
        for (String demandMonth : demandedMonthList) {
            headersList.add(demandMonth + headerRowName02);
        }
        for (String demandMonth : demandedMonthList) {
            headersList.add(demandMonth + headerRowName03);
        }
        for (String demandMonth : demandedMonthList) {
            headersList.add(demandMonth + headerRowName04);
        }

        Collections.addAll(headersList, "未生产订单", "特殊要求", "毛坯规格");
        String[] headers = headersList.toArray(new String[0]);
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }

    private void createSummersData(Workbook workbook,
                                   Sheet sheet,
                                   int startRow,
                                   OriginalFilmDemandConsultSummaryVO summaryVO,
                                   Entry<String, List<OriginalFilmDemandConsultDetailVO>> entry,
                                   List<String> demandedMonthList,
                                   Map<String, String> sourceTypeEnumMap,
                                   Map<String, BigDecimal> demandedDateMap) {
        List<OriginalFilmDemandConsultDetailVO> value = entry.getValue();
        OriginalFilmDemandConsultDetailVO detailVO = value.get(0);
        Map<String, List<OriginalFilmDemandConsultDetailVO>> detailMap = value.stream()
                .collect(Collectors.groupingBy(data -> DateUtils.dateToString(data.getDemandedDate(), DateUtils.YEAR_MONTH)));

        // 未生产订单总和
        BigDecimal unprocessedOrderSum = value.stream()
                .map(OriginalFilmDemandConsultDetailVO::getUnprocessedOrder)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 数据样式
        CellStyle dataStyle = workbook.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        // 序号行
        Row indexRow = sheet.createRow(startRow);
        createCell(indexRow, 0, sourceTypeEnumMap.get(detailVO.getDemandSourceType()), dataStyle);
        createCell(indexRow, 1, detailVO.getVehicleModelCode(), dataStyle);
        createCell(indexRow, 2, detailVO.getProductName(), dataStyle);
        createCell(indexRow, 3, detailVO.getOriginalFilmProductCode(), dataStyle);
        createCell(indexRow, 4, detailVO.getOriginalFilmProductLength() + "*" + detailVO.getOriginalFilmProductWidth(), dataStyle);
        createCell(indexRow, 5, String.valueOf(detailVO.getOriginalFilmProductThickness()), dataStyle);
        createCell(indexRow, 6, String.valueOf(detailVO.getArea()), dataStyle);
        createCell(indexRow, 7, String.valueOf(detailVO.getWeight()), dataStyle);
        createCell(indexRow, 8, String.valueOf(detailVO.getFloatInventoryQuantity()), dataStyle);
        createCell(indexRow, 9, String.valueOf(detailVO.getPortRoadInventory()), dataStyle);
        createCell(indexRow, 10, String.valueOf(detailVO.getPortInventory()), dataStyle);
        createCell(indexRow, 11, String.valueOf(detailVO.getFactoryInventory()), dataStyle);
        int col = 12;
        BigDecimal demandSum = BigDecimal.ZERO;
        for (String demandedMonth : demandedMonthList) {
            BigDecimal demand = BigDecimal.ZERO;
            if (demandedDateMap.containsKey(demandedMonth)) {
                demand = demandedDateMap.get(demandedMonth);
            }

            if (detailMap.containsKey(demandedMonth)){
                BigDecimal unprocessedOrder = detailMap.get(demandedMonth).stream()
                        .map(OriginalFilmDemandConsultDetailVO::getUnprocessedOrder)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                demand = demand.subtract(unprocessedOrder);
                demandSum = demandSum.add(demand);
            }
            createCell(indexRow, col, String.valueOf(demand), dataStyle);
            col++;
        }

        createCell(indexRow, col, String.valueOf(demandSum), dataStyle);
        col++;

        for (String demandedMonth : demandedMonthList) {
            BigDecimal reduce = BigDecimal.ZERO;
            if (detailMap.containsKey(demandedMonth)) {
                List<OriginalFilmDemandConsultDetailVO> demandConsultDetailVOS = detailMap.get(demandedMonth);
                reduce = demandConsultDetailVOS.stream()
                        .map(OriginalFilmDemandConsultDetailVO::getStandardDemandedQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            createCell(indexRow, col, String.valueOf(reduce), dataStyle);
            col++;
        }
        for (String demandedMonth : demandedMonthList) {
            BigDecimal reduce = BigDecimal.ZERO;
            if (detailMap.containsKey(demandedMonth)) {
                List<OriginalFilmDemandConsultDetailVO> demandConsultDetailVOS = detailMap.get(demandedMonth);
                reduce = demandConsultDetailVOS.stream()
                        .map(OriginalFilmDemandConsultDetailVO::getUsedAsReplaceQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            createCell(indexRow, col, String.valueOf(reduce), dataStyle);
            col++;
        }
        for (String demandedMonth : demandedMonthList) {
            BigDecimal reduce = BigDecimal.ZERO;
            if (detailMap.containsKey(demandedMonth)) {
                List<OriginalFilmDemandConsultDetailVO> demandConsultDetailVOS = detailMap.get(demandedMonth);
                reduce = demandConsultDetailVOS.stream()
                        .map(OriginalFilmDemandConsultDetailVO::getUseReplaceQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            createCell(indexRow, col, String.valueOf(reduce), dataStyle);
            col++;
        }

        createCell(indexRow, col, String.valueOf(unprocessedOrderSum), dataStyle);
        col++;
        createCell(indexRow, col, detailVO.getSpecialRequirements(), dataStyle);
        col++;
        createCell(indexRow, col, detailVO.getMpblSpec(), dataStyle);
    }


    private static void createCell(Row row, int col, String value, CellStyle style) {
        Cell cell = row.createCell(col);
        cell.setCellValue(value);
        if (style != null) cell.setCellStyle(style);
    }

    @Override
    public void export(HttpServletResponse response, Pagination pagination, String sortParam, String queryCriteriaParam,
                       String exportType) {
        List<OriginalFilmDemandConsultSummaryVO> consultSummaryList;
        if (EXPORTTYPE_CURRENT.equals(exportType)) {
            //导出当前页面
            consultSummaryList = this.selectByPage(pagination, sortParam, queryCriteriaParam);
        } else {
            //导出所有
            // 查询用户下权限的物料

            FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("product_color"))
                    .queryParam(ImmutableMap.of("materialPlanner", SystemHolder.getUserId(),
                            "productColorIsNotNull", YesOrNoEnum.YES.getCode()))
                    .build();

            List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(),
                    feignDynamicParam);
            if (CollectionUtils.isEmpty(newProductStockPointVOList)) {
                log.info("当前用户没有权限的物料或未维护颜色");
                return;
            }
            List<String> planUserProductColorList = newProductStockPointVOList.stream()
                    .map(NewProductStockPointVO::getProductColor)
                    .distinct().collect(Collectors.toList());

            // 查询当前用户颜色版本
            Map<String, Object> versionParams = new HashMap<>(2);
            versionParams.put("productColorList", planUserProductColorList);
            List<OriginalFilmDemandConsultVersionVO> versionVOS = originalFilmDemandConsultVersionService.selectByParams(versionParams);
            if (CollectionUtils.isEmpty(versionVOS)) {
                log.info("当前用户没有维护版本");
                return;
            }

            List<String> versionIds = versionVOS.stream()
                    .collect(Collectors.groupingBy(
                            OriginalFilmDemandConsultVersionVO::getProductColor,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(OriginalFilmDemandConsultVersionVO::getCreateTime)),
                                    optional -> optional.orElse(null) // 明确处理 Optional
                            )
                    ))
                    .values().stream()
                    .filter(Objects::nonNull) // 过滤掉可能的 null 值
                    .map(OriginalFilmDemandConsultVersionVO::getId)
                    .collect(Collectors.toList());
            Map<String, Object> params = new HashMap<>();
            params.put("originalFilmDemandConsultVersionIds", versionIds);
            consultSummaryList = this.selectByParams(params);
//            consultSummaryList = this.selectByCondition(sortParam, null);
        }

        //查询对应的详情
        List<OriginalFilmDemandConsultExportDTO> exprotDtos = new ArrayList<>();
        if (CollUtil.isNotEmpty(consultSummaryList)) {
            OriginalFilmDemandSourceTypeEnum[] values = OriginalFilmDemandSourceTypeEnum.values();
            Map<String, String> originalFilmDemandSourceTypeEnumMap = Arrays.stream(values).distinct()
                    .collect(Collectors.toMap(OriginalFilmDemandSourceTypeEnum::getCode,
                            OriginalFilmDemandSourceTypeEnum::getDesc, (k1, k2) -> k2));

            List<String> consultSummaryIds = consultSummaryList.stream().map(OriginalFilmDemandConsultSummaryVO::getId).collect(Collectors.toList());
            List<OriginalFilmDemandConsultDetailVO> detailList = originalFilmDemandConsultDetailService.selectByDemandSourceIds(consultSummaryIds);
            Map<String, List<OriginalFilmDemandConsultDetailVO>> detailMap = detailList.stream()
                    .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getConsultSummaryId));
            for (int i = 0; i < consultSummaryList.size(); i++) {
                OriginalFilmDemandConsultSummaryVO consultSummary = consultSummaryList.get(i);
                OriginalFilmDemandConsultExportDTO addParent = new OriginalFilmDemandConsultExportDTO();
                addParent.setSortNo(i + 1 + "");
                addParent.setDemandedMonth(consultSummary.getDemandedMonth());
                addParent.setTotalDemandedQuantity(consultSummary.getDemandedQuantity());
                addParent.setOriginalFilmProductThickness(consultSummary.getProductThickness());
                addParent.setOriginalFilmProductColor(consultSummary.getProductColor());
                addParent.setSpecialRemark(consultSummary.getSpecialRemark());
                exprotDtos.add(addParent);
                //维护详情
                List<OriginalFilmDemandConsultDetailVO> subDetailList = detailMap.get(consultSummary.getId());
                if (CollUtil.isEmpty(subDetailList)) {
                    continue;
                }
                for (int z = 0; z < subDetailList.size(); z++) {
                    OriginalFilmDemandConsultDetailVO subDetail = subDetailList.get(z);
                    OriginalFilmDemandConsultExportDTO addSubDetail = new OriginalFilmDemandConsultExportDTO();
                    addSubDetail.setOriginalFilmProductThickness(subDetail.getOriginalFilmProductThickness());
                    addSubDetail.setOriginalFilmProductColor(subDetail.getOriginalFilmProductColor());
                    addSubDetail.setDemandSourceId(subDetail.getDemandSourceId());
                    addSubDetail.setVehicleModelCode(subDetail.getVehicleModelCode());
                    addSubDetail.setProductCode(subDetail.getProductCode());
                    addSubDetail.setProductName(subDetail.getProductName());
                    addSubDetail.setOriginalFilmProductCode(subDetail.getOriginalFilmProductCode());
                    addSubDetail.setOriginalFilmProductName(subDetail.getOriginalFilmProductName());
                    addSubDetail.setDemandedQuantity(subDetail.getDemandedQuantity());
                    addSubDetail.setFloatInventoryQuantity(subDetail.getFloatInventoryQuantity());
                    addSubDetail.setPortRoadInventory(subDetail.getPortRoadInventory());
                    addSubDetail.setPortInventory(subDetail.getPortInventory());
                    addSubDetail.setFactoryInventory(subDetail.getFactoryInventory());
                    addSubDetail.setStandardDemandedQuantity(subDetail.getStandardDemandedQuantity());
                    addSubDetail.setUsedAsReplaceQuantity(subDetail.getUsedAsReplaceQuantity());
                    addSubDetail.setUseReplaceQuantity(subDetail.getUseReplaceQuantity());
                    addSubDetail.setNetDemandedQuantity(subDetail.getNetDemandedQuantity());

                    addSubDetail.setSortNo(addParent.getSortNo() + "/" + (i + 1));
                    addSubDetail.setDemandedDate(DateUtils.dateToString(subDetail.getDemandedDate(), DateUtils.COMMON_DATE_STR3));
                    addSubDetail.setSpecialRequirements(subDetail.getSpecialRequirements());
                    addSubDetail.setDemandSourceType(EnumUtils.getDescByCode(OriginalFilmDemandSourceTypeEnum.class, subDetail.getDemandSourceType()));
                    addSubDetail.setDemandSourceType(originalFilmDemandSourceTypeEnumMap.get(subDetail.getDemandSourceType()));
                    if (subDetail.getOriginalFilmProductLength() != null && subDetail.getOriginalFilmProductWidth() != null) {
                        addSubDetail.setOriginalFilmProductSpec(subDetail.getOriginalFilmProductLength().stripTrailingZeros().toPlainString()
                                + "*" + subDetail.getOriginalFilmProductWidth().stripTrailingZeros().toPlainString());
                    }
                    exprotDtos.add(addSubDetail);
                }
            }
        }

        try (OutputStream out = new BufferedOutputStream(response.getOutputStream())) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode("原片需求征询.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            EasyExcel.write(out, OriginalFilmDemandConsultExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 关闭自动列宽
//                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25 * 256)) // 设置固定列宽
                    .sheet("原片需求征询")
                    .doWrite(exprotDtos);
        } catch (IOException e) {
            log.error("Excel导出失败", e);
            throw new BusinessException("导出失败，请稍后再试");
        }


    }

    @Override
    public List<LabelValue<String>> selectAllColor() {
        List<NewProductStockPointVO> selectAllProductStockPoint = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("productColorIsNotNull", YesOrNoEnum.YES.getCode()));
        List<String> colorList = selectAllProductStockPoint.stream().filter(e -> StringUtils.isNotEmpty(e.getProductColor()))
                .map(NewProductStockPointVO::getProductColor).distinct().collect(Collectors.toList());
        return colorList.stream()
                .map(x -> new LabelValue<>(x, x))
                .collect(Collectors.toList());
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> selectByPage2(OriginalFilmDemandConsultParam originalFilmDemandConsultParam) {
        String versionId;
        if (StringUtils.isNotBlank(originalFilmDemandConsultParam.getOriginalFilmDemandConsultVersionId())) {
            versionId = originalFilmDemandConsultParam.getOriginalFilmDemandConsultVersionId();
        } else {
           versionId =  originalFilmDemandConsultVersionService.selectFinallyVersion();
        }
        List<String> productThicknessList = new ArrayList<>();
        if (StringUtils.isNotEmpty(originalFilmDemandConsultParam.getProductThickness())) {
            productThicknessList = Arrays.stream(originalFilmDemandConsultParam.getProductThickness().split(","))
                    .collect(Collectors.toList());
        }
        Map<String, Object> params = new HashMap<>();
        params.put("originalFilmDemandConsultVersionId", versionId);
        params.put("productThicknessList", productThicknessList);
        params.put("productColor",originalFilmDemandConsultParam.getProductColor());

        PageHelper.startPage(originalFilmDemandConsultParam.getPageNum(), originalFilmDemandConsultParam.getPageSize());
        List<OriginalFilmDemandConsultSummaryVO> originalFilmDemandConsultSummaryVOS = this.selectByParams(params);
        if (CollectionUtils.isEmpty(originalFilmDemandConsultSummaryVOS)) {
            return originalFilmDemandConsultSummaryVOS;
        }
        if (StringUtils.isNotBlank(originalFilmDemandConsultParam.getOrderBy())) {
            com.yhl.platform.common.utils.CollectionUtils.sort(originalFilmDemandConsultSummaryVOS, originalFilmDemandConsultParam.getOrderBy());
        }
        // 查询原片需求汇总详情
        List<String> consultSummaryIds = originalFilmDemandConsultSummaryVOS.stream().map(OriginalFilmDemandConsultSummaryVO::getId).collect(Collectors.toList());
        List<OriginalFilmDemandConsultDetailVO> originalFilmDemandConsultDetailVOS = originalFilmDemandConsultDetailService.selectByDemandSourceIds(consultSummaryIds);
        Map<String, List<OriginalFilmDemandConsultDetailVO>> consultDetailGroupOfSummaryId = originalFilmDemandConsultDetailVOS
                .stream()
                .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getConsultSummaryId));
        for (OriginalFilmDemandConsultSummaryVO originalFilmDemandConsultSummaryVO : originalFilmDemandConsultSummaryVOS) {
            List<OriginalFilmDemandConsultDetailVO> consultDetailVOS = consultDetailGroupOfSummaryId.get(originalFilmDemandConsultSummaryVO.getId());
            if (CollectionUtils.isEmpty(consultDetailVOS)) {
                continue;
            }
            Map<String, BigDecimal> monthDemandQuantity = new HashMap<>();
            // 详情按照日期进行分组，年月
            Map<String, List<OriginalFilmDemandConsultDetailVO>> consultDetailGroupOfDate = consultDetailVOS.stream()
                    .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandedDate(), DateUtils.YEAR_MONTH)));
            BigDecimal sumUnprocessedOrder = BigDecimal.ZERO;
            for (Entry<String, List<OriginalFilmDemandConsultDetailVO>> entry : consultDetailGroupOfDate.entrySet()) {
                String month = entry.getKey();
                List<OriginalFilmDemandConsultDetailVO> value = entry.getValue();
                // 汇总value的demandedQuantity需求量
                BigDecimal sumDemandedQuantity = value.stream()
                        .map(OriginalFilmDemandConsultDetailVO::getDemandedQuantity)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                // 汇总value的unprocessedOrder需求量
                sumUnprocessedOrder = sumUnprocessedOrder.add(value.stream()
                        .map(OriginalFilmDemandConsultDetailVO::getUnprocessedOrder)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                monthDemandQuantity.put(month, sumDemandedQuantity);
            }
            originalFilmDemandConsultSummaryVO.setMonthDemandQuantity(monthDemandQuantity);
            originalFilmDemandConsultSummaryVO.setUnprocessedOrder(sumUnprocessedOrder);
        }

        List<String> demandedMonthList = originalFilmDemandConsultDetailVOS.stream()
                .map(item -> DateUtils.dateToString(item.getDemandedDate(), DateUtils.YEAR_MONTH))
                .distinct().sorted().collect(Collectors.toList());
        // 排序demandedMonthList
        originalFilmDemandConsultSummaryVOS.forEach(item -> item.setDemandedMonthList(demandedMonthList));
        return originalFilmDemandConsultSummaryVOS;
    }

    @Override
    public PageInfo<GlassPurchasePlanVO> selectByPageGlassPurchasePlan(OriginalFilmDemandConsultParam originalFilmDemandConsultParam) {

        List<GlassPurchasePlanVO> result = new ArrayList<>();

        // 原片汇总查询条件
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(originalFilmDemandConsultParam.getOriginalFilmDemandConsultVersionId())) {
            params.put("originalFilmDemandConsultVersionId", originalFilmDemandConsultParam.getOriginalFilmDemandConsultVersionId());
        } else {
            // 查询用户下权限的物料
            List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                    ImmutableMap.of("materialPlanner", SystemHolder.getUserId()));
            if (CollectionUtils.isEmpty(newProductStockPointVOList)) {
                log.error("用户{}权限下无可用物料", SystemHolder.getUserId());
                return null;
            }
            List<String> planUserProductColorList = newProductStockPointVOList.stream()
                    .map(NewProductStockPointVO::getProductColor)
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(planUserProductColorList)) {
                return null;
            }
            String productThickness = originalFilmDemandConsultParam.getProductThickness();
            List<BigDecimal> productThicknessList = new ArrayList<>();
            if (StringUtils.isNotBlank(productThickness)) {
                productThicknessList = Arrays.stream(productThickness.split(","))
                        .map(BigDecimal::new)
                        .collect(Collectors.toList());
            }

            // 查询当前用户颜色版本
            Map<String, Object> versionParams = new HashMap<>(2);
            versionParams.put("productColor", originalFilmDemandConsultParam.getProductColor());
            versionParams.put("productColorList", planUserProductColorList);
            List<OriginalFilmDemandConsultVersionVO> versionVOS = originalFilmDemandConsultVersionService.selectByParams(versionParams);
            if (CollectionUtils.isEmpty(versionVOS)) {
                return null;
            }

            List<String> versionIds = versionVOS.stream()
                    .collect(Collectors.groupingBy(
                            OriginalFilmDemandConsultVersionVO::getProductColor,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(OriginalFilmDemandConsultVersionVO::getCreateTime)),
                                    optional -> optional.orElse(null)
                            )
                    ))
                    .values().stream()
                    .filter(Objects::nonNull)
                    .map(OriginalFilmDemandConsultVersionVO::getId)
                    .collect(Collectors.toList());
            params.put("productThicknessList", productThicknessList);
            params.put("demandedMonth", originalFilmDemandConsultParam.getDemandedMonth());
            params.put("originalFilmDemandConsultVersionIds", versionIds);
        }
        List<OriginalFilmDemandConsultSummaryVO> originallySummaryVoList = this.selectByParams(params);
        List<String> originallyIdList = originallySummaryVoList.stream().map(BaseVO::getId).collect(Collectors.toList());

        PageHelper.startPage(originalFilmDemandConsultParam.getPageNum(), originalFilmDemandConsultParam.getPageSize());
        List<OriginalFilmDemandConsultSummaryVO> consultSummaryList = this.selectByParams(params);
        if (CollectionUtils.isEmpty(consultSummaryList)) throw new BusinessException("无可用数据");
        List<String> consultSummaryIds = consultSummaryList.stream().map(OriginalFilmDemandConsultSummaryVO::getId).collect(Collectors.toList());

        List<OriginalFilmDemandConsultDetailVO> detailList = originalFilmDemandConsultDetailService.selectByDemandSourceIds(consultSummaryIds);
        Map<String, List<OriginalFilmDemandConsultDetailVO>> originalFilmDemandConsultDetailMap = detailList.stream()
                .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getConsultSummaryId));
        List<String> demandedMonthList = detailList.stream()
                .sorted(Comparator.comparing(OriginalFilmDemandConsultDetailVO::getDemandedDate))
                .map(item -> DateUtils.dateToString(item.getDemandedDate(), DateUtils.YEAR_MONTH))
                .filter(Objects::nonNull)
                .distinct().sorted().collect(Collectors.toList());
        List<String> productCodeList = detailList.stream()
                .map(OriginalFilmDemandConsultDetailVO::getOriginalFilmProductCode).distinct().collect(Collectors.toList());

        List<String> productFactoryCodeList = detailList.stream()
                .filter(data -> null != data.getProductName())
                .map(data -> Arrays.stream(data.getProductName().split(",")))
                .distinct()
                .flatMap(Stream::sorted)
                .collect(Collectors.toList());

        // 根据本厂编码查询物品BOM明细
        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), new HashMap<>());
        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), new HashMap<>());
        List<String> allProductCode = new ArrayList<>();
        allProductCode.addAll(productCodeList);
        allProductCode.addAll(productFactoryCodeList);
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "loading_position", "vehicle_model_code"))
                .queryParam(ImmutableMap.of("productCodeList", allProductCode))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(),
                feignDynamicParam);
        Map<String, NewProductStockPointVO> productStockPointMap = newProductStockPointVOList.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));

        for (OriginalFilmDemandConsultSummaryVO summaryVO : consultSummaryList) {
            List<OriginalFilmDemandConsultDetailVO> demandConsultDetailVOS = originalFilmDemandConsultDetailMap.get(summaryVO.getId());
            Map<String, List<OriginalFilmDemandConsultDetailVO>> detailMap = demandConsultDetailVOS.stream()
                    .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getOriginalFilmProductCode));
            // 列汇总
            List<GlassPurchasePlanVO> columnSummaryList = new ArrayList<>();

            if (detailMap.isEmpty()) {
                GlassPurchasePlanVO glassPurchasePlanVO = new GlassPurchasePlanVO();
                glassPurchasePlanVO.setStockPointCode("SJG");
                glassPurchasePlanVO.setProductThickness(summaryVO.getProductThickness());
                glassPurchasePlanVO.setProductColor(summaryVO.getProductColor());
                result.add(glassPurchasePlanVO);
                columnSummaryList.add(glassPurchasePlanVO);
            }

            for (Entry<String, List<OriginalFilmDemandConsultDetailVO>> entry : detailMap.entrySet()) {
                String key = entry.getKey();
                List<OriginalFilmDemandConsultDetailVO> value = entry.getValue();
                OriginalFilmDemandConsultDetailVO originalFilmDemandConsultDetailVO = value.get(0);

                Map<String, List<OriginalFilmDemandConsultDetailVO>> demandDetailMap = value.stream()
                        .collect(Collectors.groupingBy(data -> DateUtils.dateToString(data.getDemandedDate(), DateUtils.YEAR_MONTH)));
                List<String> productFactoryList = value.stream()
                        .filter(data -> null != data.getProductName())
                        .map(data -> Arrays.stream(data.getProductName().split(",")))
                        .distinct()
                        .flatMap(Stream::sorted)
                        .collect(Collectors.toList());
//                List<String> targetProducts = getTargetProducts(key, grossDemandMap, productFactoryList, 3);

                GlassPurchasePlanVO glassPurchasePlanVO = new GlassPurchasePlanVO();
                glassPurchasePlanVO.setStockPointCode("SJG");
                glassPurchasePlanVO.setProductCode(key);
                glassPurchasePlanVO.setProductThickness(summaryVO.getProductThickness());
                glassPurchasePlanVO.setProductColor(summaryVO.getProductColor());

                glassPurchasePlanVO.setMpblSpec(glassPurchasePlanDataService.getMpblSpecData(key, productBomVersionVOList, productBomVOList));

                glassPurchasePlanVO.setVehicleModelCode(productFactoryList.stream().map(data -> {
                    if (productStockPointMap.containsKey(data)) {
                        NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(data);
                        return newProductStockPointVO.getVehicleModelCode();
                    }
                    return null;
                }).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")));

                glassPurchasePlanVO.setLoadingPosition(productFactoryList.stream().map(data -> {
                    if (productStockPointMap.containsKey(data)) {
                        NewProductStockPointVO newProductStockPointVO = productStockPointMap.get(data);
                        return newProductStockPointVO.getLoadingPosition();
                    }
                    return null;
                }).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")));

                glassPurchasePlanVO.setDemandedMonthList(demandedMonthList);

                Map<String, BigDecimal> monthDemandQuantity = new HashMap<>();
                for (String demandMonth : demandedMonthList) {
                    BigDecimal reduce = BigDecimal.ZERO;
                    if (demandDetailMap.containsKey(demandMonth)) {
                        reduce = demandDetailMap.get(demandMonth).stream()
                                .map(OriginalFilmDemandConsultDetailVO::getDemandedQuantity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    monthDemandQuantity.put(demandMonth, reduce);
                }
                glassPurchasePlanVO.setMonthDemandQuantity(monthDemandQuantity);

                BigDecimal reduce = monthDemandQuantity.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                glassPurchasePlanVO.setAmountArea(reduce.multiply(originalFilmDemandConsultDetailVO.getArea()));

                glassPurchasePlanVO.setWeight(originalFilmDemandConsultDetailVO.getWeight());

                if (key.contains("ABB")) {
                    glassPurchasePlanVO.setQualityGrade("汽车优级");
                } else if (key.contains("ABT")) {
                    glassPurchasePlanVO.setQualityGrade("汽车特级");
                }else if (key.contains("*")){
                    glassPurchasePlanVO.setQualityGrade("汽车优级");
                }

                glassPurchasePlanVO.setSpecificationDirection("平行于" + key.substring(3, 7));

                glassPurchasePlanVO.setProductThicknessDeviation("内控");

                glassPurchasePlanVO.setSpecialRequirements(summaryVO.getSpecialRemark());

                result.add(glassPurchasePlanVO);
                columnSummaryList.add(glassPurchasePlanVO);
            }

            // 获取第一个对象作为汇总载体
            GlassPurchasePlanVO glassPurchasePlanVO = new GlassPurchasePlanVO();
            glassPurchasePlanVO.setStockPointCode("汇总");
            glassPurchasePlanVO.setProductThickness(summaryVO.getProductThickness());
            glassPurchasePlanVO.setProductColor(summaryVO.getProductColor());

            Map<String, BigDecimal> summedDemand = columnSummaryList.stream()
                    .map(GlassPurchasePlanVO::getMonthDemandQuantity)
                    .filter(Objects::nonNull)
                    .flatMap(map -> map.entrySet().stream())
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue() != null ? entry.getValue() : BigDecimal.ZERO,
                            BigDecimal::add
                    ));
            glassPurchasePlanVO.setMonthDemandQuantity(summedDemand);

            BigDecimal sumAmountArea = columnSummaryList.stream()
                    .map(GlassPurchasePlanVO::getAmountArea).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            glassPurchasePlanVO.setAmountArea(sumAmountArea);

            BigDecimal weightArea = columnSummaryList.stream()
                    .map(GlassPurchasePlanVO::getWeight).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            glassPurchasePlanVO.setWeight(weightArea);

            result.add(glassPurchasePlanVO);
        }

        int total = originalFilmDemandConsultDetailService.selectByGroupProductTotal(originallyIdList);

        PageInfo<GlassPurchasePlanVO> pageInfo = new PageInfo<>(result);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(originalFilmDemandConsultParam.getPageNum());
        pageInfo.setPageSize(originalFilmDemandConsultParam.getPageSize());
        pageInfo.setSize(result.size());
        // 向上取整计算总页数：(总数 + 每页数量 - 1) / 每页数量
        pageInfo.setPages((originallyIdList.size() + pageInfo.getPageSize() - 1) / pageInfo.getPageSize());
        return pageInfo;
    }

    @Override
    public void doUpload(MultipartFile file, String versionId) {
        List<UnProducedOrdersExportDTO> fileList = null;
        try {
            fileList = EasyExcelFactory.read(file.getInputStream())
                    .head(UnProducedOrdersExportDTO.class)
                    .sheet()
                    .doReadSync();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (CollectionUtils.isEmpty(fileList)) {
            throw new BusinessException("文件数据为空");
        }

        Map<String, List<UnProducedOrdersExportDTO>> fileMap = fileList.stream()
                .collect(Collectors.groupingBy(UnProducedOrdersExportDTO::getProductCode));

        if (com.yhl.platform.common.utils.StringUtils.isEmpty(versionId)) {
            versionId = originalFilmDemandConsultVersionService.selectFinallyVersion();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("originalFilmDemandConsultVersionId", versionId);
        List<OriginalFilmDemandConsultSummaryVO> originalFilmDemandConsultSummaryVOS = this.selectByParams(params);

        // 查询原片需求汇总详情
        List<String> consultSummaryIds = originalFilmDemandConsultSummaryVOS.stream()
                .map(OriginalFilmDemandConsultSummaryVO::getId)
                .collect(Collectors.toList());
        List<String> productCodeList = new ArrayList<>();
        Set<String> productCodeSet = fileMap.keySet();
        for (String productCode : productCodeSet) {
            String mixProductCode = productCode.substring(0, 2) + "*" + productCode.substring(3);
            productCodeList.add(mixProductCode);
        }
        productCodeList.addAll(productCodeSet);
        productCodeList = productCodeList.stream().distinct().collect(Collectors.toList());

        List<OriginalFilmDemandConsultDetailVO> originalFilmDemandConsultDetailVOS = originalFilmDemandConsultDetailService.selectVOByParams(
                ImmutableMap.of("consultSummaryIds", consultSummaryIds, "originalFilmProductCodes", productCodeList));

        Map<String, List<OriginalFilmDemandConsultDetailVO>> originalFilmDemandConsultDetailMap = originalFilmDemandConsultDetailVOS.stream()
                .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getOriginalFilmProductCode));

        List<OriginalFilmDemandConsultDetailDTO> updateList = new ArrayList<>();
        for (Entry<String, List<OriginalFilmDemandConsultDetailVO>> detailEntity : originalFilmDemandConsultDetailMap.entrySet()) {
            String key = detailEntity.getKey();
            List<OriginalFilmDemandConsultDetailVO> value = detailEntity.getValue();
            Map<String, OriginalFilmDemandConsultDetailVO> detailMap = value.stream()
                    .collect(Collectors.toMap(data -> DateUtils.dateToString(data.getDemandedDate()), Function.identity(), (v1, v2) -> v1));

            // 找出每个月份最后的对应的日期
            Map<String, String> dataMap = value.stream()
                    // 过滤空日期
                    .filter(data -> data.getDemandedDate() != null)
                    // 转换Date为LocalDate（使用系统默认时区）
                    .map(data -> {
                        // 直接使用系统默认时区转换，不显式声明zoneId变量
                        return data.getDemandedDate()
                                .toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDate();
                    })
                    // 按年月分组
                    .collect(Collectors.groupingBy(
                            date -> date.format(DateTimeFormatter.ofPattern("yyyy-MM")),
                            // 每组取最大的日期
                            Collectors.maxBy(Comparator.comparing(LocalDate::toEpochDay))
                    ))
                    // 处理分组结果，转换回Date类型
                    .entrySet().stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> entry.getValue()
                                    .map(maxDate -> maxDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                                    .orElse(null)
                    ));
            List<UnProducedOrdersExportDTO> unProducedOrdersExportDTOS = new ArrayList<>();
            if (fileMap.containsKey(key)){
                unProducedOrdersExportDTOS = fileMap.get(key);
            }
            // key的第3个字符转置为B
            key = key.substring(0, 2) + "B" + key.substring(3);
            if (CollectionUtils.isEmpty(unProducedOrdersExportDTOS) && fileMap.containsKey(key)){
                unProducedOrdersExportDTOS = fileMap.get(key);
            }
            // key的第3个字符转置为T
            key = key.substring(0, 2) + "T" + key.substring(3);
            if (CollectionUtils.isEmpty(unProducedOrdersExportDTOS) && fileMap.containsKey(key)){
                unProducedOrdersExportDTOS = fileMap.get(key);
            }

            Map<String, List<UnProducedOrdersExportDTO>> unProducedOrdersExportMap = unProducedOrdersExportDTOS.stream()
                    .collect(Collectors.groupingBy(UnProducedOrdersExportDTO::getSpendTime));

            for (Entry<String, List<UnProducedOrdersExportDTO>> exportEntity : unProducedOrdersExportMap.entrySet()) {
                String exportEntityKey = exportEntity.getKey();
                List<UnProducedOrdersExportDTO> exportEntityValue = exportEntity.getValue();
                String date = null;

                try {
                    // 尝试解析"yyyy/MM-/dd"格式
                    LocalDate localDate = LocalDate.parse(
                            exportEntityKey,
                            DateTimeFormatter.ofPattern("yyyy/MM/dd")
                    );
                    date = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                } catch (DateTimeParseException e) {
                    // 正则匹配"YYYY年MM月"或"YYYY年MM月剩余"格式
                    Pattern pattern = Pattern.compile("(\\d{4})年(\\d{1,2})月(剩余)?");
                    Matcher matcher = pattern.matcher(exportEntityKey.trim());

                    if (matcher.matches()) {
                        int year = Integer.parseInt(matcher.group(1));
                        int month = Integer.parseInt(matcher.group(2));
                        // 转换为"yyyy-MM"格式并赋值给date
                        date = dataMap.get(LocalDate.of(year, month, 1).format(DateTimeFormatter.ofPattern("yyyy-MM")));
                    }
                }

                if (null != date) {
                    OriginalFilmDemandConsultDetailVO originalFilmDemandConsultDetailVO = detailMap.get(date);
                    if (Objects.isNull(originalFilmDemandConsultDetailVO)) continue;

                    BigDecimal reduce = exportEntityValue.stream()
                            .map(order -> order.getPerBox().multiply(order.getBox()))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    OriginalFilmDemandConsultDetailDTO dto = new OriginalFilmDemandConsultDetailDTO();
                    dto.setId(originalFilmDemandConsultDetailVO.getId());
                    dto.setUnprocessedOrder(reduce);
                    updateList.add(dto);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            // 按照ID进行分组
            Map<String, List<OriginalFilmDemandConsultDetailDTO>> detailGroup = updateList.stream()
                    .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailDTO::getId));
            List<OriginalFilmDemandConsultDetailDTO> mergeUpdateList = new ArrayList<>();
            for (Entry<String, List<OriginalFilmDemandConsultDetailDTO>> entry : detailGroup.entrySet()) {
                List<OriginalFilmDemandConsultDetailDTO> value = entry.getValue();
                BigDecimal unprocessedOrder = value.stream()
                        .map(OriginalFilmDemandConsultDetailDTO::getUnprocessedOrder)
                        .filter(Objects::nonNull) // 过滤掉null值
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                OriginalFilmDemandConsultDetailDTO mergeDto = new OriginalFilmDemandConsultDetailDTO();
                mergeDto.setId(entry.getKey());
                mergeDto.setUnprocessedOrder(unprocessedOrder);
                mergeUpdateList.add(mergeDto);
            }
            Lists.partition(mergeUpdateList, 1000).forEach(list -> originalFilmDemandConsultDetailService.doUpdateBatchSelective(list));
        }
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "未生产订单导入模板");
        List<List<String>> headers = com.google.common.collect.Lists.newArrayList();
        headers.add(Collections.singletonList("规格mm"));
        headers.add(Collections.singletonList("片/箱*"));
        headers.add(Collections.singletonList("生产箱数*"));
        headers.add(Collections.singletonList("本厂编码*"));
        headers.add(Collections.singletonList("预计生产时间*"));

        // 准备示例数据
        List<List<Object>> exampleData = new ArrayList<>();
        // 第一行示例数据，与表头一一对应
        List<Object> exampleRow = new ArrayList<>();
        exampleRow.add("0905*1425");
        exampleRow.add("245");
        exampleRow.add("96");
        exampleRow.add("ABB090514250210SG1");
        exampleRow.add("2025-08-06");
        exampleData.add(exampleRow);

        List<Object> exampleRow02 = new ArrayList<>();
        exampleRow02.add("0905*1425");
        exampleRow02.add("245");
        exampleRow02.add("96");
        exampleRow02.add("ABB090514250210SG1");
        exampleRow02.add("2025年8月剩余");
        exampleData.add(exampleRow02);

        List<Object> exampleRow03 = new ArrayList<>();
        exampleRow03.add("0905*1425");
        exampleRow03.add("245");
        exampleRow03.add("96");
        exampleRow03.add("ABB090514250210SG1");
        exampleRow03.add("2025年9月");
        exampleData.add(exampleRow03);

        EasyExcel.write(out)
                .sheet()
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(exampleData);
    }

    private BigDecimal getMpblSpecData(List<String> targetProducts,
                                       Map<String, ProductBomVO> productBomMap) {
        if (CollectionUtils.isEmpty(targetProducts)) return null;
        ProductBomVO productBomVO = productBomMap.get(targetProducts.get(0));
        if (Objects.nonNull(productBomVO)) {
            if (null != productBomVO.getProductArea()) {
                return productBomVO.getProductArea();
            } else if (null != productBomVO.getProductLength() && null != productBomVO.getProductWidth()) {
                return productBomVO.getProductLength()
                        .multiply(productBomVO.getProductWidth())
                        .divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP);
            }
        }
        return null;
    }

    private List<String> getTargetProducts(String key,
                                           Map<String, List<MaterialGrossDemandVO>> grossDemandMap,
                                           List<String> productFactoryList,
                                           int size) {
        // 确保size为正数，避免无效值
        size = Math.max(size, 1);

        List<String> targetProducts;

        // 处理*，ABB 和 ABT的毛需求都看
        List<String> productCodeList = handleSpecialProductCodeList(Collections.singletonList(key));
        List<MaterialGrossDemandVO> materialGrossDemandVOS = new ArrayList<>();
        for (String productCode : productCodeList) {
            if (grossDemandMap.containsKey(productCode)){
                // 根据物料编码获取毛需求
                materialGrossDemandVOS.addAll(grossDemandMap.get(productCode));
            }
        }

        if (CollectionUtils.isNotEmpty(materialGrossDemandVOS)) {
            Map<String, BigDecimal> productDemandMap = materialGrossDemandVOS.stream()
                    .collect(Collectors.groupingBy(
                            MaterialGrossDemandVO::getProductFactoryCode,
                            Collectors.reducing(
                                    BigDecimal.ZERO,
                                    MaterialGrossDemandVO::getDemandQuantity,
                                    BigDecimal::add
                            )
                    ));

            List<Map.Entry<String, BigDecimal>> matchedEntries = productDemandMap.entrySet()
                    .stream()
                    .filter(e -> productFactoryList.contains(e.getKey()))
                    .collect(Collectors.toList());

            if (!matchedEntries.isEmpty()) {
                // 按需求数量降序排序，取前size个产品代码
                targetProducts = matchedEntries.stream()
                        .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                        .limit(size)
                        .map(Map.Entry::getKey)
                        .collect(Collectors.toList());
            } else {
                // 无匹配时从productFactoryList取前size个
                targetProducts = productFactoryList.stream()
                        .limit(size)
                        .collect(Collectors.toList());
            }
        } else {
            // 无对应毛需求时从productFactoryList取前size个
            targetProducts = productFactoryList.stream()
                    .limit(size)
                    .collect(Collectors.toList());
        }

        return targetProducts;
    }

    public static List<String> handleSpecialProductCodeList(List<String> productCodes) {
        List<String> newProductCodes = new ArrayList<>();
        for (String productCode : productCodes) {
            if (productCode.charAt(2) == '*') {
                StringBuilder sb = new StringBuilder(productCode);
                sb.setCharAt(2, 'B');
                newProductCodes.add(sb.toString());
                sb.setCharAt(2, 'T');
                newProductCodes.add(sb.toString());
            } else {
                newProductCodes.add(productCode);
            }
        }
        return newProductCodes;
    }

    /**
     * 转换特殊日期格式为标准日期
     *
     * @param dateStr 输入的日期字符串，可以是标准日期、"YYYY年MM月"、"YYYY年MM月剩余"或"yyyy/M/d"
     * @return 转换后的标准日期字符串(yyyy-MM-dd)，如果无法转换则返回null
     */
    public static String convertSpecialDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        // 目标格式
        DateTimeFormatter targetFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 1. 先尝试解析标准日期格式(yyyy-MM-dd)
        try {
            LocalDate.parse(dateStr, targetFormatter);
            return dateStr; // 如果是标准格式，直接返回
        } catch (DateTimeParseException e) {
            // 不是标准格式，继续处理其他格式
        }

        // 2. 尝试解析"yyyy/M/d"格式(如2025/10/4)
        try {
            DateTimeFormatter slashFormatter = DateTimeFormatter.ofPattern("yyyy/M/d");
            LocalDate date = LocalDate.parse(dateStr.trim(), slashFormatter);
            return date.format(targetFormatter);
        } catch (DateTimeParseException e) {
            // 不是"yyyy/M/d"格式，继续处理其他格式
        }

        // 3. 处理"YYYY年MM月"和"YYYY年MM月剩余"格式
        Pattern pattern = Pattern.compile("(\\d{4})年(\\d{1,2})月(剩余)?");
        Matcher matcher = pattern.matcher(dateStr.trim());

        if (matcher.matches()) {
            try {
                int year = Integer.parseInt(matcher.group(1));
                int month = Integer.parseInt(matcher.group(2));

                // 验证年月的有效性
                if (month < 1 || month > 12) {
                    return null;
                }

                // 转换为当月第一天的标准日期格式
                return LocalDate.of(year, month, 1).format(targetFormatter);
            } catch (NumberFormatException | DateTimeParseException ex) {
                return null;
            }
        }

        // 无法识别的格式
        return null;
    }
}
