package com.yhl.scp.mrp.originalFilm.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultVersionVO;

/**
 * <code>OriginalFilmDemandConsultVersionDao</code>
 * <p>
 * 原片需求征询版本DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24 14:13:54
 */
public interface OriginalFilmDemandConsultVersionDao extends BaseDao<OriginalFilmDemandConsultVersionPO, OriginalFilmDemandConsultVersionVO> {

    String selectFinallyVersion();

}
