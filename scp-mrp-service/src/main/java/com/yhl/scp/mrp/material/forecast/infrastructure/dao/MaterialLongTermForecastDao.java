package com.yhl.scp.mrp.material.forecast.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.forecast.infrastructure.po.MaterialLongTermForecastPO;
import com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialLongTermForecastDao</code>
 * <p>
 * 材料长期预测DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 15:45:39
 */
public interface MaterialLongTermForecastDao extends BaseDao<MaterialLongTermForecastPO, MaterialLongTermForecastVO> {

    void deleteByProductCodeList(@Param("list") List<String> productCodeList);

    List<MaterialLongTermForecastVO> selectGroupByParams(@Param("params") Map<String, Object> params);

    @Select("SELECT DISTINCT DATE_FORMAT(demand_date, '%Y-%m') AS demandDateStr " +
            "FROM mrp_material_long_term_forecast " +
            "WHERE demand_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')")
    List<String> selectDistinctDemandDateStr();

}
