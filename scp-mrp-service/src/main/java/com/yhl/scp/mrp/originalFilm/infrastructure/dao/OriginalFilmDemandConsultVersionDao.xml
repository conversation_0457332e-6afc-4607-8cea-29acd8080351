<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.originalFilm.infrastructure.dao.OriginalFilmDemandConsultVersionDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO">
        <!--@Table mrp_original_film_demand_consult_version-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="product_thickness_range" jdbcType="VARCHAR" property="productThicknessRange"/>
        <result column="demanded_month_start" jdbcType="VARCHAR" property="demandedMonthStart"/>
        <result column="demanded_month_end" jdbcType="VARCHAR" property="demandedMonthEnd"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultVersionVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,version_code,product_color,product_thickness_range,demanded_month_start,demanded_month_end,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productColor != null and params.productColor != ''">
                and product_color = #{params.productColor,jdbcType=VARCHAR}
            </if>
            <if test="params.productThicknessRange != null and params.productThicknessRange != ''">
                and product_thickness_range = #{params.productThicknessRange,jdbcType=VARCHAR}
            </if>
            <if test="params.demandedMonthStart != null and params.demandedMonthStart != ''">
                and demanded_month_start = #{params.demandedMonthStart,jdbcType=VARCHAR}
            </if>
            <if test="params.demandedMonthEnd != null and params.demandedMonthEnd != ''">
                and demanded_month_end = #{params.demandedMonthEnd,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productColorList != null and params.productColorList.size() > 0">
                and product_color in
                <foreach collection="params.productColorList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_original_film_demand_consult_version
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_original_film_demand_consult_version
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_original_film_demand_consult_version
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_original_film_demand_consult_version
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectFinallyVersion" resultType="java.lang.String">
        select
        id
        from mrp_original_film_demand_consult_version
        order by create_time desc limit 1
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_original_film_demand_consult_version(
        id,
        version_code,
        product_color,
        product_thickness_range,
        demanded_month_start,
        demanded_month_end,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionCode,jdbcType=VARCHAR},
        #{productColor,jdbcType=VARCHAR},
        #{productThicknessRange,jdbcType=VARCHAR},
        #{demandedMonthStart,jdbcType=VARCHAR},
        #{demandedMonthEnd,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO">
        insert into mrp_original_film_demand_consult_version(id,
                                                             version_code,
                                                             product_color,
                                                             product_thickness_range,
                                                             demanded_month_start,
                                                             demanded_month_end,
                                                             remark,
                                                             enabled,
                                                             creator,
                                                             create_time,
                                                             modifier,
                                                             modify_time,
                                                             version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionCode,jdbcType=VARCHAR},
                #{productColor,jdbcType=VARCHAR},
                #{productThicknessRange,jdbcType=VARCHAR},
                #{demandedMonthStart,jdbcType=VARCHAR},
                #{demandedMonthEnd,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_original_film_demand_consult_version(
        id,
        version_code,
        product_color,
        product_thickness_range,
        demanded_month_start,
        demanded_month_end,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionCode,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productThicknessRange,jdbcType=VARCHAR},
            #{entity.demandedMonthStart,jdbcType=VARCHAR},
            #{entity.demandedMonthEnd,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_original_film_demand_consult_version(
        id,
        version_code,
        product_color,
        product_thickness_range,
        demanded_month_start,
        demanded_month_end,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionCode,jdbcType=VARCHAR},
            #{entity.productColor,jdbcType=VARCHAR},
            #{entity.productThicknessRange,jdbcType=VARCHAR},
            #{entity.demandedMonthStart,jdbcType=VARCHAR},
            #{entity.demandedMonthEnd,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO">
        update mrp_original_film_demand_consult_version
        set version_code            = #{versionCode,jdbcType=VARCHAR},
            product_color           = #{productColor,jdbcType=VARCHAR},
            product_thickness_range = #{productThicknessRange,jdbcType=VARCHAR},
            demanded_month_start    = #{demandedMonthStart,jdbcType=VARCHAR},
            demanded_month_end      = #{demandedMonthEnd,jdbcType=VARCHAR},
            remark                  = #{remark,jdbcType=VARCHAR},
            enabled                 = #{enabled,jdbcType=VARCHAR},
            modifier                = #{modifier,jdbcType=VARCHAR},
            modify_time             = #{modifyTime,jdbcType=TIMESTAMP},
            version_value           = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO">
        update mrp_original_film_demand_consult_version
        <set>
            <if test="item.versionCode != null and item.versionCode != ''">
                version_code = #{item.versionCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productColor != null and item.productColor != ''">
                product_color = #{item.productColor,jdbcType=VARCHAR},
            </if>
            <if test="item.productThicknessRange != null and item.productThicknessRange != ''">
                product_thickness_range = #{item.productThicknessRange,jdbcType=VARCHAR},
            </if>
            <if test="item.demandedMonthStart != null and item.demandedMonthStart != ''">
                demanded_month_start = #{item.demandedMonthStart,jdbcType=VARCHAR},
            </if>
            <if test="item.demandedMonthEnd != null and item.demandedMonthEnd != ''">
                demanded_month_end = #{item.demandedMonthEnd,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_original_film_demand_consult_version
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productColor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_thickness_range = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productThicknessRange,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demanded_month_start = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandedMonthStart,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demanded_month_end = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandedMonthEnd,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_original_film_demand_consult_version
            <set>
                <if test="item.versionCode != null and item.versionCode != ''">
                    version_code = #{item.versionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productColor != null and item.productColor != ''">
                    product_color = #{item.productColor,jdbcType=VARCHAR},
                </if>
                <if test="item.productThicknessRange != null and item.productThicknessRange != ''">
                    product_thickness_range = #{item.productThicknessRange,jdbcType=VARCHAR},
                </if>
                <if test="item.demandedMonthStart != null and item.demandedMonthStart != ''">
                    demanded_month_start = #{item.demandedMonthStart,jdbcType=VARCHAR},
                </if>
                <if test="item.demandedMonthEnd != null and item.demandedMonthEnd != ''">
                    demanded_month_end = #{item.demandedMonthEnd,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_original_film_demand_consult_version
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_original_film_demand_consult_version where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
