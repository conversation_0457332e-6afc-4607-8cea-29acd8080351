package com.yhl.scp.mrp.material.distribute.domain.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.BigDecimalUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.constants.StringConstants;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.distribute.domain.entity.DistributeContext;
import com.yhl.scp.mrp.material.distribute.domain.process.AbstractDistributeCheck;
import com.yhl.scp.mrp.material.distribute.dto.MaterialDemandDTO;
import com.yhl.scp.mrp.material.distribute.dto.MaterialFulfillmentDTO;
import com.yhl.scp.mrp.material.distribute.dto.MaterialSupplyDTO;
import com.yhl.scp.mrp.material.distribute.enums.MrpSupplyTypeEnum;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanReplaceVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.sds.basic.enums.KitStatusEnum;
import com.yhl.scp.sds.basic.pegging.enums.DemandTypeEnum;
import com.yhl.scp.sds.basic.pegging.enums.FulfillmentStatusEnum;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DefaultDistributeCheckImpl</code>
 * <p>
 * 默认实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 14:58:32
 */
@Slf4j
@Service
public class DefaultDistributeCheckImpl extends AbstractDistributeCheck {


    @Override
    protected void removeAlreadyExistsResult() {
        log.info("开始清除已存在的齐套检查结果");
        materialDemandDao.deleteAll();
        materialFulfillmentDao.deleteAll();
        materialSupplyDao.deleteAll();
        log.info("已清除已存在的齐套检查结果");
    }

    @Override
    protected void generateDemandData(DistributeContext context) {
        log.info("生成需求demand开始");
        List<OperationVO> operationVOS = context.getOperationVOS();
        List<OperationVO> parentOperationList = operationVOS.stream().filter(p -> StrUtil.isEmpty(p.getParentId())).collect(Collectors.toList());
        Map<String, List<OperationVO>> childOperationMap = operationVOS.stream().filter(
                p -> StrUtil.isNotEmpty(p.getParentId())).collect(Collectors.groupingBy(OperationVO::getParentId));
        // 包装工序毛需求，来自毛需求
        Date planStartTime = context.getPlanningHorizonVO().getPlanStartTime();
        Date demandRangeTime = DateUtil.offsetDay(planStartTime, 7);
        Map<String, List<OperationVO>> operationMap = StreamUtils.mapListByColumn(parentOperationList, OperationVO::getProductId);
        for (Map.Entry<String, List<OperationVO>> entry : operationMap.entrySet()) {
            List<OperationVO> parentOperation = entry.getValue();
            boolean isFirstOrder = true;
            parentOperation.sort(Comparator.comparing(OperationVO::getStartTime));
            for (OperationVO operationParent : parentOperation) {
                String id = operationParent.getId();
                if (!childOperationMap.containsKey(id)) {
                    continue;
                }
                List<OperationVO> childOperations = childOperationMap.get(id);
                BigDecimal demandQuantity = BigDecimal.ZERO;
                for (OperationVO operationVO : childOperations) {
                    // 获取完工数量
                    BigDecimal reportingQuantity = BigDecimal.ZERO;
                    List<FeedbackProductionVO> feedbackProductionVOS = context.getFeedbackProductionGroup().get(operationVO.getId());
                    if (CollectionUtils.isNotEmpty(feedbackProductionVOS)) {
                        reportingQuantity = feedbackProductionVOS.stream()
                                .map(FeedbackProductionVO::getReportingQuantity)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    // 需求数量 = 工序排产数量 - 完工数量
                    BigDecimal childDemandQuantity = operationVO.getQuantity().subtract(reportingQuantity);
                    if (demandQuantity.compareTo(BigDecimal.ZERO) < 0) {
                        continue;
                    }
                    demandQuantity = demandQuantity.add(childDemandQuantity);
                }
                operationParent.setQuantity(demandQuantity);
                String productId = operationParent.getProductId();
                NewProductStockPointVO bcProductStockPointVO = context.getProductOnIdMap().get(productId);
                String routingStepId = operationParent.getRoutingStepId();
                String routingStepName = operationParent.getRoutingStepName();
                List<RoutingStepInputVO> routingStepInputVOList = context.getRoutingStepInputGroupOfStepId().get(routingStepId);
                if (CollectionUtils.isEmpty(routingStepInputVOList)) {
                    log.warn("工序：{}，无输入物品", routingStepId);
                    continue;
                }
                Date startTime = operationParent.getStartTime();
                boolean range = startTime.getTime() < demandRangeTime.getTime();
                for (RoutingStepInputVO stepInputVO : routingStepInputVOList) {
                    // 获取物品
                    NewProductStockPointVO inputProductStockPoint = context.getProductOnIdMap().get(stepInputVO.getInputProductId());
                    if (null == inputProductStockPoint) {
                        continue;
                    }
                    if (routingStepName.equals("包装") && range) {
                        // 计划期间七天内的包装工序使用毛需求计划
                        if (isFirstOrder) {
                            // 七天内仅生第一个订单的包装工序生效
                            packingGenerateDemand(operationParent, inputProductStockPoint, bcProductStockPointVO, stepInputVO,
                                    context, startTime, demandRangeTime);
                        }
                        isFirstOrder = false;
                    } else {
                        // 生成生产计划毛需求计划
                        generateMpsGrossDemand(operationParent, inputProductStockPoint, bcProductStockPointVO, stepInputVO, context);
                    }
                }
            }
        }
        log.info("生成需求demand结束");
    }

    private void packingGenerateDemand(OperationVO operationParent, NewProductStockPointVO inputProductStockPoint,
                                       NewProductStockPointVO bcProductStockPointVO, RoutingStepInputVO stepInputVO,
                                       DistributeContext context, Date startTime, Date demandRangeTime) {
        if (!StringUtils.equals("P", inputProductStockPoint.getProductType())) {
            // 半品需求不处理
            return;
        }
        String productClassify = inputProductStockPoint.getProductClassify();
        String factoryCode = bcProductStockPointVO.getProductCode();
        String materialProductCode = inputProductStockPoint.getProductCode();
        List<MaterialGrossDemandVO> materialGrossDemandVOS = context.getFinishedMaterialGrossDemand().get(factoryCode);
        if (CollectionUtils.isEmpty(materialGrossDemandVOS)) {
            log.warn("成品：{}，无毛需求数据", factoryCode);
            return;
        }
        // 筛选包装工序毛需求，混合替代还是主料替代都能筛出来，需求全部转化为替代料
        List<MaterialGrossDemandVO> replaceDemand = materialGrossDemandVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getMainProductCode())
                && p.getMainProductCode().equals(materialProductCode)
                && (p.getDemandTime().getTime() >= startTime.getTime() && p.getDemandTime().getTime() <= demandRangeTime.getTime())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(replaceDemand)) {
            // 没有替代料使用自己的包装毛需求
            replaceDemand = materialGrossDemandVOS.stream().filter(p -> p.getProductCode().equals(materialProductCode)
                    && (p.getDemandTime().getTime() >= startTime.getTime() && p.getDemandTime().getTime() <= demandRangeTime.getTime())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(replaceDemand)) {
            log.warn("工序：{}，包装工序没有匹配到毛需求数据", operationParent.getId());
            return;
        }
        for (MaterialGrossDemandVO materialGrossDemandVO : replaceDemand) {
            String replaceProductId = materialGrossDemandVO.getProductId();
            BigDecimal substituteInputFactor = materialGrossDemandVO.getSubstituteInputFactor();
            // 毛需求包装工序的需求时间作为需求时间
            Date demandTime = materialGrossDemandVO.getDemandTime();
            NewProductStockPointVO replaceProductStockPointVO = context.getProductOnIdMap().get(replaceProductId);
            MaterialDemandDTO materialDemandDTO = MaterialDemandDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .demandTime(demandTime)
                    .productId(bcProductStockPointVO.getId())
                    .productCode(bcProductStockPointVO.getProductCode())
                    .stockPointId(bcProductStockPointVO.getStockPointCode())
                    .materialProductId(replaceProductStockPointVO.getId())
                    .materialProductCode(replaceProductStockPointVO.getProductCode())
                    .materialType(productClassify)
                    .quantity(materialGrossDemandVO.getDemandQuantity())
                    .unfulfilledQuantity(materialGrossDemandVO.getDemandQuantity())
                    .operationId(operationParent.getId())
                    .demandOrderId(operationParent.getOrderId())
                    .demandType(DemandTypeEnum.WORK_ORDER_DEMAND.getCode())
                    .fulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode())
                    .inputFactor(substituteInputFactor)
                    .replaceType("PACKING")
                    .build();
            context.getInsertMaterialDemandList().add(materialDemandDTO);
            context.getDemandProductList().add(inputProductStockPoint);
        }
    }


    private void generateMpsGrossDemand(OperationVO operationVO, NewProductStockPointVO inputProductStockPoint,
                                        NewProductStockPointVO bcProductStockPointVO,
                                        RoutingStepInputVO stepInputVO, DistributeContext context) {
        if (!StringUtils.equals("P", inputProductStockPoint.getProductType())) {
            // 半品需求不处理
            return;
        }
        if (null == stepInputVO.getYield()) {
            stepInputVO.setYield(BigDecimal.ONE);
        }
        if (null == stepInputVO.getInputFactor()) {
            stepInputVO.setInputFactor(BigDecimal.ONE);
        }
        String productClassify = inputProductStockPoint.getProductClassify();
        String factoryCode = bcProductStockPointVO.getProductCode();
        String materialProductCode = inputProductStockPoint.getProductCode();
        String key = StrUtil.join(StringConstants.SPLIT_STR_1, factoryCode, materialProductCode);
        Date startTime = operationVO.getStartTime();
        List<MaterialGrossDemandVO> materialGrossDemandVOS = context.getReplaceDemandMap().get(key);
        if (!TYPE_FLAG.equals(productClassify) && CollectionUtils.isNotEmpty(materialGrossDemandVOS) && materialGrossDemandVOS.size() > 1) {
            for (MaterialGrossDemandVO materialGrossDemandVO : materialGrossDemandVOS) {
                String replaceProductId = materialGrossDemandVO.getProductId();
                BigDecimal substituteInputFactor = materialGrossDemandVO.getSubstituteInputFactor();
                NewProductStockPointVO replaceProductStockPointVO = context.getProductOnIdMap().get(replaceProductId);
                MaterialDemandDTO materialDemandDTO = MaterialDemandDTO.builder()
                        .id(UUIDUtil.getUUID())
                        .demandTime(startTime)
                        .productId(bcProductStockPointVO.getId())
                        .productCode(bcProductStockPointVO.getProductCode())
                        .stockPointId(bcProductStockPointVO.getStockPointCode())
                        .materialProductId(replaceProductStockPointVO.getId())
                        .materialProductCode(replaceProductStockPointVO.getProductCode())
                        .materialType(productClassify)
                        .quantity(operationVO.getQuantity())
                        .unfulfilledQuantity(operationVO.getQuantity())
                        .operationId(operationVO.getId())
                        .demandOrderId(operationVO.getOrderId())
                        .demandType(DemandTypeEnum.WORK_ORDER_DEMAND.getCode())
                        .fulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode())
                        .inputFactor(substituteInputFactor)
                        .replaceType("REPLACE_PRODUCT")
                        .build();
                context.getInsertMaterialDemandList().add(materialDemandDTO);
                context.getDemandProductList().add(inputProductStockPoint);
            }
        } else {
            MaterialDemandDTO materialDemandDTO = MaterialDemandDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .demandTime(startTime)
                    .productId(bcProductStockPointVO.getId())
                    .productCode(bcProductStockPointVO.getProductCode())
                    .stockPointId(bcProductStockPointVO.getStockPointCode())
                    .materialProductId(inputProductStockPoint.getId())
                    .materialProductCode(inputProductStockPoint.getProductCode())
                    .materialType(productClassify)
                    .quantity(operationVO.getQuantity())
                    .unfulfilledQuantity(operationVO.getQuantity())
                    .operationId(operationVO.getId())
                    .demandOrderId(operationVO.getOrderId())
                    .demandType(DemandTypeEnum.WORK_ORDER_DEMAND.getCode())
                    .fulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode())
                    .inputFactor(stepInputVO.getInputFactor())
                    .build();
            context.getInsertMaterialDemandList().add(materialDemandDTO);
            context.getDemandProductList().add(inputProductStockPoint);
        }
    }

    @Override
    protected void alternativeRelationship(DistributeContext context) {
        Map<String, List<MaterialGrossDemandVO>> replaceDemandMap = context.getReplaceDemandMap();
        List<MaterialDemandDTO> insertMaterialDemandList = context.getInsertMaterialDemandList();
        List<String> productCodeList = StreamUtils.columnToList(insertMaterialDemandList, MaterialDemandDTO::getProductCode);
        List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOS = glassSubstitutionRelationshipService.selectByParams(ImmutableMap.of("productCodeList", productCodeList));
        Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionMap = StreamUtils.mapListByColumn(glassSubstitutionRelationshipVOS, p -> StrUtil.join(StringConstants.SPLIT_STR_1,
                p.getStockPointCode(), p.getProductCode(), p.getProductionSubstituteProductCode()));
        for (MaterialDemandDTO materialDemandDTO : insertMaterialDemandList) {
            String mainProductCode = materialDemandDTO.getProductCode();
            String materialProductCode = materialDemandDTO.getMaterialProductCode();
            String key = StrUtil.join(StringConstants.SPLIT_STR_1, mainProductCode, materialProductCode);
            if (!replaceDemandMap.containsKey(key)) {
                continue;
            }
            String materialType = materialDemandDTO.getMaterialType();
            if (materialType.equals(TYPE_FLAG)) {
                // 原片
                originalFilmMapping(materialDemandDTO, replaceDemandMap, context, glassSubstitutionMap);
            } else {
                // 非原片
                notOriginalFilmMapping(materialDemandDTO, replaceDemandMap, context);
            }
        }
    }

    private void originalFilmMapping(MaterialDemandDTO materialDemandDTO, Map<String, List<MaterialGrossDemandVO>> replaceDemandMap,
                                     DistributeContext context, Map<String, List<GlassSubstitutionRelationshipVO>> glassSubstitutionMap) {
        String id = materialDemandDTO.getId();
        String stockPointId = materialDemandDTO.getStockPointId();
        String productCode = materialDemandDTO.getProductCode();
        String materialProductCode = materialDemandDTO.getMaterialProductCode();
        String mainKey = StrUtil.join(StringConstants.SPLIT_STR_1, productCode, materialProductCode);
        List<MaterialGrossDemandVO> materialGrossDemandVOS = replaceDemandMap.get(mainKey);
        if (CollectionUtils.isEmpty(materialGrossDemandVOS)) {
            return;
        }
        // 根据erpBom查找生产BOM
        MaterialGrossDemandVO productionReplaceBom = materialGrossDemandVOS.get(0);
        DistributeContext.ReplaceMapping replaceMapping = DistributeContext.ReplaceMapping.builder()
                .replaceMode(false)
                .demandReplace(productionReplaceBom)
                .build();
        // 生产BOM物品代码
        String replaceCode = productionReplaceBom.getProductCode();
        String key = StrUtil.join(StringConstants.SPLIT_STR_1, stockPointId, productCode, replaceCode);
        if (glassSubstitutionMap.containsKey(key)) {
            List<GlassSubstitutionRelationshipVO> glassSubstitutionRelationshipVOS = glassSubstitutionMap.get(key);
            replaceMapping.setReplaceGlassDemandList(glassSubstitutionRelationshipVOS);
        }
        context.getReplaceMappingMap().put(id, replaceMapping);
    }

    private void notOriginalFilmMapping(MaterialDemandDTO materialDemandDTO, Map<String, List<MaterialGrossDemandVO>> replaceDemandMap,
                                        DistributeContext context) {
        String stockPointId = materialDemandDTO.getStockPointId();
        String productCode = materialDemandDTO.getProductCode();
        String materialProductCode = materialDemandDTO.getMaterialProductCode();
        String mainKey = StrUtil.join(StringConstants.SPLIT_STR_1, productCode, materialProductCode);
        List<MaterialGrossDemandVO> materialGrossDemandVOS = replaceDemandMap.get(mainKey);
        if (materialGrossDemandVOS.size() == 1) {
            // 主料替代：需求先用主料满足，再用替代料满足
            MaterialGrossDemandVO materialGrossDemandVO = materialGrossDemandVOS.get(0);
            String key = StrUtil.join(StringConstants.SPLIT_STR_1, stockPointId, productCode, materialProductCode);
            DistributeContext.ReplaceMapping replaceMapping = DistributeContext.ReplaceMapping.builder()
                    .replaceMode(false)
                    .demandReplace(materialGrossDemandVO)
                    .build();
            if (context.getProductSubstitutionMap().containsKey(key)) {
                List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS = context.getProductSubstitutionMap().get(key);
                productSubstitutionRelationshipVOS.sort(Comparator.comparing(ProductSubstitutionRelationshipVO::getPriority));
                replaceMapping.setSupplyReplace(productSubstitutionRelationshipVOS.get(0));
            }
            context.getReplaceMappingMap().put(materialDemandDTO.getId(), replaceMapping);
        }
    }

    @Override
    protected void generateRelatedSupply(DistributeContext distributeContext) {
        log.info("开始构建supply");
        // 非原片+原片-实时库存（物品id匹配）
        nonOriginalFilmStock(distributeContext);
        // 非原片-在途+采购订单（物品id匹配）
        nonOriginalFilmInTransit(distributeContext);

        // 原片-码头库存（物品代码匹配）
        originalFilmDockInventory(distributeContext);
        // 原片-汽运在途-海运在途（物品代码匹配）
        originalFilmInTransitStock(distributeContext);

        List<MaterialSupplyDTO> insertMaterialSupplyList = distributeContext.getInsertMaterialSupplyList();
        Map<String, List<MaterialSupplyDTO>> supplyProductMap = insertMaterialSupplyList.stream()
                .collect(Collectors.groupingBy(
                        p -> StrUtil.join(StringConstants.SPLIT_STR_1, p.getProductCode(), p.getSupplyType()),
                        Collectors.collectingAndThen(
                                Collectors.toList(), list -> list.stream()
                                        .sorted(Comparator.comparing(MaterialSupplyDTO::getUnfulfilledQuantity))
                                        .collect(Collectors.toList())
                        )
                ));
        distributeContext.setSupplyProductMap(supplyProductMap);
        log.info("库存构建结束");
    }


    private void originalFilmDockInventory(DistributeContext distributeContext) {
        List<InventoryQuayDetailVO> inventoryQuayDetailVOS = distributeContext.getInventoryQuayDetailVOS();
        if (CollectionUtils.isEmpty(inventoryQuayDetailVOS)) {
            log.warn("原片码头库存为空");
            return;
        }
        Date dayFirstTime = DateUtils.getDayFirstTime(new Date());
        Date startTime = DateUtil.offsetDay(dayFirstTime, 1);
        for (InventoryQuayDetailVO inventoryQuayDetailVO : inventoryQuayDetailVOS) {
            String productCode = inventoryQuayDetailVO.getProductCode();
            String stockPointCode = inventoryQuayDetailVO.getStockPointCode();
            BigDecimal actualSentQuantity = inventoryQuayDetailVO.getActualSentQuantity();
            // 确认码头库存的supply时间，送柜时间作为供应日期，没有送柜时间=当前时间+1天
            Date actualArrivalTime = inventoryQuayDetailVO.getActualArrivalTime() == null ? startTime : inventoryQuayDetailVO.getActualArrivalTime();
            MaterialSupplyDTO materialSupplyDTO = MaterialSupplyDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .productId(productCode)
                    .productCode(productCode)
                    .productType(TYPE_FLAG)
                    .stockPointId(stockPointCode)
                    .supplyTime(actualArrivalTime)
                    .quantity(actualSentQuantity)
                    .unfulfilledQuantity(actualSentQuantity)
                    .fulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode())
                    .supplyType(MrpSupplyTypeEnum.DOCK_INVENTORY_SUPPLY.getCode())
                    .remark(inventoryQuayDetailVO.getId())
                    .build();
            distributeContext.getInsertMaterialSupplyList().add(materialSupplyDTO);
        }
    }

    private void originalFilmInTransitStock(DistributeContext distributeContext) {
        List<InventoryFloatGlassShippedDetailVO> floatGlassShippedDetailVOS = distributeContext.getFloatGlassShippedDetailVOS();
        if (CollectionUtils.isEmpty(floatGlassShippedDetailVOS)) {
            log.info("原片汽运海运数据为空");
            return;
        }
        for (InventoryFloatGlassShippedDetailVO floatGlassShippedDetailVO : floatGlassShippedDetailVOS) {
            String productCode = floatGlassShippedDetailVO.getProductCode();
            String stockPointCode = floatGlassShippedDetailVO.getStockPointCode();
            BigDecimal actualSentQuantity = floatGlassShippedDetailVO.getActualSentQuantity();
            // 预计到港时间
            Date estimatedArrivalTime = floatGlassShippedDetailVO.getEstimatedArrivalTime();
            if (Objects.isNull(estimatedArrivalTime)) {
                log.warn("原片汽运海运预计到港时间为空：{}", floatGlassShippedDetailVO.getId());
                continue;
            }
            MaterialSupplyDTO materialSupplyDTO = MaterialSupplyDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .productId(productCode)
                    .productCode(productCode)
                    .productType(TYPE_FLAG)
                    .stockPointId(stockPointCode)
                    .quantity(actualSentQuantity)
                    .unfulfilledQuantity(actualSentQuantity)
                    .fulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode())
                    .remark(floatGlassShippedDetailVO.getId())
                    .build();
            String deliveryMethod = floatGlassShippedDetailVO.getDeliveryMethod();
            if (deliveryMethod.equals(AUTOMOBILE_TRANSPORTATION)) {
                materialSupplyDTO.setSupplyTime(estimatedArrivalTime);
                materialSupplyDTO.setSupplyType(MrpSupplyTypeEnum.AUTOMOBILE_INVENTORY_SUPPLY.getCode());
            }
            if (deliveryMethod.equals(CONTAINER_TRANSPORTATION)) {
                // 海运在途supply时间为预计到港时间+2天
                Date time = DateUtil.offsetDay(estimatedArrivalTime, 2);
                materialSupplyDTO.setSupplyTime(time);
                materialSupplyDTO.setSupplyType(MrpSupplyTypeEnum.SEA_FREIGHT_INVENTORY_SUPPLY.getCode());
            }
            distributeContext.getInsertMaterialSupplyList().add(materialSupplyDTO);
        }

    }


    private void nonOriginalFilmStock(DistributeContext distributeContext) {
        List<InventoryBatchDetailVO> inventoryBatchDetail = distributeContext.getInventoryBatchDetail();
        if (CollectionUtils.isEmpty(inventoryBatchDetail)) {
            log.info("实时库存为空");
            return;
        }
        Date supplyDate = DateUtils.stringToDate("2000-01-01 00:00:00", DateUtils.COMMON_DATE_STR1);
        Map<String, List<InventoryBatchDetailVO>> stockMap = StreamUtils.mapListByColumn(inventoryBatchDetail,
                p -> StrUtil.join(StringConstants.SPLIT_STR_1, p.getStockPointCode(), p.getProductCode()));
        for (Map.Entry<String, List<InventoryBatchDetailVO>> entry : stockMap.entrySet()) {
            String key = entry.getKey();
            NewProductStockPointVO newProductStockPointVO = distributeContext.getProductCodeMap().get(key);
            if (null == newProductStockPointVO) {
                log.warn("库存物料key：{}，没有对应物料信息", key);
                continue;
            }
            BigDecimal qty = entry.getValue()
                    .stream().map(inventoryBatchDetailVO -> BigDecimalUtils.toBigDecimal(inventoryBatchDetailVO.getCurrentQuantity())
                    ).reduce(BigDecimal.ZERO, BigDecimal::add);
            String productClassify = newProductStockPointVO.getProductClassify();
            MaterialSupplyDTO materialSupplyDTO = MaterialSupplyDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .productCode(newProductStockPointVO.getProductCode())
                    .productId(newProductStockPointVO.getId())
                    .productType(newProductStockPointVO.getProductClassify())
                    .stockPointId(newProductStockPointVO.getStockPointCode())
                    .supplyTime(supplyDate)
                    .quantity(qty)
                    .unfulfilledQuantity(qty)
                    .fulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode())
                    .build();
            if (productClassify.equals(TYPE_FLAG)) {
                materialSupplyDTO.setSupplyType(MrpSupplyTypeEnum.OUR_FACTORY_INVENTORY_SUPPLY.getCode());
            } else {
                materialSupplyDTO.setSupplyType(MrpSupplyTypeEnum.REAL_TIME_INVENTORY_SUPPLY.getCode());
            }
            distributeContext.getInsertMaterialSupplyList().add(materialSupplyDTO);
        }
    }

    private void nonOriginalFilmInTransit(DistributeContext distributeContext) {
        List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS = distributeContext.getMaterialArrivalTrackingVOS();
        if (CollectionUtils.isEmpty(materialArrivalTrackingVOS)) {
            log.info("非原片在途及采购订单供应为空");
            return;
        }
        for (MaterialArrivalTrackingVO materialArrivalTrackingVO : materialArrivalTrackingVOS) {
            String productId = materialArrivalTrackingVO.getProductId();
            String stockPointCode = materialArrivalTrackingVO.getStockPointCode();
            String materialCode = materialArrivalTrackingVO.getMaterialCode();
            String arrivalStatus = materialArrivalTrackingVO.getArrivalStatus();
            // 预计到货日期
            Date predictArrivalDate = materialArrivalTrackingVO.getPredictArrivalDate();
            BigDecimal predictArrivalQuantity = materialArrivalTrackingVO.getPredictArrivalQuantity();
            // 要货日期
            Date recommendRequireDate = materialArrivalTrackingVO.getRequireDate();
            BigDecimal requireQuantity = materialArrivalTrackingVO.getRequireQuantity();
            if (ArrivalStatusEnum.DELIVERED.getCode().equals(arrivalStatus) && Objects.isNull(predictArrivalDate)) {
                log.warn("非原片在途库存供应，预计到货时间为空");
                continue;
            }
            if (ArrivalStatusEnum.PLAN_PRUCHASE.getCode().equals(arrivalStatus) && Objects.isNull(recommendRequireDate)) {
                log.warn("非原片采购订单供应，要货时间为空");
                continue;
            }
            NewProductStockPointVO newProductStockPointVO = distributeContext.getProductOnIdMap().get(productId);
            MaterialSupplyDTO materialSupplyDTO = MaterialSupplyDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .productCode(materialCode)
                    .productId(productId)
                    .stockPointId(stockPointCode)
                    .productType(newProductStockPointVO.getProductClassify())
                    .stockPointId(newProductStockPointVO.getStockPointCode())
                    .fulfillmentStatus(FulfillmentStatusEnum.UNFULFILL.getCode())
                    .remark(materialArrivalTrackingVO.getId())
                    .build();
            if (ArrivalStatusEnum.DELIVERED.getCode().equals(arrivalStatus)) {
                materialSupplyDTO.setSupplyTime(predictArrivalDate);
                materialSupplyDTO.setQuantity(predictArrivalQuantity);
                materialSupplyDTO.setUnfulfilledQuantity(predictArrivalQuantity);
                materialSupplyDTO.setSupplyType(MrpSupplyTypeEnum.IN_TRANSIT_INVENTORY_SUPPLY.getCode());
            }
            if (ArrivalStatusEnum.PLAN_PRUCHASE.getCode().equals(arrivalStatus)) {
                materialSupplyDTO.setSupplyTime(recommendRequireDate);
                materialSupplyDTO.setQuantity(requireQuantity);
                materialSupplyDTO.setUnfulfilledQuantity(requireQuantity);
                materialSupplyDTO.setSupplyType(MrpSupplyTypeEnum.PURCHASE_ORDER_INVENTORY_SUPPLY.getCode());
            }
            distributeContext.getInsertMaterialSupplyList().add(materialSupplyDTO);
        }
    }


    @Override
    protected void kitStateCalculations(DistributeContext distributeContext) {
        List<OperationVO> operation = distributeContext.getOperationVOS()
                .stream().filter(p -> StrUtil.isEmpty(p.getParentId())).collect(Collectors.toList());
        List<MaterialDemandDTO> insertMaterialDemandList = distributeContext.getInsertMaterialDemandList();
        List<MaterialFulfillmentDTO> insertMaterialFulfillmentList = distributeContext.getInsertMaterialFulfillmentList();
        Map<String, List<MaterialDemandDTO>> demandMap = StreamUtils.mapListByColumn(insertMaterialDemandList, MaterialDemandDTO::getOperationId);
        Map<String, List<MaterialFulfillmentDTO>> fulfillmenMap = StreamUtils.mapListByColumn(insertMaterialFulfillmentList, MaterialFulfillmentDTO::getDemandId);
        List<String> stockList = ListUtil.of(MrpSupplyTypeEnum.REAL_TIME_INVENTORY_SUPPLY.getCode(), MrpSupplyTypeEnum.OUR_FACTORY_INVENTORY_SUPPLY.getCode());

        for (OperationVO operationVO : operation) {
            String id = operationVO.getId();
            List<MaterialDemandDTO> materialDemandDTOS = demandMap.get(id);
            // 没有demand默认已齐套
            if (CollectionUtils.isEmpty(materialDemandDTOS)) {
                operationVO.setKitStatus(KitStatusEnum.KITTED.getCode());
                continue;
            }
            // 检查是否所有需求都已满足
            boolean allFulfilled = materialDemandDTOS.stream()
                    .allMatch(demand -> demand.getUnfulfilledQuantity().compareTo(BigDecimal.ZERO) <= 0);
            if (!allFulfilled) {
                operationVO.setKitStatus(KitStatusEnum.UNKIT.getCode());
                continue;
            }
            // 检查供应类型以确定齐套状态
            boolean isFullySuppliedByStock = true;
            for (MaterialDemandDTO materialDemandDTO : materialDemandDTOS) {
                String demandId = materialDemandDTO.getId();
                List<MaterialFulfillmentDTO> materialFulfillmentDTOS = fulfillmenMap.get(demandId);
                if (CollectionUtils.isNotEmpty(materialFulfillmentDTOS)) {
                    // 检查是否所有供应都来自库存
                    boolean allFromStock = materialFulfillmentDTOS.stream()
                            .allMatch(fulfillment -> stockList.contains(fulfillment.getSupplyType()));
                    if (!allFromStock) {
                        isFullySuppliedByStock = false;
                        break;
                    }
                }
            }
            // 设置齐套状态
            if (isFullySuppliedByStock) {
                operationVO.setKitStatus(KitStatusEnum.KITTED.getCode()); // 全部由库存满足 -> 已齐套
            } else {
                operationVO.setKitStatus(KitStatusEnum.KITTING.getCode()); // 部分由在途或采购订单满足 -> 预齐套
            }
        }
        Map<String, List<OperationVO>> operationMap = StreamUtils.mapListByColumn(operation, OperationVO::getOrderId);
        List<WorkOrderVO> workOrderVOS = distributeContext.getWorkOrderVOS();
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            String id = workOrderVO.getId();
            List<OperationVO> operationVOS = operationMap.get(id);
            if (CollectionUtils.isEmpty(operationVOS)) {
                workOrderVO.setKitStatus(KitStatusEnum.KITTED.getCode());
                continue;
            }
            // 工序全部齐套订单则为齐套，有未齐套工序则订单为未齐套，否则为预齐套
            boolean hasUnkit = false;
            boolean hasKitting = false;
            for (OperationVO operationVO : operationVOS) {
                String kitStatus = operationVO.getKitStatus();
                if (KitStatusEnum.UNKIT.getCode().equals(kitStatus)) {
                    hasUnkit = true;
                }
                if (KitStatusEnum.KITTING.getCode().equals(kitStatus)) {
                    hasKitting = true;
                }
            }
            // 根据工序状态确定工单状态
            if (hasUnkit) {
                workOrderVO.setKitStatus(KitStatusEnum.UNKIT.getCode()); // 有未齐套工序，则工单未齐套
            } else if (hasKitting) {
                workOrderVO.setKitStatus(KitStatusEnum.KITTING.getCode()); // 有预齐套工序，则工单预齐套
            } else {
                workOrderVO.setKitStatus(KitStatusEnum.KITTED.getCode()); // 全部齐套，则工单齐套
            }
        }
        // 更新operation及workOrder齐套状态
        mpsFeign.batchUpdateOperation(operation);
        mpsFeign.batchUpdateWorkOrder(workOrderVOS);
    }


    @Override
    protected void supplyDemandAllocation(DistributeContext distributeContext) {
        List<MaterialDemandDTO> insertMaterialDemandList = distributeContext.getInsertMaterialDemandList();
        Map<String, DistributeContext.ReplaceMapping> replaceMappingMap = distributeContext.getReplaceMappingMap();
        // 非原片
        List<MaterialDemandDTO> nonOriginalFilmDemand = StreamUtils.filter(insertMaterialDemandList,
                p -> !p.getMaterialType().equals(TYPE_FLAG));
        notOriginalFilmDemandSupply(nonOriginalFilmDemand, distributeContext, replaceMappingMap);
        // 原片
        List<MaterialDemandDTO> originalFilmDemand = StreamUtils.filter(insertMaterialDemandList,
                p -> p.getMaterialType().equals(TYPE_FLAG));
        originalFilmDemandSupply(originalFilmDemand, distributeContext, replaceMappingMap);
    }

    private void originalFilmDemandSupply(List<MaterialDemandDTO> originalFilmDemand,
                                          DistributeContext distributeContext,
                                          Map<String, DistributeContext.ReplaceMapping> replaceMappingMap) {
        if (CollectionUtils.isEmpty(originalFilmDemand)) {
            return;
        }
        log.info("原片分配开始");
        originalFilmDemand.sort(Comparator.comparing(MaterialDemandDTO::getDemandTime));
        for (MaterialDemandDTO demand : originalFilmDemand) {
            String id = demand.getId();
            DistributeContext.ReplaceMapping replaceMapping = replaceMappingMap.get(id);
            originalFilmMixedSubstitution(distributeContext, demand, replaceMapping);
        }
        log.info("原片分配结束");
    }

    private void originalFilmMixedSubstitution(DistributeContext distributeContext,
                                               MaterialDemandDTO demand,
                                               DistributeContext.ReplaceMapping replaceMapping) {
        BigDecimal quantity = demand.getUnfulfilledQuantity();
        BigDecimal inputFactor = demand.getInputFactor();
        // 主料
        String productId = demand.getMaterialProductId();
        // 净需求数量 = 需求数量 * 单耗
        BigDecimal netDemandQuantity = quantity.multiply(inputFactor).setScale(2, RoundingMode.HALF_UP);
        BigDecimal unFulfillmentQuantity = netDemandQuantity;
        if (Objects.nonNull(replaceMapping)) {
            // 原片替代料分配供应
            netDemandQuantity = originalFilmReplacement(distributeContext, demand, replaceMapping, netDemandQuantity);
        } else {
            List<String> supplySourceList = ListUtil.of(
                    MrpSupplyTypeEnum.OUR_FACTORY_INVENTORY_SUPPLY.getCode(),
                    MrpSupplyTypeEnum.AUTOMOBILE_INVENTORY_SUPPLY.getCode(),
                    MrpSupplyTypeEnum.DOCK_INVENTORY_SUPPLY.getCode(),
                    MrpSupplyTypeEnum.SEA_FREIGHT_INVENTORY_SUPPLY.getCode());
            for (String supplySource : supplySourceList) {
                // 循环使用不同供应分配需求
                netDemandQuantity = matchSupply(productId, netDemandQuantity, distributeContext, supplySource,
                        demand, false, null);
            }
        }
        // 计算未分配数量
        BigDecimal result = getSatisfied(netDemandQuantity, unFulfillmentQuantity, quantity);
        demand.setUnfulfilledQuantity(result);
        // 计算分配状态
        String fulfillStatus = getFulfillStatus(demand.getUnfulfilledQuantity(), demand.getQuantity());
        demand.setFulfillmentStatus(fulfillStatus);
    }

    /*
     * 生产BOM找临时替代计划，临时替代计划转为具体的原片编码，分配
     * 分配生产BOM的库存
     * 分配ERP-BOM的库存
     * 分配替代BOM的库存
     */
    private BigDecimal originalFilmReplacement(DistributeContext distributeContext, MaterialDemandDTO demand,
                                               DistributeContext.ReplaceMapping replaceMapping, BigDecimal netDemandQuantity) {
        // 生产BOM，这里的单耗使用‘一切几’来换算
        MaterialGrossDemandVO productionBom = replaceMapping.getDemandReplace();
        // 生产BOM物品代码
        String productionBomProductCode = productionBom.getProductCode();
        Date demandTime = demand.getDemandTime();
        // 生产BOM一切几
        BigDecimal substituteInputFactor = productionBom.getSubstituteInputFactor() == null ? BigDecimal.ONE : productionBom.getSubstituteInputFactor();
        BigDecimal productionBomDemandQty = BigDecimalUtils.multiply(netDemandQuantity, substituteInputFactor);
        BigDecimal productionBomUnFulfillmentQty = productionBomDemandQty;
        // 本厂库存 > 码头库存
        List<String> supplySource = ListUtil.of(
                MrpSupplyTypeEnum.OUR_FACTORY_INVENTORY_SUPPLY.getCode(),
                MrpSupplyTypeEnum.DOCK_INVENTORY_SUPPLY.getCode());
        List<String> supplySourceList = ListUtil.of(
                MrpSupplyTypeEnum.OUR_FACTORY_INVENTORY_SUPPLY.getCode(),
                MrpSupplyTypeEnum.AUTOMOBILE_INVENTORY_SUPPLY.getCode(),
                MrpSupplyTypeEnum.DOCK_INVENTORY_SUPPLY.getCode(),
                MrpSupplyTypeEnum.SEA_FREIGHT_INVENTORY_SUPPLY.getCode()
        );
        for (MaterialPlanReplaceVO materialPlanReplaceVO : distributeContext.getMaterialPlanReplaceVOS()) {
            Date replaceDate = materialPlanReplaceVO.getReplaceDate();
            if (demandTime.getTime() >= replaceDate.getTime()) {
                continue;
            }
            String masterProductCode = materialPlanReplaceVO.getMasterProductCode();
            StringBuilder sb = new StringBuilder(productionBomProductCode);
            sb.setCharAt(2, '*');
            String productCodeLikeCode = sb.toString();
            if (!masterProductCode.equals(productionBomProductCode) && !masterProductCode.equals(productCodeLikeCode)) {
                continue;
            }
            // 临时替代数量
            BigDecimal replaceQuantity = materialPlanReplaceVO.getReplaceQuantity().min(productionBomDemandQty);
            BigDecimal unFulfillReplaceQuantity = replaceQuantity;
            List<String> replaceProductCodeList = new ArrayList<>();
            String replaceProductCode = materialPlanReplaceVO.getReplaceProductCode();
            if (replaceProductCode.contains("*")) {
                List<String> replaceCodes = distributeContext.getReplaceProductMap().getOrDefault(replaceProductCode, new ArrayList<>());
                replaceProductCodeList.addAll(replaceCodes);
            } else {
                replaceProductCodeList.add(replaceProductCode);
            }
            for (String replaceCode : replaceProductCodeList) {
                for (String type : supplySource) {
                    replaceQuantity = matchSupply(replaceCode, replaceQuantity, distributeContext, type,
                            demand, true, BigDecimal.ONE);
                }
            }
            BigDecimal fulfillQty = BigDecimalUtils.subtract(unFulfillReplaceQuantity, replaceQuantity);
            // 需求数量 = 需求量 - 临时替代分配量
            productionBomDemandQty = BigDecimalUtils.subtract(productionBomDemandQty, fulfillQty);
            BigDecimal replaceQuantityOld = materialPlanReplaceVO.getReplaceQuantity();
            materialPlanReplaceVO.setReplaceQuantity(BigDecimalUtils.subtract(replaceQuantityOld, fulfillQty));
        }

        // 生产BOM分配
        String productionProductId = productionBom.getProductId();
        for (String productionBomSupplySource : supplySourceList) {
            productionBomDemandQty = matchSupply(productionProductId, productionBomDemandQty, distributeContext, productionBomSupplySource,
                    demand, true, substituteInputFactor);
        }

        // ERP-BOM分配
        String materialProductId = demand.getMaterialProductId();
        // 根据满足比重新计算erpBom的量
        netDemandQuantity = getSatisfied(productionBomDemandQty, productionBomUnFulfillmentQty, netDemandQuantity);
        for (String productionBomSupplySource : supplySourceList) {
            netDemandQuantity = matchSupply(materialProductId, netDemandQuantity, distributeContext, productionBomSupplySource,
                    demand, true, substituteInputFactor);
        }

        // todo 替代BOM分配
        List<GlassSubstitutionRelationshipVO> replaceGlassDemandList = replaceMapping.getReplaceGlassDemandList();
        netDemandQuantity = replaceBomGlassFulfill(replaceGlassDemandList, distributeContext, demand, netDemandQuantity);
        return netDemandQuantity;
    }


    private BigDecimal replaceBomGlassFulfill(List<GlassSubstitutionRelationshipVO> replaceGlassDemandList,
                                              DistributeContext distributeContext,
                                              MaterialDemandDTO demand, BigDecimal netDemandQuantity) {
        if (CollectionUtils.isEmpty(replaceGlassDemandList) || netDemandQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            return netDemandQuantity;
        }
        for (GlassSubstitutionRelationshipVO glassSubstitutionRelationshipVO : replaceGlassDemandList) {
            if (netDemandQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            BigDecimal productionInputFactor = glassSubstitutionRelationshipVO.getProductionInputFactor();
            BigDecimal substituteInputFactor = glassSubstitutionRelationshipVO.getSubstituteInputFactor();
            String stockPointCode = glassSubstitutionRelationshipVO.getStockPointCode();
            String substituteProductCode = glassSubstitutionRelationshipVO.getSubstituteProductCode();
            BigDecimal cutRatio = BigDecimalUtils.divide(productionInputFactor, substituteInputFactor, 2);
            BigDecimal replaceDemandQty = BigDecimalUtils.multiply(netDemandQuantity, cutRatio);
            BigDecimal unFulfillReplaceDemandQty = replaceDemandQty;
            String key = StrUtil.join(StringConstants.SPLIT_STR_1, stockPointCode, substituteProductCode);
            NewProductStockPointVO newProductStockPointVO = distributeContext.getProductCodeMap().getOrDefault(key, new NewProductStockPointVO());
            String materialProductId = newProductStockPointVO.getId();
            // 本厂-用物品id
            replaceDemandQty = matchSupply(materialProductId, replaceDemandQty, distributeContext, MrpSupplyTypeEnum.OUR_FACTORY_INVENTORY_SUPPLY.getCode(),
                    demand, true, cutRatio);
            // 码头-用替代料物品代码
            replaceDemandQty = matchSupply(substituteProductCode, replaceDemandQty, distributeContext, MrpSupplyTypeEnum.DOCK_INVENTORY_SUPPLY.getCode(),
                    demand, true, cutRatio);
            netDemandQuantity = getSatisfied(replaceDemandQty, unFulfillReplaceDemandQty, netDemandQuantity);
        }
        // 主料ERP-BOM的量
        return netDemandQuantity;
    }

    private void notOriginalFilmDemandSupply(List<MaterialDemandDTO> nonOriginalFilmDemand,
                                             DistributeContext distributeContext,
                                             Map<String, DistributeContext.ReplaceMapping> replaceMappingMap) {
        if (CollectionUtils.isEmpty(nonOriginalFilmDemand)) {
            return;
        }
        log.info("非原片分配开始");
        nonOriginalFilmDemand.sort(Comparator.comparing(MaterialDemandDTO::getDemandTime));
        for (MaterialDemandDTO demand : nonOriginalFilmDemand) {
            String id = demand.getId();
            DistributeContext.ReplaceMapping replaceMapping = replaceMappingMap.get(id);
            mixedSubstitution(distributeContext, demand, replaceMapping);
        }
        log.info("非原片分配结束");
    }

    private void mixedSubstitution(DistributeContext distributeContext, MaterialDemandDTO demand,
                                   DistributeContext.ReplaceMapping replaceMapping) {
        BigDecimal quantity = demand.getUnfulfilledQuantity();
        BigDecimal inputFactor = demand.getInputFactor() == null ? BigDecimal.ONE : demand.getInputFactor();
        // 主料
        String productId = demand.getMaterialProductId();
        // 净需求数量 = 需求数量 * 单耗
        BigDecimal netDemandQuantity = quantity.multiply(inputFactor).setScale(2, RoundingMode.HALF_UP);
        BigDecimal unFulfillmentQuantity = netDemandQuantity;
        List<String> supplySourceList = ListUtil.of(
                MrpSupplyTypeEnum.REAL_TIME_INVENTORY_SUPPLY.getCode(),
                MrpSupplyTypeEnum.IN_TRANSIT_INVENTORY_SUPPLY.getCode(),
                MrpSupplyTypeEnum.PURCHASE_ORDER_INVENTORY_SUPPLY.getCode());
        for (String supplySource : supplySourceList) {
            // 循环使用不同供应分配需求
            netDemandQuantity = matchSupply(productId, netDemandQuantity, distributeContext, supplySource,
                    demand, false, null);
        }
        // 计算未分配数量
        BigDecimal result = getSatisfied(netDemandQuantity, unFulfillmentQuantity, quantity);

        // 主料需求未被满足，使用替代料满足
        if (result.compareTo(BigDecimal.ZERO) > 0 && Objects.nonNull(replaceMapping)) {
            MaterialGrossDemandVO demandReplace = replaceMapping.getDemandReplace();
            String replaceProductId = demandReplace.getProductId();
            BigDecimal substituteInputFactor = demandReplace.getSubstituteInputFactor() == null ? BigDecimal.ONE : demandReplace.getSubstituteInputFactor();
            // 替代料需求数量
            BigDecimal replaceNetDemandQuantity = result.multiply(substituteInputFactor).setScale(2, RoundingMode.HALF_UP);
            // 替代料只能使用本厂库存
            BigDecimal notFulfillQty = matchSupply(replaceProductId, replaceNetDemandQuantity, distributeContext,
                    MrpSupplyTypeEnum.REAL_TIME_INVENTORY_SUPPLY.getCode(), demand, true, substituteInputFactor);
            // 根据满足比换算成品剩余量
            BigDecimal satisfied = getSatisfied(notFulfillQty, replaceNetDemandQuantity, result);
            demand.setUnfulfilledQuantity(satisfied);
        } else {
            demand.setUnfulfilledQuantity(result);
        }
        // 计算分配状态
        String fulfillStatus = getFulfillStatus(demand.getUnfulfilledQuantity(), demand.getQuantity());
        demand.setFulfillmentStatus(fulfillStatus);
    }

    private BigDecimal getSatisfied(BigDecimal unFulFillQuantity, BigDecimal sumNetDemandQuantity, BigDecimal sumFinishQuantity) {
        if (unFulFillQuantity.compareTo(BigDecimal.ZERO) == 0) {
            // 需求完全被分配了
            return BigDecimal.ZERO;
        }
        if (unFulFillQuantity.compareTo(sumNetDemandQuantity) == 0) {
            return sumFinishQuantity;
        }
        // 计算满足比：(未分配量/需求数量)
        BigDecimal satisfied = BigDecimalUtils.divide(unFulFillQuantity, sumNetDemandQuantity, 4);
        // 总量 / 满足比
        return BigDecimalUtils.multiply(sumFinishQuantity, satisfied, 2);
    }

    public static void main(String[] args) {
        // 假设 distributeContext.getInsertMaterialSupplyList() 返回如下对象
        DistributeContext distributeContext = new DistributeContext();
        MaterialSupplyDTO supply = new MaterialSupplyDTO();
        supply.setUnfulfilledQuantity(new BigDecimal("100"));
        supply.setProductId("PRODUCT_001");
        MaterialSupplyDTO supply2 = new MaterialSupplyDTO();
        supply2.setUnfulfilledQuantity(new BigDecimal("200"));
        supply2.setProductId("PRODUCT_002");
        List<MaterialSupplyDTO> materialSupplyDTOS = ListUtil.of(supply, supply2);
        distributeContext.setInsertMaterialSupplyList(materialSupplyDTOS);
        Map<String, List<MaterialSupplyDTO>> stringListMap = StreamUtils.mapListByColumn(materialSupplyDTOS, MaterialSupplyDTO::getProductId);
        distributeContext.setSupplyProductMap(stringListMap);

        List<MaterialSupplyDTO> product001 = distributeContext.getSupplyProductMap().get("PRODUCT_001");
        for (MaterialSupplyDTO materialSupplyDTO : product001) {
            materialSupplyDTO.setUnfulfilledQuantity(new BigDecimal("999"));
        }
        System.out.println(stringListMap);
    }


    private BigDecimal matchSupply(String productId, BigDecimal netDemandQuantity, DistributeContext distributeContext,
                                   String supplyType, MaterialDemandDTO demand, boolean whetherReplace, BigDecimal substituteInputFactor) {
        // 基础校验
        if (StrUtil.isEmpty(productId)) {
            log.error("匹配供应，物料id为空：{}", demand.getDemandOrderId());
            return netDemandQuantity;
        }
        if (netDemandQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }

        // 获取符合条件的供应列表
        List<MaterialSupplyDTO> supplyList = getQualifiedSupplyList(productId, supplyType, demand.getDemandTime(), distributeContext);

        // 分配供应
        if (CollectionUtils.isNotEmpty(supplyList)) {
            netDemandQuantity = allocateSupply(supplyList, netDemandQuantity, demand, supplyType, whetherReplace, substituteInputFactor, distributeContext);
        }

        return netDemandQuantity;
    }

    /**
     * 获取符合条件的供应列表
     */
    private List<MaterialSupplyDTO> getQualifiedSupplyList(String productId, String supplyType, Date demandTime, DistributeContext distributeContext) {
        List<MaterialSupplyDTO> materialSupplyDTOS = getSupplyListByProductId(productId, supplyType, distributeContext);

        if (CollectionUtils.isEmpty(materialSupplyDTOS)) {
            return Collections.emptyList();
        }

        // 判断是否需要时间筛选
        boolean needTimeFilter = isTimeFilterRequired(supplyType);

        return materialSupplyDTOS.stream()
                .filter(p -> p.getUnfulfilledQuantity().compareTo(BigDecimal.ZERO) > 0)
                .filter(p -> !needTimeFilter || p.getSupplyTime().getTime() <= demandTime.getTime())
                .collect(Collectors.toList());
    }

    /**
     * 根据供应类型获取供应列表
     */
    private List<MaterialSupplyDTO> getSupplyListByProductId(String productId, String supplyType, DistributeContext distributeContext) {
//        // 原片使用物料代码作为key
//        if (isOriginalFilmSupply(supplyType)) {
//            NewProductStockPointVO product = distributeContext.getProductOnIdMap().get(productId);
//            String productCode = Objects.isNull(product) ? productId : product.getProductCode();
//            String key = StrUtil.join(StringConstants.SPLIT_STR_1, productCode, supplyType);
//            return distributeContext.getSupplyProductMap().get(key);
//        }
//        // 非原片直接使用物料id
//        return distributeContext.getSupplyProductMap().get(StrUtil.join(StringConstants.SPLIT_STR_1, productId, supplyType));

        NewProductStockPointVO product = distributeContext.getProductOnIdMap().get(productId);
        String productCode = Objects.isNull(product) ? productId : product.getProductCode();
        // 匹配供应全部使用物料代码，可能会匹配到多个库存点供应
        String key = StrUtil.join(StringConstants.SPLIT_STR_1, productCode, supplyType);
        List<MaterialSupplyDTO> materialSupplyDTOS = distributeContext.getSupplyProductMap().get(key);
        return materialSupplyDTOS;
    }

    /**
     * 判断是否为原片供应类型
     */
    private boolean isOriginalFilmSupply(String supplyType) {
        return MrpSupplyTypeEnum.AUTOMOBILE_INVENTORY_SUPPLY.getCode().equals(supplyType)
                || MrpSupplyTypeEnum.SEA_FREIGHT_INVENTORY_SUPPLY.getCode().equals(supplyType)
                || MrpSupplyTypeEnum.DOCK_INVENTORY_SUPPLY.getCode().equals(supplyType);
    }

    /**
     * 判断是否需要时间筛选
     */
    private boolean isTimeFilterRequired(String supplyType) {
        return !supplyType.equals(MrpSupplyTypeEnum.REAL_TIME_INVENTORY_SUPPLY.getCode())
                && !supplyType.equals(MrpSupplyTypeEnum.OUR_FACTORY_INVENTORY_SUPPLY.getCode());
    }

    /**
     * 分配供应
     */
    private BigDecimal allocateSupply(List<MaterialSupplyDTO> supplyList, BigDecimal netDemandQuantity,
                                      MaterialDemandDTO demand, String supplyType, boolean whetherReplace,
                                      BigDecimal substituteInputFactor, DistributeContext distributeContext) {
        for (MaterialSupplyDTO materialSupplyDTO : supplyList) {
            if (netDemandQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                // 需求完全被满足了
                break;
            }

            BigDecimal unfulfilledQuantity = materialSupplyDTO.getUnfulfilledQuantity();
            BigDecimal supplyQty = unfulfilledQuantity.min(netDemandQuantity);
            netDemandQuantity = netDemandQuantity.subtract(supplyQty);
            unfulfilledQuantity = unfulfilledQuantity.subtract(supplyQty);

            // 更新供应信息
            materialSupplyDTO.setUnfulfilledQuantity(unfulfilledQuantity);
            materialSupplyDTO.setFulfillmentStatus(getFulfillStatus(unfulfilledQuantity, materialSupplyDTO.getQuantity()));

            // 创建履行记录
            MaterialFulfillmentDTO materialFulfillmentDTO = buildFulfillmentRecord(demand, materialSupplyDTO,
                    supplyQty, supplyType, whetherReplace, substituteInputFactor);
            distributeContext.getInsertMaterialFulfillmentList().add(materialFulfillmentDTO);
        }

        return netDemandQuantity;
    }

    /**
     * 构建履行记录
     */
    private MaterialFulfillmentDTO buildFulfillmentRecord(MaterialDemandDTO demand, MaterialSupplyDTO supply,
                                                          BigDecimal supplyQty, String supplyType,
                                                          boolean whetherReplace, BigDecimal substituteInputFactor) {
        MaterialFulfillmentDTO materialFulfillmentDTO = MaterialFulfillmentDTO.builder()
                .id(UUIDUtil.getUUID())
                .netDemandQuantity(demand.getQuantity())
                .demandOrderId(demand.getDemandOrderId())
                .demandId(demand.getId())
                .demandProductId(demand.getMaterialProductId())
                .demandStockPointId(demand.getStockPointId())
                .demandType(demand.getDemandType())
                .supplyId(supply.getId())
                .supplyProductId(supply.getProductId())
                .supplyStockPointId(supply.getStockPointId())
                .fulfillmentQuantity(supplyQty)
                .supplyType(supplyType)
                .build();

        if (whetherReplace) {
            materialFulfillmentDTO.setAltMaterialUsed(YesOrNoEnum.YES.getCode());
            materialFulfillmentDTO.setAltRate(substituteInputFactor);
        }

        return materialFulfillmentDTO;
    }


    private String getFulfillStatus(BigDecimal unfulfilledQuantity, BigDecimal quantity) {
        if (unfulfilledQuantity.compareTo(quantity) == 0) {
            return FulfillmentStatusEnum.UNFULFILL.getCode();
        }
        if (unfulfilledQuantity.compareTo(BigDecimal.ZERO) == 0) {
            return FulfillmentStatusEnum.ALL_FULFILLED.getCode();
        }
        return FulfillmentStatusEnum.SOME_FULFILLED.getCode();
    }

    @Override
    protected void dataBaseOperation(DistributeContext distributeContext) {
        List<MaterialDemandDTO> insertMaterialDemandList = distributeContext.getInsertMaterialDemandList();
        List<MaterialSupplyDTO> insertMaterialSupplyList = distributeContext.getInsertMaterialSupplyList();
        List<MaterialFulfillmentDTO> insertMaterialFulfillmentList = distributeContext.getInsertMaterialFulfillmentList();
        log.info("insertMaterialDemandList数量：{}，insertMaterialSupplyList数量：{}，insertMaterialFulfillmentList数量：{}", insertMaterialDemandList.size(), insertMaterialSupplyList.size(), insertMaterialFulfillmentList.size());
        if (CollectionUtils.isNotEmpty(insertMaterialDemandList)) {
            Lists.partition(insertMaterialDemandList, 2000).forEach(subList -> materialDemandService.doCreateBatch(subList));
        }
        if (CollectionUtils.isNotEmpty(insertMaterialSupplyList)) {
            Lists.partition(insertMaterialSupplyList, 2000).forEach(subList -> materialSupplyService.doCreateBatch(subList));
        }
        if (CollectionUtils.isNotEmpty(insertMaterialFulfillmentList)) {
            Lists.partition(insertMaterialFulfillmentList, 2000).forEach(subList -> materialFulfillmentService.doCreateBatch(subList));
        }
        log.info("数据库操作结束");
    }

}
