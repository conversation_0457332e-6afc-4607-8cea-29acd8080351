package com.yhl.scp.mrp.material.distribute.domain.process;

import com.alibaba.fastjson.JSON;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mrp.material.distribute.domain.IDistributeCheck;
import com.yhl.scp.mrp.material.distribute.domain.entity.DistributeContext;
import com.yhl.scp.mrp.material.distribute.domain.entity.DistributeDataSupport;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <code>AbstractDistributeCheck</code>
 * <p>
 * 齐套检查主流程
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 14:38:05
 */
@Slf4j
public abstract class AbstractDistributeCheck extends DistributeDataSupport implements IDistributeCheck {

    /**
     * MPS:
     * 根据计划期间风险等级获取检查订单列表
     * 计划调整
     * 获取计算工序列表
     * MRP:
     * 清楚历史分配结果
     * 展开BOM获取毛需求数据
     * 关联原片/非原片替代料信息
     * 查找原片/非原片供应
     * 物料分配，判断分配数量及状态
     * 齐套状态更新
     */
    @Override
    @Transactional
    public void doCheck(String scenario) {
        String taskId = UUIDUtil.getUUID();
        try {
            // 执行任务校验
            runTaskCheck(scenario);
            // 根据计划期间风险等级获取检查订单列表,计划调整
            BaseResponse<List<OperationVO>> dataResult = mpsFeign.planAdjustGetData(scenario, taskId);
            if (!dataResult.getSuccess()) {
                throw new BusinessException(dataResult.getMsg());
            }
            // 初始化数据
            DistributeContext distributeContext = super.initData(dataResult.getData(), scenario);
            // 展开BOM获取毛需求数据
            generateDemandData(distributeContext);
            // 替代关系映射
            alternativeRelationship(distributeContext);
            // 根据需求结果关联供应
            generateRelatedSupply(distributeContext);
            // 供需分配,区分原片/非原片
            supplyDemandAllocation(distributeContext);
            // 清除已存在的齐套检查结果
            removeAlreadyExistsResult();
            // 齐套状态计算
            kitStateCalculations(distributeContext);
            // 结果落库
            dataBaseOperation(distributeContext);
            // 记录日志
            writeLogging(distributeContext, taskId);
        } catch (Exception e) {
            log.error("MRP齐套检查异常:", e);
            throw new BusinessException("MRP齐套检查异常:" + e.getMessage());
        }
    }

    private void writeLogging(DistributeContext distributeContext, String taskId) {
        List<String> logInfo = distributeContext.getLogInfo();
        // TODO 记录计算日志进计划调整算法包
    }

    private void runTaskCheck(String scenario) {
        List<AlgorithmLog> algorithmLogs = ipsFeign.selectScenarioAlgorithmRunCheck(scenario);
        if (CollectionUtils.isNotEmpty(algorithmLogs)) {
            log.info("齐套检查算法运行任务信息：{}", JSON.toJSONString(algorithmLogs));
            throw new BusinessException("齐套检查执行终止，已有算法任务在执行，请等待结束后操作");
        }
    }

    protected abstract void dataBaseOperation(DistributeContext distributeContext);

    protected abstract void alternativeRelationship(DistributeContext distributeContext);

    protected abstract void kitStateCalculations(DistributeContext distributeContext);

    protected abstract void supplyDemandAllocation(DistributeContext distributeContext);

    protected abstract void generateRelatedSupply(DistributeContext distributeContext);

    protected abstract void generateDemandData(DistributeContext data);

    protected abstract void removeAlreadyExistsResult();


}
