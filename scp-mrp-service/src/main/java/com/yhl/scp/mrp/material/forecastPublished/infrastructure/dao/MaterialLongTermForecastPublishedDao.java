package com.yhl.scp.mrp.material.forecastPublished.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.forecastPublished.infrastructure.po.MaterialLongTermForecastPublishedPO;
import com.yhl.scp.mrp.material.forecastPublished.vo.MaterialLongTermForecastPublishedVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialLongTermForecastPublishedDao</code>
 * <p>
 * 材料长期预测发布表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-07 15:02:57
 */
public interface MaterialLongTermForecastPublishedDao extends BaseDao<MaterialLongTermForecastPublishedPO, MaterialLongTermForecastPublishedVO> {

    @Select("SELECT DISTINCT DATE_FORMAT(demand_date, '%Y-%m') AS demandDateStr " +
            "FROM mrp_material_long_term_forecast_published " +
            "WHERE demand_date >= DATE_FORMAT(CURDATE(), '%Y-%m-01')")
    List<String> selectDistinctDemandDateStr();

    List<MaterialLongTermForecastPublishedVO> selectGroupByParams(@Param("params") Map<String, Object> params);
}
