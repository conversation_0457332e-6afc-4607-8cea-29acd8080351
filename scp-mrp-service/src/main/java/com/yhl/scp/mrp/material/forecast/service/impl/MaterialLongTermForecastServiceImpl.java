package com.yhl.scp.mrp.material.forecast.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mrp.material.forecast.convertor.MaterialLongTermForecastConvertor;
import com.yhl.scp.mrp.material.forecast.domain.entity.MaterialLongTermForecastDO;
import com.yhl.scp.mrp.material.forecast.domain.service.MaterialLongTermForecastDomainService;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastDTO;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastIssueDetailDTO;
import com.yhl.scp.mrp.material.forecast.dto.MaterialLongTermForecastParam;
import com.yhl.scp.mrp.material.forecast.infrastructure.dao.MaterialLongTermForecastDao;
import com.yhl.scp.mrp.material.forecast.infrastructure.po.MaterialLongTermForecastPO;
import com.yhl.scp.mrp.material.forecast.service.MaterialLongTermForecastIssueDetailService;
import com.yhl.scp.mrp.material.forecast.service.MaterialLongTermForecastService;
import com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastDetailVO;
import com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastIssueDetailVO;
import com.yhl.scp.mrp.material.forecast.vo.MaterialLongTermForecastVO;
import com.yhl.scp.mrp.material.forecastPublished.domain.service.MaterialLongTermForecastPublishedDomainService;
import com.yhl.scp.mrp.material.forecastPublished.dto.MaterialLongTermForecastPublishedDTO;
import com.yhl.scp.mrp.material.forecastPublished.dto.MaterialLongTermForecastPublishedVersionDTO;
import com.yhl.scp.mrp.material.forecastPublished.service.MaterialLongTermForecastPublishedService;
import com.yhl.scp.mrp.material.forecastPublished.service.MaterialLongTermForecastPublishedVersionService;
import com.yhl.scp.mrp.material.forecastPublished.vo.MaterialLongTermForecastPublishedVO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedReleaseMonthDTO;
import com.yhl.scp.mrp.material.plan.result.InsertRollPredictionMonthResult;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftSupplyVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.math.BigDecimal;

/**
 * <code>MaterialLongTermForecastServiceImpl</code>
 * <p>
 * 材料长期预测应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 15:45:41
 */
@Slf4j
@Service
public class MaterialLongTermForecastServiceImpl extends AbstractService implements MaterialLongTermForecastService {

    @Resource
    private MaterialLongTermForecastDao materialLongTermForecastDao;

    @Resource
    private MaterialLongTermForecastDomainService materialLongTermForecastDomainService;

    @Resource
    private MaterialLongTermForecastIssueDetailService materialLongTermForecastIssueDetailService;

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private MaterialLongTermForecastPublishedDomainService materialLongTermForecastPublishedDomainService;

    @Resource
    private MaterialLongTermForecastPublishedService materialLongTermForecastPublishedService;

    @Resource
    private MaterialLongTermForecastPublishedVersionService materialLongTermForecastPublishedVersionService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MaterialLongTermForecastDTO materialLongTermForecastDTO) {
        // 0.数据转换
        MaterialLongTermForecastDO materialLongTermForecastDO = MaterialLongTermForecastConvertor.INSTANCE.dto2Do(materialLongTermForecastDTO);
        MaterialLongTermForecastPO materialLongTermForecastPO = MaterialLongTermForecastConvertor.INSTANCE.dto2Po(materialLongTermForecastDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialLongTermForecastDomainService.validation(materialLongTermForecastDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialLongTermForecastPO);
        materialLongTermForecastDao.insert(materialLongTermForecastPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MaterialLongTermForecastDTO materialLongTermForecastDTO) {
        // 0.数据转换
        MaterialLongTermForecastDO materialLongTermForecastDO = MaterialLongTermForecastConvertor.INSTANCE.dto2Do(materialLongTermForecastDTO);
        MaterialLongTermForecastPO materialLongTermForecastPO = MaterialLongTermForecastConvertor.INSTANCE.dto2Po(materialLongTermForecastDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialLongTermForecastDomainService.validation(materialLongTermForecastDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialLongTermForecastPO);
        materialLongTermForecastDao.update(materialLongTermForecastPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialLongTermForecastDTO> list) {
        List<MaterialLongTermForecastPO> newList = MaterialLongTermForecastConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialLongTermForecastDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialLongTermForecastDTO> list) {
        List<MaterialLongTermForecastPO> newList = MaterialLongTermForecastConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialLongTermForecastDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialLongTermForecastDao.deleteBatch(idList);
        }
        return materialLongTermForecastDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialLongTermForecastVO selectByPrimaryKey(String id) {
        MaterialLongTermForecastPO po = materialLongTermForecastDao.selectByPrimaryKey(id);
        return MaterialLongTermForecastConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_long_term_forecast")
    public List<MaterialLongTermForecastVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_long_term_forecast")
    public List<MaterialLongTermForecastVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialLongTermForecastVO> dataList = materialLongTermForecastDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialLongTermForecastServiceImpl target = SpringBeanUtils.getBean(MaterialLongTermForecastServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialLongTermForecastVO> selectByParams(Map<String, Object> params) {
        List<MaterialLongTermForecastPO> list = materialLongTermForecastDao.selectByParams(params);
        return MaterialLongTermForecastConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialLongTermForecastVO> selectVOByParams(Map<String, Object> params) {
        return materialLongTermForecastDao.selectVOByParams(params);
    }

    @Override
    public List<MaterialLongTermForecastVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> issue(List<String> ids, String type) {
        // 获取对应长期预测
        List<MaterialLongTermForecastVO> materialLongTermForecastVOList;
        if (CollectionUtils.isEmpty(ids)) {
            materialLongTermForecastVOList = this.selectVOByParams(ImmutableMap.of("demandPattern", type));
        } else {
            materialLongTermForecastVOList = this.selectVOByParams(ImmutableMap.of("ids", ids));
        }

        // 获取当前下发明细版本
        String versionCode = materialLongTermForecastPublishedVersionService.getVersionCode();

        // 先进行全量数据汇总
        Map<String, MaterialLongTermForecastVO> materialLongTermForecastVOMap = materialLongTermForecastVOList.stream()
                .collect(Collectors.toMap(
                        data -> String.join("_",
                                data.getProductCode(),
                                data.getSupplierCode(),
                                DateUtils.dateToString(data.getDemandDate(), "yyyy-MM")
                        ),
                        Function.identity(),
                        (data1, data2) -> {
                            data1.setDemandQuantity(data1.getDemandQuantity().add(data2.getDemandQuantity()));
                            return data1;
                        }
                ));

        // 将汇总后的结果按批次拆分（修正后的代码）
        int batchSize = 100;
        List<Map<String, MaterialLongTermForecastVO>> batches = new ArrayList<>();
        List<Map.Entry<String, MaterialLongTermForecastVO>> entries = new ArrayList<>(materialLongTermForecastVOMap.entrySet());

        for (int i = 0; i < entries.size(); i += batchSize) {
            Map<String, MaterialLongTermForecastVO> batchMap = new HashMap<>();
            // 使用Math.min确保j不会超过entries的最大有效索引
            for (int j = i; j < Math.min(i + batchSize, entries.size()); j++) {
                batchMap.put(entries.get(j).getKey(), entries.get(j).getValue());
            }
            batches.add(batchMap);
        }

        List<String> issueNos = materialLongTermForecastVOList.stream()
                .map(data -> data.getProductCode() + "_" + data.getSupplierCode())
                .distinct().collect(Collectors.toList());
        // 获取下发明细
        List<MaterialLongTermForecastIssueDetailVO> materialLongTermForecastIssueDetailVOList =
                materialLongTermForecastIssueDetailService.selectByParams(ImmutableMap.of(
                        "issueNos", issueNos,
                        "status", "true"));
        // 根据下发单号 分组
        Map<String, List<MaterialLongTermForecastIssueDetailVO>> materialLongTermForecastIssueDetailVOMap =
                materialLongTermForecastIssueDetailVOList.stream()
                        .collect(Collectors.groupingBy(MaterialLongTermForecastIssueDetailVO::getIssueNo));

        // 根据供应商分批发送（会多次调用接口）
        List<InsertRollPredictionMonthResult> insertRollPredictionMonthResults = new ArrayList<>();
        // 分批处理
        for (Map<String, MaterialLongTermForecastVO> batch : batches) {
            // 处理匹配的映射数据
            log.info("长期预测本次下发数据量{}", batch.values().size());
            insertRollPredictionMonthResults.addAll(
                    fetchInsertRollPredictionResult(
                            batch,
                            materialLongTermForecastIssueDetailVOMap,
                            versionCode
                    )
            );
        }

        // 根据状态分组看有无异常状态
        Map<Integer, List<InsertRollPredictionMonthResult>> statusDatMap =
                insertRollPredictionMonthResults.stream().collect(Collectors.groupingBy(InsertRollPredictionMonthResult::getStatus));
        // 记录下发失败长期预测日志
        processMaterialPlanLong(statusDatMap.getOrDefault(1, new ArrayList<>()), materialLongTermForecastVOMap, Boolean.FALSE.toString(), versionCode);
        // 记录下发成功长期预测日志
        processMaterialPlanLong(statusDatMap.getOrDefault(0, new ArrayList<>()), materialLongTermForecastVOMap, Boolean.TRUE.toString(), versionCode);

        // 记录到发布表
        if (CollectionUtils.isNotEmpty(materialLongTermForecastVOList)) {
            // 创建发布版本表
            MaterialLongTermForecastPublishedVersionDTO materialLongTermForecastPublishedVersionDTO = new MaterialLongTermForecastPublishedVersionDTO();
            materialLongTermForecastPublishedVersionDTO.setId(UUID.randomUUID().toString());
            materialLongTermForecastPublishedVersionDTO.setVersionCode(materialLongTermForecastPublishedVersionService.getVersionCode());
            materialLongTermForecastPublishedVersionService.doCreate(materialLongTermForecastPublishedVersionDTO);

            List<MaterialLongTermForecastPublishedDTO> list = new ArrayList<>();
            for (MaterialLongTermForecastVO materialLongTermForecastVO : materialLongTermForecastVOList) {
                MaterialLongTermForecastPublishedDTO materialLongTermForecastPublishedDTO = materialLongTermForecastPublishedDomainService.getDtoByMaterialLongTermForecastVO(materialLongTermForecastVO);
                materialLongTermForecastPublishedDTO.setId(UUID.randomUUID().toString());
                materialLongTermForecastPublishedDTO.setForecastVersionId(materialLongTermForecastPublishedVersionDTO.getId());
                list.add(materialLongTermForecastPublishedDTO);
            }
            materialLongTermForecastPublishedService.doCreateBatch(list);
        }

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    /**
     * 下发长期预测
     *
     * @param materialLongTermForecastVOMap            长期预测（物料编码 + 供应商编码 + 需求日期月维度）
     * @param materialLongTermForecastIssueDetailVOMap 长期预测下发明细（下发单号）
     * @param versionCode                              下发明细版本
     * @return 下发结果
     */
    public List<InsertRollPredictionMonthResult> fetchInsertRollPredictionResult(Map<String, MaterialLongTermForecastVO> materialLongTermForecastVOMap,
                                                                                 Map<String, List<MaterialLongTermForecastIssueDetailVO>> materialLongTermForecastIssueDetailVOMap,
                                                                                 String versionCode) {
        try {
            // 填充长期预测
            MaterialPlanNeedReleaseMonthDTO materialPlanNeedReleaseMonthDTO =
                    fillMaterialPlanNeedReleaseMonthDTO(materialLongTermForecastVOMap, materialLongTermForecastIssueDetailVOMap, versionCode);

            // 填充参数调用dcp服务下发30天要货计划
            HashMap<String, Object> params = new HashMap<>();
            params.put("Service", materialPlanNeedReleaseMonthDTO.getService());
            BaseResponse<String> response = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_PRODUCT_NEED_MONTH.getCode(), params);
//            log.info("下发长期预测数据为{}", materialPlanNeedReleaseMonthDTO);
            if (response.getSuccess().equals(false)) {
                throw new BusinessException("服务接口调用失败");
            }
            // 解析嵌套的 JSON 数组
            return JSON.parseArray(response.getData(), InsertRollPredictionMonthResult.class);
        } catch (Exception e) {
            log.error("下发长期预测失败", e);
            throw new BusinessException("下发长期预测失败{}", e.getLocalizedMessage());
        }
    }

    /**
     * 转换 MaterialPlanNeedReleaseMonthDTO
     *
     * @param materialLongTermForecastVOMap            长期预测（物料编码 + 供应商编码 + 需求日期月维度）
     * @param materialLongTermForecastIssueDetailVOMap 长期预测下发明细（下发单号）
     * @param versionCode                              下发明细版本
     * @return MaterialPlanNeedReleaseDayDTO 滚动预测要货计划请求参数DTO
     */
    private MaterialPlanNeedReleaseMonthDTO fillMaterialPlanNeedReleaseMonthDTO(Map<String, MaterialLongTermForecastVO> materialLongTermForecastVOMap,
                                                                                Map<String, List<MaterialLongTermForecastIssueDetailVO>> materialLongTermForecastIssueDetailVOMap,
                                                                                String versionCode) {

        // 初始化发布服务DTO
        MaterialPlanNeedReleaseMonthDTO.Service service = createMonthDTOService();
        // 采购计划列表
        MaterialPlanNeedReleaseMonthDTO.RollPredictionList rollPredictionList = new MaterialPlanNeedReleaseMonthDTO.RollPredictionList();
        List<MaterialPlanNeedReleaseMonthDTO.RollPrediction> rollPredictions = new ArrayList<>();

        int version = 1;
        for (Map.Entry<String, MaterialLongTermForecastVO> entry : materialLongTermForecastVOMap.entrySet()) {
            String key = entry.getKey();
            MaterialLongTermForecastVO value = entry.getValue();

            // 获取对应下发明细
            List<MaterialLongTermForecastIssueDetailVO> materialLongTermForecastIssueDetailVOS =
                    materialLongTermForecastIssueDetailVOMap.get(String.join("_", value.getSupplierCode(), versionCode));

            if (CollectionUtils.isNotEmpty(materialLongTermForecastIssueDetailVOS)) {

                // 获取明细里的版本号并+1
                version = materialLongTermForecastIssueDetailVOS.stream().sorted((vo1, vo2) -> Integer.compare(vo2.getIssueVersion(), vo1.getIssueVersion()))
                        .collect(Collectors.toList())
                        .get(0)
                        .getIssueVersion() + 1;
            }
            MaterialPlanNeedReleaseMonthDTO.RollPrediction rollPrediction = createRollPrediction(value, version, key, versionCode);
            rollPredictions.add(rollPrediction);
        }

        rollPredictionList.setRollPrediction(rollPredictions);

        // 设置要货数据
        MaterialPlanNeedReleaseMonthDTO.Request request = new MaterialPlanNeedReleaseMonthDTO.Request();
        request.setRollPredictionList(rollPredictionList);

        MaterialPlanNeedReleaseMonthDTO.Data data = new MaterialPlanNeedReleaseMonthDTO.Data();
        data.setRequest(request);
        // 将数据设置到服务中
        service.setData(data);

        // 创建最终的 DTO 对象
        MaterialPlanNeedReleaseMonthDTO materialPlanNeedReleaseMonthDTO = new MaterialPlanNeedReleaseMonthDTO();
        materialPlanNeedReleaseMonthDTO.setService(service);
        return materialPlanNeedReleaseMonthDTO;
    }

    /**
     * 创建并返回一个已赋固定值的Service对象
     *
     * @return MaterialPlanNeedReleaseMonthDTO.Service 返回一个初始化了Route信息的Service对象
     */
    private MaterialPlanNeedReleaseMonthDTO.Service createMonthDTOService() {
        MaterialPlanNeedReleaseMonthDTO.Service service = new MaterialPlanNeedReleaseMonthDTO.Service();

        MaterialPlanNeedReleaseMonthDTO.Route route = new MaterialPlanNeedReleaseMonthDTO.Route();
        route.setSerialNO("2024121203018000001");
        route.setServiceID("02003000000009");
        route.setSourceSysID("03018");
        route.setServiceTime("20241212152805");
        service.setRoute(route);

        return service;
    }

    /**
     * 根据传入的materialLongTermForecastVO对象创建并返回一个新的RollPrediction对象
     *
     * @param materialLongTermForecastVO 长期预测
     * @param issueNo                    下发单号
     * @param versionCode                下发明细版本
     * @return MaterialPlanNeedReleaseMonthDTO.RollPrediction  返回初始化的RollPrediction对象
     */
    private MaterialPlanNeedReleaseMonthDTO.RollPrediction createRollPrediction(MaterialLongTermForecastVO materialLongTermForecastVO,
                                                                                int version,
                                                                                String issueNo,
                                                                                String versionCode) {
        MaterialPlanNeedReleaseMonthDTO.RollPrediction rollPrediction = new MaterialPlanNeedReleaseMonthDTO.RollPrediction();
        if (Objects.nonNull(materialLongTermForecastVO)) {
            rollPrediction.setPredictionNo(String.join("_", materialLongTermForecastVO.getSupplierCode(), versionCode));
            rollPrediction.setOrgCode(materialLongTermForecastVO.getOrganizationId());
            rollPrediction.setOrgName(materialLongTermForecastVO.getStockPointName());
            rollPrediction.setSupplierCode(materialLongTermForecastVO.getSupplierCode());
            rollPrediction.setSupplierName(materialLongTermForecastVO.getSupplierName());
            rollPrediction.setItemCode(materialLongTermForecastVO.getProductCode());
            rollPrediction.setItemName(materialLongTermForecastVO.getProductName());
            rollPrediction.setRequireDate(materialLongTermForecastVO.getDemandDate() != null ?
                    DateUtils.dateToString(materialLongTermForecastVO.getDemandDate(), "yyyy-MM") :
                    DateUtils.dateToString(new Date(), "yyyy-MM"));
            rollPrediction.setRequireNum(materialLongTermForecastVO.getDemandQuantity() != null ?
                    String.valueOf(materialLongTermForecastVO.getDemandQuantity()) :
                    "0");
            rollPrediction.setVersion(String.valueOf(version));
            rollPrediction.setPublisher(SystemHolder.getStaffCode() != null ? SystemHolder.getStaffCode() : "默认");
            rollPrediction.setPublishDate(DateUtils.dateToString(new Date(), "yyyy-MM-dd"));
        }
        return rollPrediction;
    }

    /**
     * 记录长期预测下发日志
     *
     * @param insertRollPredictionMonthResultList 要货计划-长期预测返回结果
     * @param materialLongTermForecastVOMap       长期预测
     * @param state                               状态
     * @param versionCode                         下发明细版本
     */
    private void processMaterialPlanLong(List<InsertRollPredictionMonthResult> insertRollPredictionMonthResultList,
                                         Map<String, MaterialLongTermForecastVO> materialLongTermForecastVOMap,
                                         String state,
                                         String versionCode) {
        if (CollectionUtils.isEmpty(insertRollPredictionMonthResultList)) return;

        List<MaterialLongTermForecastIssueDetailDTO> addList = new ArrayList<>();

        for (InsertRollPredictionMonthResult result : insertRollPredictionMonthResultList) {
            String planCode = result.getPlanCode();
            String[] split = planCode.split("_");
            String key = String.join("_", result.getItemCode(), split[0], result.getRequireDate());
            MaterialLongTermForecastVO materialLongTermForecastVO = materialLongTermForecastVOMap.get(key);

            MaterialLongTermForecastIssueDetailDTO issueDetailDTO = new MaterialLongTermForecastIssueDetailDTO();
            BeanUtils.copyProperties(materialLongTermForecastVO, issueDetailDTO);
            issueDetailDTO.setIssueNo(result.getPlanCode());
            issueDetailDTO.setIssueVersion(result.getVersionNo());
            issueDetailDTO.setStatus(state);
            issueDetailDTO.setRemark(result.getMsg());
            addList.add(issueDetailDTO);
        }

        Lists.partition(addList, 1000).forEach(materialLongTermForecastIssueDetailService::doCreateBatch);
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MaterialLongTermForecastVO> invocation(List<MaterialLongTermForecastVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doSyncLongTermForecastData(List<NoGlassInventoryShiftDataVO> shiftDataList,
                                           List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftDetailVOList,
                                           Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap,
                                           Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                           Map<String, SupplierVO> supplierVOMapOfId, Map<String, SupplierVO> supplierVOMapOfCode,
                                           String demandPattern, List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS,
                                           String versionCode) {
        // 计算当前月第一天（只计算一次，避免在Stream中重复计算）
        Date monthFirstDay = DateUtils.getMonthFirstDay(new Date());

        Map<String, NoGlassInventoryShiftDataVO> shiftDataVOMap = shiftDataList.stream()
                .collect(Collectors.toMap(NoGlassInventoryShiftDataVO::getId, Function.identity()));

        Map<String, List<NoGlassInventoryShiftDetailVO>> detailGroup = noGlassInventoryShiftDetailVOList.stream()
                .collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId));
        Date date = new Date();
        String userId = SystemHolder.getUserId();
        List<MaterialLongTermForecastPO> insertList = new ArrayList<>();
        Map<String, Date> supplierMoveDateMap = new HashMap<>();
        for (Map.Entry<String, List<NoGlassInventoryShiftDetailVO>> entry : detailGroup.entrySet()) {
            List<NoGlassInventoryShiftDetailVO> details = entry.getValue();

            // 只保留当前月和之后的明细
            details = details.stream()
                    .filter(vo -> null != vo.getInventoryDate())
                    .filter(vo -> !vo.getInventoryDate().before(monthFirstDay))
                    .collect(Collectors.toList());

            NoGlassInventoryShiftDataVO shiftDataVO = shiftDataVOMap.get(entry.getKey());
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(shiftDataVO.getProductCode());
            if (null == materialSupplierPurchaseVO) {
                continue;
            }
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.get(materialSupplierPurchaseVO.getId());
            if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(supplierPurchaseRatioVOList)) {
                continue;
            }
            for (NoGlassInventoryShiftDetailVO shiftDetailVO : details) {
                if (null == shiftDetailVO.getPlanPurchase() || shiftDetailVO.getPlanPurchase().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 剩余计划采购量
                BigDecimal remainderPlanPurchase = shiftDetailVO.getPlanPurchase();
                // 按照供应商采购比例生成要货计划
                for (int i = 0; i < supplierPurchaseRatioVOList.size(); i++) {
                    SupplierPurchaseRatioVO supplierPurchaseRatioVO = supplierPurchaseRatioVOList.get(i);
                    // 采购比例
                    BigDecimal purchaseRatio;
                    if (null == supplierPurchaseRatioVO.getPurchaseRatio() || supplierPurchaseRatioVO.getPurchaseRatio().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    purchaseRatio = supplierPurchaseRatioVO.getPurchaseRatio();
                    // 获取供应商
                    SupplierVO supplierVO = supplierVOMapOfId.get(supplierPurchaseRatioVO.getSupplierId());
                    if (null == supplierVO || 0 == supplierVO.getPlanDisplayCycleMonth()) {
                        continue;
                    }
                    // 预测滚动周期
                    Date moveDate;
                    if (supplierMoveDateMap.containsKey(supplierVO.getId())) {
                        moveDate = supplierMoveDateMap.get(supplierVO.getId());
                    } else {
                        int planDisplayCycleMonth = supplierVO.getPlanDisplayCycleMonth();
                        moveDate = DateUtils.moveMonth(date, planDisplayCycleMonth);
                        supplierMoveDateMap.put(supplierVO.getId(), moveDate);
                    }

                    if (shiftDetailVO.getInventoryDate().compareTo(moveDate) > 0) {
                        continue;
                    }

                    BigDecimal planPurchase = shiftDetailVO.getPlanPurchase();
                    planPurchase = planPurchase.multiply(purchaseRatio);
                    // 最后一个供应商取剩余量，为0则并不生成
                    if ((i == supplierPurchaseRatioVOList.size() - 1 && remainderPlanPurchase.compareTo(BigDecimal.ZERO) == 0) ||
                            remainderPlanPurchase.compareTo(BigDecimal.ZERO) < 0) {
                        continue;
                    }
                    if (null != materialSupplierPurchaseVO.getPackageLot() && i != supplierPurchaseRatioVOList.size() - 1) {
                        // 按照包装批量向上取整
                        planPurchase = materialPlanNeedService.roundUpwards(planPurchase, materialSupplierPurchaseVO.getPackageLot());
                    }
                    if (i == supplierPurchaseRatioVOList.size() - 1) {
                        // 最后一家取剩余量
                        planPurchase = remainderPlanPurchase;
                    }
                    remainderPlanPurchase = remainderPlanPurchase.subtract(planPurchase);
                    // 生产中长期预测
                    MaterialLongTermForecastPO materialLongTermForecastPO = generateMaterialLongTermForecastPO(shiftDataVO.getProductCode(), shiftDetailVO.getInventoryDate(),
                            planPurchase, supplierVO, demandPattern, versionCode, userId, date);
                    insertList.add(materialLongTermForecastPO);
                }
            }
        }

        Map<String, List<MaterialLongTermForecastPO>> longTermForecastMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(insertList)) {
            longTermForecastMap = insertList.stream()
                    .collect(Collectors.groupingBy(item -> String.join("#",
                            item.getProductCode(),
                            item.getSupplierCode(),
                            DateUtils.dateToString(item.getDemandDate(), DateUtils.COMMON_DATE_STR3))));
        }

        // 只保留当前月和之后的明细
        inventoryShiftSupplyVOS = inventoryShiftSupplyVOS.stream()
                .filter(vo -> null != vo.getSupplyDate())
                .filter(vo -> !vo.getSupplyDate().before(monthFirstDay))
                .collect(Collectors.toList());

        // 遍历计划供应
        for (MaterialPlanInventoryShiftSupplyVO inventoryShiftSupplyVO : inventoryShiftSupplyVOS) {
            // 获取已发布采购数量
            String joinKey = String.join("#",
                    inventoryShiftSupplyVO.getProductCode(),
                    inventoryShiftSupplyVO.getSupplierCode(),
                    DateUtils.dateToString(inventoryShiftSupplyVO.getSupplyDate(), DateUtils.COMMON_DATE_STR3));

            if (longTermForecastMap.containsKey(joinKey)) {
                List<MaterialLongTermForecastPO> longTermForecastPOList = longTermForecastMap.get(joinKey);
                // 获取发布采购数量
                longTermForecastPOList.get(0).setDemandQuantity(longTermForecastPOList.get(0).getDemandQuantity().add(inventoryShiftSupplyVO.getSupplyQuantity()));
            } else {
                // 生成长期预测
                MaterialLongTermForecastPO materialLongTermForecastPO = generateMaterialLongTermForecastPO(
                        inventoryShiftSupplyVO.getProductCode(), inventoryShiftSupplyVO.getSupplyDate(), inventoryShiftSupplyVO.getSupplyQuantity(),
                        supplierVOMapOfCode.get(inventoryShiftSupplyVO.getSupplierCode()), demandPattern, versionCode, userId, date);
                insertList.add(materialLongTermForecastPO);
            }
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            List<String> productCodeList = insertList.stream().map(MaterialLongTermForecastPO::getProductCode).distinct().collect(Collectors.toList());
            // 根据物料删除数据
            materialLongTermForecastDao.deleteByProductCodeList(productCodeList);

            Lists.partition(insertList, 2000).forEach(item -> {
                materialLongTermForecastDao.insertBatchWithPrimaryKey(item);
            });
        }
    }

    private MaterialLongTermForecastPO generateMaterialLongTermForecastPO(String productCode, Date inventoryDate,
                                                                          BigDecimal demandQuantity, SupplierVO supplierVO,
                                                                          String demandPattern, String versionCode,
                                                                          String userId, Date date) {
        MaterialLongTermForecastPO materialLongTermForecastPO = new MaterialLongTermForecastPO();
        materialLongTermForecastPO.setProductCode(productCode);
        materialLongTermForecastPO.setDemandDate(inventoryDate);
        materialLongTermForecastPO.setDemandQuantity(demandQuantity);
        materialLongTermForecastPO.setSupplierId(null != supplierVO ? supplierVO.getId() : null);
        materialLongTermForecastPO.setSupplierCode(null != supplierVO ? supplierVO.getSupplierCode() : null);
        materialLongTermForecastPO.setDemandPattern(demandPattern);
        materialLongTermForecastPO.setInventoryShiftVersionCode(versionCode);
        materialLongTermForecastPO.setId(UUID.randomUUID().toString());
        materialLongTermForecastPO.setCreator(userId);
        materialLongTermForecastPO.setCreateTime(date);
        materialLongTermForecastPO.setModifier(userId);
        materialLongTermForecastPO.setModifyTime(date);
        return materialLongTermForecastPO;
    }

    @Override
    public PageInfo<MaterialLongTermForecastVO> selectByPage2(MaterialLongTermForecastParam materialLongTermForecastParam) {
        // 查询分页数据，根据productCode、supplierId分组
        Map<String, Object> params = new HashMap<>();
        long total;
        int pages;
        List<String> productCodeList;
        List<String> dateStrList;
        List<MaterialLongTermForecastVO> materialLongTermForecastVOList;
        List<MaterialLongTermForecastVO> list;
        if (StringUtils.isNotBlank(materialLongTermForecastParam.getForecastVersionId())) {
            // 查询发布版本表
            params.put("forecastVersionId", materialLongTermForecastParam.getForecastVersionId());
            PageHelper.startPage(materialLongTermForecastParam.getPageNum(), materialLongTermForecastParam.getPageSize());
            List<MaterialLongTermForecastPublishedVO> materialLongTermForecastPublishedVOList = materialLongTermForecastPublishedService.selectGroupByParams(params);
            PageInfo<MaterialLongTermForecastPublishedVO> pageInfo = new PageInfo<>(materialLongTermForecastPublishedVOList);
            if (CollectionUtils.isEmpty(materialLongTermForecastPublishedVOList)) {
                return null;
            }
            materialLongTermForecastVOList = materialLongTermForecastDomainService.getVOSByPublishedVOS(materialLongTermForecastPublishedVOList);
            total = pageInfo.getTotal();
            pages = pageInfo.getPages();
            productCodeList = materialLongTermForecastPublishedVOList.stream()
                    .map(MaterialLongTermForecastPublishedVO::getProductCode)
                    .distinct().collect(Collectors.toList());
            // 查询详细数据
            List<MaterialLongTermForecastPublishedVO> publishedVOList = materialLongTermForecastPublishedService.selectVOByParams(
                    ImmutableMap.of("productCodeList", productCodeList,
                            "demandPattern", materialLongTermForecastParam.getDemandPattern(),
                            "forecastVersionId", materialLongTermForecastParam.getForecastVersionId(),
                            "supplierCode", materialLongTermForecastParam.getSupplierCode()));
            list = materialLongTermForecastDomainService.getVOSByPublishedVOS(publishedVOList);
            // 获取时间长度
            dateStrList = materialLongTermForecastPublishedService.selectDistinctDemandDateStr();
        } else {
            params.put("demandPattern", materialLongTermForecastParam.getDemandPattern());
            params.put("productCode", materialLongTermForecastParam.getProductCode());
            params.put("supplierCode", materialLongTermForecastParam.getSupplierCode());
            params.put("supplierName", materialLongTermForecastParam.getSupplierName());
            PageHelper.startPage(materialLongTermForecastParam.getPageNum(), materialLongTermForecastParam.getPageSize());
            materialLongTermForecastVOList = materialLongTermForecastDao.selectGroupByParams(params);
            PageInfo<MaterialLongTermForecastVO> pageInfo = new PageInfo<>(materialLongTermForecastVOList);
            if (CollectionUtils.isEmpty(materialLongTermForecastVOList)) {
                return pageInfo;
            }
            total = pageInfo.getTotal();
            pages = pageInfo.getPages();
            productCodeList = materialLongTermForecastVOList.stream()
                    .map(MaterialLongTermForecastVO::getProductCode)
                    .distinct().collect(Collectors.toList());

            // 查询详细数据
            list = materialLongTermForecastDao.selectVOByParams(ImmutableMap.of("productCodeList", productCodeList,
                    "demandPattern", materialLongTermForecastParam.getDemandPattern(),
                    "supplierCode", materialLongTermForecastParam.getSupplierCode()));
            // 获取时间长度
            dateStrList = materialLongTermForecastDao.selectDistinctDemandDateStr();

        }

        // 对dateStrList排序
        Collections.sort(dateStrList);

        // 进行升序排序
        list = list.stream()
                .sorted(Comparator.comparing(MaterialLongTermForecastVO::getDemandDate))
                .collect(Collectors.toList());

        // 详情数据分组
        Map<String, List<MaterialLongTermForecastVO>> detailGroup = list.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getSupplierId())));
        for (MaterialLongTermForecastVO materialLongTermForecastVO : materialLongTermForecastVOList) {
            List<String> idList = new ArrayList<>();
            String joinKey = String.join("#", materialLongTermForecastVO.getProductCode(), materialLongTermForecastVO.getSupplierId());
            if (!detailGroup.containsKey(joinKey)) {
                continue;
            }
            List<MaterialLongTermForecastDetailVO> detailVOS = new ArrayList<>();
            List<MaterialLongTermForecastVO> termForecastVOS = detailGroup.get(joinKey);
            // 根据月份汇总数据
            Map<String, List<MaterialLongTermForecastVO>> termForecastGroup = termForecastVOS.stream()
                    .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandDate(), DateUtils.YEAR_MONTH)));
            for (Map.Entry<String, List<MaterialLongTermForecastVO>> entry : termForecastGroup.entrySet()) {
                MaterialLongTermForecastDetailVO materialLongTermForecastDetailVO = new MaterialLongTermForecastDetailVO();
                materialLongTermForecastDetailVO.setDemandDateStr(entry.getKey());
                List<MaterialLongTermForecastVO> value = entry.getValue();
                idList.addAll(value.stream().map(MaterialLongTermForecastVO::getId).collect(Collectors.toList()));
                BigDecimal demandQuantitySum = value.stream()
                        .map(MaterialLongTermForecastVO::getDemandQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                materialLongTermForecastDetailVO.setDemandQuantitySum(demandQuantitySum);
                detailVOS.add(materialLongTermForecastDetailVO);
            }

            materialLongTermForecastVO.setIdList(idList);
            materialLongTermForecastVO.setDateList(dateStrList);
            materialLongTermForecastVO.setDetailList(detailVOS);
            materialLongTermForecastVO.setInventoryShiftVersionCode(termForecastVOS.get(0).getInventoryShiftVersionCode());
            materialLongTermForecastVO.setProductName(termForecastVOS.get(0).getProductName());
            materialLongTermForecastVO.setStockPointCode(termForecastVOS.get(0).getStockPointCode());
            materialLongTermForecastVO.setStockPointName(termForecastVOS.get(0).getStockPointName());
            materialLongTermForecastVO.setSupplierCode(termForecastVOS.get(0).getSupplierCode());
            materialLongTermForecastVO.setSupplierName(termForecastVOS.get(0).getSupplierName());
            materialLongTermForecastVO.setCreateTime(termForecastVOS.stream()
                    .max(Comparator.comparing(MaterialLongTermForecastVO::getCreateTime))
                    .get().getCreateTime());
        }
        PageInfo<MaterialLongTermForecastVO> pageInfoResult = new PageInfo<>(materialLongTermForecastVOList);
        pageInfoResult.setTotal(total);
        pageInfoResult.setPages(pages);
        return pageInfoResult;
    }
}