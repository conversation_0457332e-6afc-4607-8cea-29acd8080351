package com.yhl.scp.mrp.inventory.service.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mrp.inventory.convertor.InventoryAlternativeRelationshipConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.InventoryAlternativeRelationshipDO;
import com.yhl.scp.mrp.inventory.domain.service.InventoryAlternativeRelationshipDomainService;
import com.yhl.scp.mrp.inventory.dto.InventoryAlternativeRelationshipDTO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryAlternativeRelationshipDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryAlternativeRelationshipPO;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.inventory.service.InventoryQuayDetailService;
import com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanInventoryShiftService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanVersionService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftVO;
import com.yhl.scp.mrp.substitutionRelationship.service.GlassSubstitutionRelationshipService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryAlternativeRelationshipServiceImpl</code>
 * <p>
 * 原片库存替代关系应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 10:25:16
 */
@Slf4j
@Service
public class InventoryAlternativeRelationshipServiceImpl extends AbstractService implements InventoryAlternativeRelationshipService {

    @Resource
    private InventoryAlternativeRelationshipDao inventoryAlternativeRelationshipDao;

    @Resource
    private InventoryAlternativeRelationshipDomainService inventoryAlternativeRelationshipDomainService;

    @Resource
    private InventoryQuayDetailService inventoryQuayDetailService;

    @Resource
    private MaterialPlanInventoryShiftService materialPlanInventoryShiftService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private GlassSubstitutionRelationshipService glassSubstitutionRelationshipService;

    private static final BigDecimal ZERO = BigDecimal.ZERO;
    private static final BigDecimal ONE = BigDecimal.ONE;

    @Override
    public BaseResponse<Void> doCreate(InventoryAlternativeRelationshipDTO inventoryAlternativeRelationshipDTO) {
        // 0.数据转换
        InventoryAlternativeRelationshipDO inventoryAlternativeRelationshipDO = InventoryAlternativeRelationshipConvertor.INSTANCE.dto2Do(inventoryAlternativeRelationshipDTO);
        InventoryAlternativeRelationshipPO inventoryAlternativeRelationshipPO = InventoryAlternativeRelationshipConvertor.INSTANCE.dto2Po(inventoryAlternativeRelationshipDTO);
        // 1.数据校验
        inventoryAlternativeRelationshipDomainService.validation(inventoryAlternativeRelationshipDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryAlternativeRelationshipPO);
        inventoryAlternativeRelationshipDao.insert(inventoryAlternativeRelationshipPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(InventoryAlternativeRelationshipDTO inventoryAlternativeRelationshipDTO) {
        // 0.数据转换
        InventoryAlternativeRelationshipDO inventoryAlternativeRelationshipDO = InventoryAlternativeRelationshipConvertor.INSTANCE.dto2Do(inventoryAlternativeRelationshipDTO);
        InventoryAlternativeRelationshipPO inventoryAlternativeRelationshipPO = InventoryAlternativeRelationshipConvertor.INSTANCE.dto2Po(inventoryAlternativeRelationshipDTO);
        // 1.数据校验
        inventoryAlternativeRelationshipDomainService.validation(inventoryAlternativeRelationshipDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryAlternativeRelationshipPO);
        inventoryAlternativeRelationshipDao.update(inventoryAlternativeRelationshipPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryAlternativeRelationshipDTO> list) {
        List<InventoryAlternativeRelationshipPO> newList = InventoryAlternativeRelationshipConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        // 相同替代关系，时间重叠校验


        List<InventoryAlternativeRelationshipPO> existData = inventoryAlternativeRelationshipDao.selectByParams(
                ImmutableMap.of("demandProductCode", list.get(0).getDemandProductCode()));
        existData.addAll(newList);

        Map<String, List<InventoryAlternativeRelationshipPO>> collect = existData.stream()
                .collect(Collectors.groupingBy(t -> String.join("&", t.getDemandProductCode(), t.getReplacedProductCode())));
        collect.forEach((k, v) -> {
            v.sort(Comparator.comparing(InventoryAlternativeRelationshipPO::getStartTime));
            if (v.size() == 1) {
                return;
            }
            // 校验开始时间和结束时间是否存在重叠
            for (int i = 0; i < v.size() - 1; i++) {
                Date startTime1 = v.get(i).getStartTime();
                Date endTime1 = v.get(i).getEndTime();

                Date startTime2 = v.get(i + 1).getStartTime();
                Date endTime2 = v.get(i + 1).getEndTime();

                if (endTime1.before(startTime2)) {
                    continue;
                }
                if (startTime1.after(endTime2)) {
                    continue;
                }
                throw new BusinessException(String.format("主料:{},替代料:{}替代关系时间段重叠", v.get(i).getDemandProductCode(), v.get(i).getReplacedProductCode()));
            }
        });

        inventoryAlternativeRelationshipDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryAlternativeRelationshipDTO> list) {
        List<InventoryAlternativeRelationshipPO> newList = InventoryAlternativeRelationshipConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryAlternativeRelationshipDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryAlternativeRelationshipDao.deleteBatch(idList);
        }
        return inventoryAlternativeRelationshipDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryAlternativeRelationshipVO selectByPrimaryKey(String id) {
        InventoryAlternativeRelationshipPO po = inventoryAlternativeRelationshipDao.selectByPrimaryKey(id);
        return InventoryAlternativeRelationshipConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_inventory_alternative_relationship")
    public List<InventoryAlternativeRelationshipVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_inventory_alternative_relationship")
    public List<InventoryAlternativeRelationshipVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<InventoryAlternativeRelationshipVO> dataList = inventoryAlternativeRelationshipDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryAlternativeRelationshipServiceImpl target = SpringBeanUtils.getBean(InventoryAlternativeRelationshipServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryAlternativeRelationshipVO> selectByParams(Map<String, Object> params) {
        List<InventoryAlternativeRelationshipPO> list = inventoryAlternativeRelationshipDao.selectByParams(params);
        return InventoryAlternativeRelationshipConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryAlternativeRelationshipVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_INVENTORY_ALTERNATIVE_RELATIONSHIP.getCode();
    }

    @Override
    public List<InventoryAlternativeRelationshipVO> invocation(List<InventoryAlternativeRelationshipVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }


    @Override
    public List<InventoryAlternativeRelationshipVO> materialShortageReplace(String productCode, String cuttingRatePercentage) {

        // 获取主料对应的物料信息
        NewProductStockPointVO masterProduct;
        List<NewProductStockPointVO> productStockPointVOList =
                newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("productCode", productCode));
        if (CollectionUtils.isNotEmpty(productStockPointVOList)) {
            masterProduct = productStockPointVOList.get(0);
        } else {
            masterProduct = getProductAttribute(productCode);
            if (null == masterProduct) {
                throw new BusinessException("物料截取失败，请检查物料格式是否正确");
            }
        }

        // 根据主料获取替代料物品数据（同颜色、厚度）因为是缺料所以小找大，分别比较主料和替代料的长和宽，并且相同的物料编码只保留一个
        FeignDynamicParam feignDynamicParam02 = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "product_length", "product_width"))
                .queryParam(ImmutableMap.of("productColor", masterProduct.getProductColor(), "productThickness", masterProduct.getProductThickness()))
                .build();
        List<NewProductStockPointVO> alternativeProductStockPointVOList = new ArrayList<>(newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam02).stream()
                .filter(data -> null != data.getProductLength() && null != data.getProductWidth())
                .filter(data -> masterProduct.getProductLength().compareTo(data.getProductLength()) <= 0 && masterProduct.getProductWidth().compareTo(data.getProductWidth()) <= 0)
                .collect(Collectors.toMap(
                        NewProductStockPointVO::getProductCode,
                        vo -> vo,
                        (existing, replacement) -> existing
                )).values());

        if (CollectionUtils.isEmpty(alternativeProductStockPointVOList)) {
            throw new BusinessException("未找到可用替代料");
        }

        // 替代料物料
        List<String> productCodes =
                alternativeProductStockPointVOList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        // 主料
        productCodes.add(productCode);
        // 获取原片库存推移数据
        List<MaterialPlanInventoryShiftVO> inventoryShiftVOList = getMaterialPlanInventoryShiftList(productCodes);

        // 毛坯
        NewProductStockPointVO blankProduct = new NewProductStockPointVO();
        // 成品
        NewProductStockPointVO finishedProduct = new NewProductStockPointVO();

        getBlankAndFinishedProduct(productCode, blankProduct, finishedProduct);

        // 筛选符合要求（同厚度、同颜色、不同规格）的替代料，并计算切裁率
        List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOList = alternativeProductStockPointVOList.stream()
                .map(alternativeProduct ->
                        buildInventoryAlternativeRelationshipVO(masterProduct, alternativeProduct, blankProduct, finishedProduct))
                .collect(Collectors.toList());

        // 过滤掉切裁率小于 cuttingRatePercentage  大于 1 的元素 以及替代料为主料本身的数据
        inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                .filter(data -> {
                    BigDecimal cuttingRate = data.getCuttingRate();
                    return cuttingRate != null && cuttingRate.compareTo(new BigDecimal(cuttingRatePercentage)) >= 0 &&
                            cuttingRate.compareTo(new BigDecimal("1")) <= 0;
                })
                .filter(data -> {
                    String replacedCode = data.getReplacedProductCode();
                    String masterCode = masterProduct.getProductCode();

                    // 如果两个代码完全相同，直接返回false
                    if (replacedCode.equals(masterCode)) {
                        return false;
                    }

                    // 如果masterCode的第三位是*，则进行特殊比较
                    if (masterCode.charAt(2) == '*') {

                        // 构建masterCode除去*的部分
                        String masterPrefix = masterCode.substring(0, 2);
                        String masterSuffix = masterCode.length() > 3 ? masterCode.substring(3) : "";

                        // 构建replacedCode对应的部分
                        String replacedPrefix = replacedCode.substring(0, 2);
                        String replacedSuffix = replacedCode.length() > 3 ? replacedCode.substring(3) : "";

                        // 比较除去第三位的其他部分
                        return !(masterPrefix.equals(replacedPrefix) && masterSuffix.equals(replacedSuffix));
                    }

                    // 否则直接比较两个代码
                    return true;
                })
                .collect(Collectors.toList());

        // 补充现有库存
        supplementExistingInventory(inventoryAlternativeRelationshipVOList);

        // 补充月需求量
        supplementMonthTotalDemand(inventoryAlternativeRelationshipVOList, inventoryShiftVOList);

        // 过滤掉现有库存为 0 或为空的元素
        inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                .filter(data -> !"0".equals(data.getExistingInventory()) && StringUtils.isNotEmpty(data.getExistingInventory()))
                .distinct().collect(Collectors.toList());

        // 按切裁率降序排序
        inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                .sorted(Comparator.comparing(InventoryAlternativeRelationshipVO::getCuttingRate).reversed())
                .collect(Collectors.toList());

        // 设置每个元素的优先级
        for (int i = 0; i < inventoryAlternativeRelationshipVOList.size(); i++) {
            inventoryAlternativeRelationshipVOList.get(i).setPriority(i + 1);
        }

        return inventoryAlternativeRelationshipVOList;
    }

    /**
     * 获取单耗
     *
     * @param alternativeProductStockPointVOList 替代料list
     * @param blankProduct                       毛坯
     * @param masterProduct                      主料
     * @return 单耗
     */
    private Map<String, BigDecimal> assignmentIoFactor(List<NewProductStockPointVO> alternativeProductStockPointVOList,
                                                       NewProductStockPointVO blankProduct,
                                                       NewProductStockPointVO masterProduct) {

        Map<String, BigDecimal> ioFactorMap = new HashMap<>();

        // 收集物料编码
        List<String> productCode = alternativeProductStockPointVOList.stream()
                .map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());

        if (Objects.nonNull(blankProduct) && StringUtils.isNotEmpty(blankProduct.getProductCode()))
            productCode.add(blankProduct.getProductCode());

        // 查询物品BOM明细
        List<ProductBomVO> productBomVOList =
                newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductCodes", productCode));

        Map<String, ProductBomVO> productBomVOMap =
                productBomVOList.stream().collect(Collectors.toMap(ProductBomVO::getIoProductCode, Function.identity(), (v1, v2) -> v1));

        // 添加替代料单耗
        alternativeProductStockPointVOList.forEach(data -> {
            // 1/(向下取整(替代原片的长/毛坯长) * 向下取整(替代原片的宽/毛坯宽) )
            BigDecimal result = BigDecimal.ONE.divide(
                    data.getProductLength().divide(blankProduct.getProductLength(), 0, RoundingMode.FLOOR)
                            .multiply(data.getProductWidth().divide(blankProduct.getProductWidth(), 0, RoundingMode.FLOOR)),
                    2, RoundingMode.HALF_UP);
            ioFactorMap.put(data.getProductCode(), result);
        });

        // 添加毛坯单耗（用毛坯单耗为准）
        ProductBomVO blankProductBomVO = productBomVOMap.get(blankProduct.getProductCode());
        if (Objects.nonNull(blankProductBomVO)) {
            ioFactorMap.put(blankProduct.getProductCode(), blankProductBomVO.getIoFactor());
        }

        // 有超过1的单号 视为1
        BigDecimal one = BigDecimal.ONE;
        for (Map.Entry<String, BigDecimal> entry : ioFactorMap.entrySet()) {
            if (entry.getValue().compareTo(one) > 0) {
                entry.setValue(one);
            }
        }
        return ioFactorMap;
    }

    /**
     * 获取毛坯 和 成品  物料基础数据
     *
     * @param productCode     主料编码
     * @param blankProduct    毛坯
     * @param finishedProduct 成品
     */
    @Override
    public void getBlankAndFinishedProductByBatch(String productCode,
                                                  NewProductStockPointVO blankProduct,
                                                  NewProductStockPointVO finishedProduct,
                                                  Map<String, List<ProductBomVO>> productBomGroup,
                                                  Map<String, ProductBomVersionVO> productBomVersionVOMap) {
        // 获取bom
        List<ProductBomVO> productBomVOList = productBomGroup.get(productCode);
        if (CollectionUtils.isEmpty(productBomVOList)){
            // bom没找到用自己本身
            blankProduct.setProductLength(new BigDecimal(productCode.substring(3, 7)));
            blankProduct.setProductWidth(new BigDecimal( productCode.substring(7, 11)));
            blankProduct.setProductCode(productCode);
            return;
        }
        List<ProductBomVersionVO> productBomVersionVOList = new ArrayList<>();
        List<String> productBomVersionIdList = productBomVOList.stream().map(ProductBomVO::getBomVersionId).distinct().collect(Collectors.toList());
        for (String bomVersionId : productBomVersionIdList) {
            if (productBomVersionVOMap.containsKey(bomVersionId)){
                productBomVersionVOList.add(productBomVersionVOMap.get(bomVersionId));
            }
        }

        if (CollectionUtils.isEmpty(productBomVersionVOList)){
            // bom没找到用自己本身
            blankProduct.setProductLength(new BigDecimal(productCode.substring(3, 7)));
            blankProduct.setProductWidth(new BigDecimal( productCode.substring(7, 11)));
            blankProduct.setProductCode(productCode);
            return;
        }

        // 毛坯
        List<ProductBomVersionVO> blankProductList = productBomVersionVOList.stream()
                .filter(data -> !data.getProductType().equals(ProductTypeEnum.FG.getCode())).collect(Collectors.toList());
        // 成品
        List<ProductBomVersionVO> finishedProductList = productBomVersionVOList.stream()
                .filter(data -> data.getProductType().equals(ProductTypeEnum.FG.getCode())).collect(Collectors.toList());

        // 处理逻辑：
        // 1. 优先处理既有毛坯又有成品的情况
        // 2. 其次处理只有成品的情况
        // 3. 最后处理只有毛坯的情况

        if (CollectionUtils.isNotEmpty(blankProductList) && CollectionUtils.isNotEmpty(finishedProductList)) {
            // 既有毛坯又有成品

            // 1. 处理毛坯
            Map<ProductBomVO, ProductBomVersionVO> dataMap = new HashMap<>();
            // 收集毛坯物料编码
            List<String> blankProductCodeList = blankProductList.stream()
                    .map(ProductBomVersionVO::getProductCode)
                    .distinct()
                    .collect(Collectors.toList());
            // 获取对应的bom明细
            List<ProductBomVO> blankProductBomList = getProductBomGlassSubstitutionByProductCodeList(blankProductCodeList, productBomGroup);
            // 获取毛坯物料的bom作为成品
            List<ProductBomVersionVO> blankProductBomVersionVOS = getProductBomVersionGlassSubstitutionByProductCodeList(blankProductCodeList, productBomGroup, productBomVersionVOMap);
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> blankProductBomVersionMap = blankProductBomVersionVOS.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            for (ProductBomVO productBomVO : blankProductBomList) {
                if (blankProductBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    dataMap.put(productBomVO, blankProductBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 2. 处理成品
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> finishedProductBomVersionMap = finishedProductList.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            // 没找到毛坯，那么就根据主料去明细中获取
            List<ProductBomVO> finishedProductBomList = getProductBomGlassSubstitutionByProductCodeList(Lists.newArrayList(productCode), productBomGroup);

            for (ProductBomVO productBomVO : finishedProductBomList) {
                if (finishedProductBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    dataMap.put(productBomVO, finishedProductBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 找出标准成品切裁率最小的组合
            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minDataEntry = dataMap.entrySet().stream()
                    .min(Comparator.comparing(entry -> calculateCuttingRate(entry.getKey(), entry.getValue())));

            // 赋值
            minDataEntry.ifPresent(entry -> BeanUtils.copyProperties(entry.getKey(), blankProduct));
            minDataEntry.ifPresent(entry -> BeanUtils.copyProperties(entry.getValue(), finishedProduct));
        } else if (CollectionUtils.isNotEmpty(finishedProductList)) {
            // 只有成品
            Map<ProductBomVO, ProductBomVersionVO> map = new HashMap<>();
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> productBomVersionMap = finishedProductList.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            // 没找到毛坯，那么就根据主料去明细中获取
            List<ProductBomVO> finishedProductBomList = getProductBomGlassSubstitutionByProductCodeList(Lists.newArrayList(productCode), productBomGroup);
            for (ProductBomVO productBomVO : finishedProductBomList) {
                if (productBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    map.put(productBomVO, productBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 找出标准毛坯切裁率最小的一对组合
            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minEntry = map.entrySet().stream()
                    .min(Comparator.comparing(entry -> calculateCuttingRate(entry.getKey(), entry.getValue())));

            minEntry.ifPresent(entry -> {
                BeanUtils.copyProperties(entry.getKey(), blankProduct);
                BeanUtils.copyProperties(entry.getValue(), finishedProduct);
            });
        } else if (CollectionUtils.isNotEmpty(blankProductList)) {
            // 如果是毛坯，需要找出对应的成品
            Map<ProductBomVO, ProductBomVersionVO> blankMap = new HashMap<>();
            // 收集毛坯物料编码
            List<String> blankProductCodeList =
                    blankProductList.stream().map(ProductBomVersionVO::getProductCode).distinct().collect(Collectors.toList());
            // 获取对应的bom明细
            List<ProductBomVO> blankProductBomList = getProductBomGlassSubstitutionByProductCodeList(blankProductCodeList, productBomGroup);
            // 获取毛坯物料的bom作为成品
            List<ProductBomVersionVO> productBomVersionVOS = getProductBomVersionGlassSubstitutionByProductCodeList(blankProductCodeList, productBomGroup, productBomVersionVOMap);
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> productBomVersionMap = productBomVersionVOS.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            for (ProductBomVO productBomVO : blankProductBomList) {
                if (productBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    blankMap.put(productBomVO, productBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 找出标准毛坯切裁率最小的一对组合
            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minEntry = blankMap.entrySet().stream()
                    .min(Comparator.comparing(entry -> calculateCuttingRate(entry.getKey(), entry.getValue())));

            minEntry.ifPresent(entry -> {
                BeanUtils.copyProperties(entry.getKey(), blankProduct);
                BeanUtils.copyProperties(entry.getValue(), finishedProduct);
            });
        }
    }

    /**
     * 获取毛坯 和 成品  物料基础数据
     *
     * @param productCode     主料编码
     * @param blankProduct    毛坯
     * @param finishedProduct 成品
     */
    private void getBlankAndFinishedProduct(String productCode,
                                            NewProductStockPointVO blankProduct,
                                            NewProductStockPointVO finishedProduct) {
        List<ProductBomVersionVO> productBomVersionVOList;
        // 获取bom
        // 如果productCode的第三位是*，则将其替换为B和T
        if (productCode.charAt(2) == '*') {
            StringBuilder sb = new StringBuilder(productCode);
            sb.setCharAt(2, 'B');
            productBomVersionVOList = getProductBomVersion(sb.toString());
            sb.setCharAt(2, 'T');
            productBomVersionVOList.addAll(getProductBomVersion(sb.toString()));
        } else {
            productBomVersionVOList = getProductBomVersion(productCode);
        }

        // 毛坯
        List<ProductBomVersionVO> blankProductList = productBomVersionVOList.stream()
                .filter(data -> !data.getProductType().equals(ProductTypeEnum.FG.getCode())).collect(Collectors.toList());
        // 成品
        List<ProductBomVersionVO> finishedProductList = productBomVersionVOList.stream()
                .filter(data -> data.getProductType().equals(ProductTypeEnum.FG.getCode())).collect(Collectors.toList());

        // 处理逻辑：
        // 1. 优先处理既有毛坯又有成品的情况
        // 2. 其次处理只有成品的情况
        // 3. 最后处理只有毛坯的情况

        if (CollectionUtils.isNotEmpty(blankProductList) && CollectionUtils.isNotEmpty(finishedProductList)) {
            // 既有毛坯又有成品

            // 1. 处理毛坯
            Map<ProductBomVO, ProductBomVersionVO> dataMap = new HashMap<>();
            // 收集毛坯物料编码
            List<String> blankProductCodeList = blankProductList.stream()
                    .map(ProductBomVersionVO::getProductCode)
                    .distinct()
                    .collect(Collectors.toList());
            // 获取对应的bom明细
            List<ProductBomVO> blankProductBomList = getProductBom(blankProductCodeList);
            // 获取毛坯物料的bom作为成品
            List<ProductBomVersionVO> blankProductBomVersionVOS = getProductBomVersion(blankProductCodeList);
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> blankProductBomVersionMap = blankProductBomVersionVOS.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            for (ProductBomVO productBomVO : blankProductBomList) {
                if (blankProductBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    dataMap.put(productBomVO, blankProductBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 2. 处理成品
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> finishedProductBomVersionMap = finishedProductList.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            // 没找到毛坯，那么就根据主料去明细中获取
            List<ProductBomVO> finishedProductBomList;

            if (productCode.charAt(2) == '*') {
                StringBuilder sb = new StringBuilder(productCode);
                sb.setCharAt(2, 'B');
                finishedProductBomList = getProductBom(sb.toString());
                if (CollectionUtils.isEmpty(finishedProductBomList)) {
                    sb.setCharAt(2, 'T');
                    finishedProductBomList = getProductBom(sb.toString());
                }
            } else {
                finishedProductBomList = getProductBom(productCode);
            }

            for (ProductBomVO productBomVO : finishedProductBomList) {
                if (finishedProductBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    dataMap.put(productBomVO, finishedProductBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 找出标准成品切裁率最小的组合
            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minDataEntry = dataMap.entrySet().stream()
                    .min(Comparator.comparing(entry -> calculateCuttingRate(entry.getKey(), entry.getValue())));

            // 赋值
            minDataEntry.ifPresent(entry -> BeanUtils.copyProperties(entry.getKey(), blankProduct));
            minDataEntry.ifPresent(entry -> BeanUtils.copyProperties(entry.getValue(), finishedProduct));
        } else if (CollectionUtils.isNotEmpty(finishedProductList)) {
            // 只有成品
            Map<ProductBomVO, ProductBomVersionVO> map = new HashMap<>();
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> productBomVersionMap = finishedProductList.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            // 没找到毛坯，那么就根据主料去明细中获取
            List<ProductBomVO> finishedProductBomList;
            if (productCode.charAt(2) == '*') {
                StringBuilder sb = new StringBuilder(productCode);
                sb.setCharAt(2, 'B');
                productCode = sb.toString();
                finishedProductBomList = getProductBom(productCode);
                if (CollectionUtils.isEmpty(finishedProductBomList)) {
                    sb.setCharAt(2, 'T');
                    productCode = sb.toString();
                    finishedProductBomList = getProductBom(productCode);
                }
            } else {
                finishedProductBomList = getProductBom(productCode);
            }

            for (ProductBomVO productBomVO : finishedProductBomList) {
                if (productBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    map.put(productBomVO, productBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 找出标准毛坯切裁率最小的一对组合
            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minEntry = map.entrySet().stream()
                    .min(Comparator.comparing(entry -> calculateCuttingRate(entry.getKey(), entry.getValue())));

            minEntry.ifPresent(entry -> {
                BeanUtils.copyProperties(entry.getKey(), blankProduct);
                BeanUtils.copyProperties(entry.getValue(), finishedProduct);
            });
        } else if (CollectionUtils.isNotEmpty(blankProductList)) {
            // 如果是毛坯，需要找出对应的成品
            Map<ProductBomVO, ProductBomVersionVO> blankMap = new HashMap<>();
            // 收集毛坯物料编码
            List<String> blankProductCodeList =
                    blankProductList.stream().map(ProductBomVersionVO::getProductCode).distinct().collect(Collectors.toList());
            // 获取对应的bom明细
            List<ProductBomVO> blankProductBomList = getProductBom(blankProductCodeList);
            // 获取毛坯物料的bom作为成品
            List<ProductBomVersionVO> productBomVersionVOS = getProductBomVersion(blankProductCodeList);
            // 根据bom的id分组
            Map<String, ProductBomVersionVO> productBomVersionMap = productBomVersionVOS.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            for (ProductBomVO productBomVO : blankProductBomList) {
                if (productBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    blankMap.put(productBomVO, productBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 找出标准毛坯切裁率最小的一对组合
            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minEntry = blankMap.entrySet().stream()
                    .min(Comparator.comparing(entry -> calculateCuttingRate(entry.getKey(), entry.getValue())));

            minEntry.ifPresent(entry -> {
                BeanUtils.copyProperties(entry.getKey(), blankProduct);
                BeanUtils.copyProperties(entry.getValue(), finishedProduct);
            });
        }

        // 未成功赋值成品或毛坯 抛异常
        if (null == blankProduct.getProductCode() || null == finishedProduct.getProductCode()) {
            log.info("毛坯明细{}", JSON.toJSONString(blankProductList));
            log.info("毛坯{}", JSON.toJSONString(blankProduct));
            log.info("成品明细{}", JSON.toJSONString(finishedProductList));
            log.info("成品{}", JSON.toJSONString(finishedProduct));
            throw new BusinessException("未找到成品或毛坯，无法计算切裁率");
        }
    }

    /**
     * 获取毛坯和成品物料基础数据
     *
     * @param productCodeList    主料编码
     * @param blankProductMap    毛坯
     * @param finishedProductMap 成品
     */
    @Override
    public void getBlankAndFinishedProduct(List<String> productCodeList,
                                           Map<String, NewProductStockPointVO> blankProductMap,
                                           Map<String, NewProductStockPointVO> finishedProductMap) {

        // 处理带*的物料
        List<String> filterProductCodeList = new ArrayList<>();
        for (String productCode : productCodeList) {
            if (productCode.charAt(2) == '*') {
                StringBuilder sb = new StringBuilder(productCode);
                sb.setCharAt(2, 'B');
                filterProductCodeList.add(sb.toString());
                sb.setCharAt(2, 'T');
                filterProductCodeList.add(sb.toString());
            } else {
                filterProductCodeList.add(productCode);
            }
        }
        productCodeList = filterProductCodeList;

        // 1. 根据物料获取BOM并分组
        Map<String, List<ProductBomVersionVO>> productBomVersionByBatchProduct =
                getProductBomVersionByBatchProduct(productCodeList);

        // 2. 区分毛坯和成品BOM
        // 区分出成品料 和 毛坯料，因为每个物料对应的BOM只有一种类型，不会出现一个物料的BOM上既是毛坯又是成品
        Map<String, List<ProductBomVersionVO>> blankBomMap = new HashMap<>();
        Map<String, List<ProductBomVersionVO>> finishedBomMap = new HashMap<>();
        classifyBomByType(productBomVersionByBatchProduct, blankBomMap, finishedBomMap);

        // 3. 处理毛坯BOM
        processBlankBomMap(blankBomMap, blankProductMap, finishedProductMap);

        // 4. 处理成品BOM
        processFinishedBomMap(finishedBomMap, finishedProductMap, blankProductMap);
    }

    /**
     * 根据BOM类型分类
     *
     * @param productBomVersionByBatchProduct 物料对应BOM
     * @param blankBomMap                     毛坯BOM
     * @param finishedBomMap                  成品BOM
     */
    private void classifyBomByType(Map<String, List<ProductBomVersionVO>> productBomVersionByBatchProduct,
                                   Map<String, List<ProductBomVersionVO>> blankBomMap,
                                   Map<String, List<ProductBomVersionVO>> finishedBomMap) {
        productBomVersionByBatchProduct.forEach((productCode, bomList) -> {
            if (!bomList.isEmpty()) {
                ProductBomVersionVO firstBom = bomList.get(0);
                if (firstBom.getProductType().equals(ProductTypeEnum.MPBL.getCode())) {
                    blankBomMap.put(productCode, bomList);
                } else if (firstBom.getProductType().equals(ProductTypeEnum.FG.getCode())) {
                    finishedBomMap.put(productCode, bomList);
                }
            }
        });
    }

    /**
     * 处理毛坯BOM映射
     *
     * @param blankBomMap        毛坯
     * @param blankProductMap    主料对应毛坯
     * @param finishedProductMap 主料对应成品
     */
    private void processBlankBomMap(Map<String, List<ProductBomVersionVO>> blankBomMap,
                                    Map<String, NewProductStockPointVO> blankProductMap,
                                    Map<String, NewProductStockPointVO> finishedProductMap) {
        if (blankBomMap.isEmpty()) return;

        // 收集毛坯Map中的所有BOM物料
        List<String> blankBomProductCodeList = blankBomMap.values().stream()
                .flatMap(List::stream).map(ProductBomVersionVO::getProductCode).distinct().collect(Collectors.toList());

        // 获取每个BOM物料对应的明细
        List<ProductBomVO> blankBomList = getProductBom(blankBomProductCodeList);
        Map<String, ProductBomVO> blankBomRelativeMap = blankBomList.stream()
                .collect(Collectors.toMap(ProductBomVO::getProductCode, Function.identity(), (v1, v2) -> v1));

        // 获取每个毛坯BOM对应的BOM
        Map<String, List<ProductBomVersionVO>> blankProductBomVersionByBatchProductMap =
                getProductBomVersionByBatchProduct(blankBomProductCodeList);

        for (Map.Entry<String, List<ProductBomVersionVO>> entry : blankBomMap.entrySet()) {
            // 构建毛坯与成品映射关系
            Map<ProductBomVO, ProductBomVersionVO> productBomMap = buildProductBomMap(entry.getValue(),
                    blankBomRelativeMap, blankProductBomVersionByBatchProductMap);

            // 找出切裁率最小的组合
            findAndSaveMinCuttingRateCombination(entry.getKey(), productBomMap, blankProductMap, finishedProductMap);
        }
    }

    /**
     * 处理成品BOM映射
     *
     * @param finishedBomMap     成品
     * @param blankProductMap    主料对应毛坯
     * @param finishedProductMap 主料对应成品
     */
    private void processFinishedBomMap(Map<String, List<ProductBomVersionVO>> finishedBomMap,
                                       Map<String, NewProductStockPointVO> finishedProductMap,
                                       Map<String, NewProductStockPointVO> blankProductMap) {
        if (finishedBomMap.isEmpty()) return;

        // 收集成品Map中的key
        List<String> finishedBomProductCodeList = new ArrayList<>(finishedBomMap.keySet());
        // 根据key获取明细，作为毛坯
        List<ProductBomVO> finishedBomList = getProductBom(finishedBomProductCodeList);
        // 根据物料分组，每个主料对应的毛坯List
        Map<String, List<ProductBomVO>> finishedBomRelativeMap = finishedBomList.stream()
                .collect(Collectors.groupingBy(ProductBomVO::getProductCode));

        for (Map.Entry<String, List<ProductBomVersionVO>> entry : finishedBomMap.entrySet()) {
            String key = entry.getKey();
            List<ProductBomVersionVO> value = entry.getValue();

            // 根据bom的id分组
            Map<String, ProductBomVersionVO> productBomVersionMap = value.stream()
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

            // 获取对应的毛坯列表
            List<ProductBomVO> finishedProductBomList = finishedBomRelativeMap.getOrDefault(key, Collections.emptyList());

            // 构建毛坯与成品映射关系
            Map<ProductBomVO, ProductBomVersionVO> productBomMap = new HashMap<>();
            for (ProductBomVO productBomVO : finishedProductBomList) {
                if (productBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
                    productBomMap.put(productBomVO, productBomVersionMap.get(productBomVO.getBomVersionId()));
                }
            }

            // 找出切裁率最小的组合
            findAndSaveMinCuttingRateCombination(key, productBomMap, blankProductMap, finishedProductMap);
        }
    }

    /**
     * 构建毛坯与成品的映射关系
     *
     * @param bomVersions          BOM
     * @param bomRelativeMap       毛坯对应BOM明细
     * @param productBomVersionMap 毛坯对应BOM
     * @return 毛坯对应成品
     */
    private Map<ProductBomVO, ProductBomVersionVO> buildProductBomMap(
            List<ProductBomVersionVO> bomVersions,
            Map<String, ProductBomVO> bomRelativeMap,
            Map<String, List<ProductBomVersionVO>> productBomVersionMap) {

        // 收集毛坯物料编码
        List<String> productCodeList = bomVersions.stream()
                .map(ProductBomVersionVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());

        // 获取毛坯对应的明细
        List<ProductBomVO> productBomList = productCodeList.stream()
                .map(bomRelativeMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 获取毛坯物料的bom作为成品
        List<ProductBomVersionVO> productBomVersionVOS = productCodeList.stream()
                .map(productBomVersionMap::get)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 根据bom的id分组
        Map<String, ProductBomVersionVO> versionMap = productBomVersionVOS.stream()
                .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));

        // 构建映射关系
        Map<ProductBomVO, ProductBomVersionVO> resultMap = new HashMap<>();
        for (ProductBomVO productBomVO : productBomList) {
            if (versionMap.containsKey(productBomVO.getBomVersionId())) {
                resultMap.put(productBomVO, versionMap.get(productBomVO.getBomVersionId()));
            }
        }

        return resultMap;
    }

    /**
     * 找出切裁率最小的组合并保存结果
     *
     * @param key                主料
     * @param productBomMap      物料BOM
     * @param blankProductMap    毛坯
     * @param finishedProductMap 成品
     */
    private void findAndSaveMinCuttingRateCombination(String key,
                                                      Map<ProductBomVO, ProductBomVersionVO> productBomMap,
                                                      Map<String, NewProductStockPointVO> blankProductMap,
                                                      Map<String, NewProductStockPointVO> finishedProductMap) {
        // 找出标准毛坯切裁率最小的一对组合
        Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minEntry = productBomMap.entrySet().stream()
                .min(Comparator.comparing(e -> calculateCuttingRate(e.getKey(), e.getValue())));

        minEntry.ifPresent(e -> {
            NewProductStockPointVO blankProduct = new NewProductStockPointVO();
            NewProductStockPointVO finishedProduct = new NewProductStockPointVO();

            BeanUtils.copyProperties(e.getKey(), blankProduct);
            BeanUtils.copyProperties(e.getValue(), finishedProduct);

            blankProductMap.put(key, blankProduct);
            finishedProductMap.put(key, finishedProduct);
        });
    }

    /**
     * 获取毛坯 和 成品  物料基础数据
     *
     * @param productCodeList     主料编码
     * @param blankProductMap    毛坯
     * @param finishedProductMap 成品
     */
//    private void getBlankAndFinishedProduct(List<String> productCodeList,
//                                            Map<String, NewProductStockPointVO> blankProductMap,
//                                            Map<String, NewProductStockPointVO> finishedProductMap) {
//
//        // 根据物料获取BOM明细再获取BOM，并 根据物料分组
//        Map<String, List<ProductBomVersionVO>> productBomVersionByBatchProduct =
//                getProductBomVersionByBatchProduct(productCodeList);
//
//        // 区分出成品料 和 毛坯料，因为每个物料对应的BOM只有一种类型，不会出现一个物料的BOM上既是毛坯又是成品
//        // 将productBomVersionByBatchProduct拆分为毛坯和成品两个Map
//        Map<String, List<ProductBomVersionVO>> blankBomMap = new HashMap<>();
//        Map<String, List<ProductBomVersionVO>> finishedBomMap = new HashMap<>();
//
//        productBomVersionByBatchProduct.forEach((productCode, bomList) -> {
//            // 每个物料的所有BOM版本的ProductType相同，取第一条判断即可
//            if (!bomList.isEmpty()) {
//                ProductBomVersionVO firstBom = bomList.get(0);
//                if (firstBom.getProductType().equals(ProductTypeEnum.MPBL.getCode())) {
//                    blankBomMap.put(productCode, bomList);
//                } else if (firstBom.getProductType().equals(ProductTypeEnum.FG.getCode())) {
//                    finishedBomMap.put(productCode, bomList);
//                }
//            }
//        });
//
//        // 处理毛坯Map
//        // 收集毛坯Map中的所有BOM物料
//        List<String> blankBomProductCodeList = blankBomMap.values().stream()
//                .flatMap(List::stream).map(ProductBomVersionVO::getProductCode).distinct().collect(Collectors.toList());
//        // 获取每个BOM物料对应的明细
//        List<ProductBomVO> blankBomList = getProductBom(blankBomProductCodeList);
//        // 根据物料分组，每个BOM物料对应的BOM明细
//        Map<String, ProductBomVO> blankBomRelativeMap = blankBomList.stream()
//                .collect(Collectors.toMap(ProductBomVO::getProductCode, Function.identity(), (v1, v2) -> v1));
//
//        // 获取每个毛坯BOM对应的BOM（把毛坯BOM作为明细再找一级）
//        Map<String, List<ProductBomVersionVO>> blankProductBomVersionByBatchProductMap =
//                getProductBomVersionByBatchProduct(blankBomProductCodeList);
//
//        for (Map.Entry<String, List<ProductBomVersionVO>> entry : blankBomMap.entrySet()) {
//            Map<ProductBomVO, ProductBomVersionVO> map = new HashMap<>();
//
//            String key = entry.getKey();
//            List<ProductBomVersionVO> value = entry.getValue();
//
//            // 收集毛坯物料编码
//            List<String> blankProductCodeList =
//                    value.stream().map(ProductBomVersionVO::getProductCode).distinct().collect(Collectors.toList());
//
//            // 获取毛坯对应的明细
//            List<ProductBomVO> blankProductBomList = blankProductCodeList.stream()
//                    .map(blankBomRelativeMap::get)
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
//
//            // 获取毛坯物料的bom作为成品
//            List<ProductBomVersionVO> productBomVersionVOS = blankProductCodeList.stream()
//                    .map(blankProductBomVersionByBatchProductMap::get)
//                    .flatMap(List::stream)
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toList());
//
//            // 根据bom的id分组
//            Map<String, ProductBomVersionVO> productBomVersionMap = productBomVersionVOS.stream()
//                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));
//
//            for (ProductBomVO productBomVO : blankProductBomList) {
//                if (productBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
//                    map.put(productBomVO, productBomVersionMap.get(productBomVO.getBomVersionId()));
//                }
//            }
//
//            // 找出标准毛坯切裁率最小的一对组合
//            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minEntry = map.entrySet().stream()
//                    .min(Comparator.comparing(e -> calculateCuttingRate(e.getKey(), e.getValue())));
//
//            minEntry.ifPresent(e -> {
//                NewProductStockPointVO blankProduct = new NewProductStockPointVO();
//                NewProductStockPointVO finishedProduct = new NewProductStockPointVO();
//
//                BeanUtils.copyProperties(e.getKey(), blankProduct);
//                BeanUtils.copyProperties(e.getValue(), finishedProduct);
//
//                blankProductMap.put(key,blankProduct);
//                finishedProductMap.put(key,finishedProduct);
//            });
//        }
//
//        // 处理成品Map
//        // 收集成品Map中的key
//        List<String> finishedBomProductCodeList = finishedBomMap.keySet().stream().distinct().collect(Collectors.toList());
//        // 根据key获取明细，作为毛坯（主料自己作为毛坯）
//        List<ProductBomVO> finishedBomList = getProductBom(finishedBomProductCodeList);
//
//        // 根据物料分组，每个主料对应的毛坯List
//        Map<String, List<ProductBomVO>> finishedBomRelativeMap = finishedBomList.stream()
//                .collect(Collectors.groupingBy(ProductBomVO::getProductCode));
//
//        for (Map.Entry<String, List<ProductBomVersionVO>> entry : finishedBomMap.entrySet()) {
//
//            Map<ProductBomVO, ProductBomVersionVO> map = new HashMap<>();
//
//            String key = entry.getKey();
//            List<ProductBomVersionVO> value = entry.getValue();
//
//            // 根据bom的id分组
//            Map<String, ProductBomVersionVO> productBomVersionMap = value.stream()
//                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v2));
//
//            // 没找到毛坯，那么就根据主料去明细中获取
//            List<ProductBomVO> finishedProductBomList = finishedBomRelativeMap.get(key);
//
//            for (ProductBomVO productBomVO : finishedProductBomList) {
//                if (productBomVersionMap.containsKey(productBomVO.getBomVersionId())) {
//                    map.put(productBomVO, productBomVersionMap.get(productBomVO.getBomVersionId()));
//                }
//            }
//
//            // 找出标准毛坯切裁率最小的一对组合
//            Optional<Map.Entry<ProductBomVO, ProductBomVersionVO>> minEntry = map.entrySet().stream()
//                    .min(Comparator.comparing(e -> calculateCuttingRate(e.getKey(), e.getValue())));
//
//            minEntry.ifPresent(e -> {
//                NewProductStockPointVO blankProduct = new NewProductStockPointVO();
//                NewProductStockPointVO finishedProduct = new NewProductStockPointVO();
//
//                BeanUtils.copyProperties(e.getKey(), blankProduct);
//                BeanUtils.copyProperties(e.getValue(), finishedProduct);
//
//                blankProductMap.put(key,blankProduct);
//                finishedProductMap.put(key,finishedProduct);
//            });
//        }
//    }

    /**
     * 计算标准毛坯切裁率（成品面积/毛坯面积/单耗）
     *
     * @param blank    毛坯
     * @param finished 成品
     * @return 标准毛坯切裁率
     */
    private BigDecimal calculateCuttingRate(ProductBomVO blank, ProductBomVersionVO finished) {

        if (null == blank.getProductArea()) {
            blank.setProductArea(blank.getProductLength().multiply(blank.getProductWidth()).divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP));
        }
        if (null == finished.getProductArea()) {
            finished.setProductArea(finished.getProductLength().multiply(finished.getProductWidth()).divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP));
        }
        // 单耗大于1  看做1 计算
        if (blank.getIoFactor().compareTo(BigDecimal.ONE) > 0) {
            blank.setIoFactor(BigDecimal.ONE);
        }
        if (blank.getProductArea().compareTo(BigDecimal.ZERO) > 0 && blank.getIoFactor().compareTo(BigDecimal.ZERO) > 0){
            return finished.getProductArea()
                    .divide(blank.getProductArea(), 3, RoundingMode.DOWN)
                    .divide(blank.getIoFactor(), 3, RoundingMode.DOWN);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 根据bom明细物料编码获取bom
     *
     * @param productCode 物料编码
     * @return bom
     */
    private List<ProductBomVersionVO> getProductBomVersion(String productCode) {
        // 查询物品BOM明细
        List<ProductBomVO> productBomVOList =
                newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductCode", productCode));

        if (CollectionUtils.isEmpty(productBomVOList)) return new ArrayList<>();

        // 根据BOM版本id分组
        Map<String, List<ProductBomVO>> productBomVOMap =
                productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
        // 收集所有的BOM版本
        Set<String> bomVersionIdSet = productBomVOMap.keySet();
        // 获取对应的BOM版本数据（过滤掉长宽为空 或者 面积为空 的数据）
        return newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", bomVersionIdSet))
                .stream()
                .filter(data -> (null != data.getProductLength() && null != data.getProductWidth()) || null != data.getProductArea())
                .collect(Collectors.toList());
    }


    /**
     * 根据bom明细物料编码获取bom
     *
     * @param productCodeList 物料编码集合
     * @return bom
     */
    private Map<String, List<ProductBomVersionVO>> getProductBomVersionByBatchProduct(List<String> productCodeList) {

        Map<String, List<ProductBomVersionVO>> result = new HashMap<>();
        // 查询物品BOM明细
        List<ProductBomVO> productBomVOList =
                newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductCodes", productCodeList));

        if (CollectionUtils.isEmpty(productBomVOList)) return result;

        Map<String, List<ProductBomVO>> productBomVOMapOfIoProductCode = productBomVOList.stream()
                .collect(Collectors.groupingBy(ProductBomVO::getIoProductCode));

        // 根据BOM版本id分组
        Map<String, List<ProductBomVO>> productBomVOMap =
                productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
        // 收集所有的BOM版本
        Set<String> bomVersionIdSet = productBomVOMap.keySet();
        // 获取对应的BOM版本数据
        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign
                .selectProductBomVersionVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", bomVersionIdSet)).stream()
                .filter(data -> (null != data.getProductLength() && null != data.getProductWidth()) || null != data.getProductArea())
                .collect(Collectors.toList());

        // 处理返回数据
        Map<String, ProductBomVersionVO> productBomVersionVOMapOfId = productBomVersionVOList.stream()
                .collect(Collectors.toMap(ProductBomVersionVO::getId, Function.identity()));
        for (String productCode : productCodeList) {
            List<ProductBomVO> bomVOList = productBomVOMapOfIoProductCode.get(productCode);
            if (CollectionUtils.isEmpty(bomVOList)){
                continue;
            }
            List<ProductBomVersionVO> list = new ArrayList<>();
            for (ProductBomVO productBomVO : bomVOList) {
                if (productBomVersionVOMapOfId.containsKey(productBomVO.getBomVersionId())){
                    list.add(productBomVersionVOMapOfId.get(productBomVO.getBomVersionId()));
                }
            }
            result.put(productCode, list);
        }
        return result;
    }

    /**
     * 根据bom明细物料编码获取bom
     *
     * @param productCodes 物料编码
     * @return bom
     */
    @Override
    public List<ProductBomVersionVO> getProductBomVersion(List<String> productCodes) {
        // 查询物品BOM明细
        List<ProductBomVO> productBomVOList =
                newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductCodes", productCodes));

        if (CollectionUtils.isEmpty(productBomVOList)) return new ArrayList<>();

        // 根据BOM版本id分组
        Map<String, List<ProductBomVO>> productBomVOMap =
                productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
        // 收集所有的BOM版本
        Set<String> bomVersionIdSet = productBomVOMap.keySet();
        // 获取对应的BOM版本数据
        return newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", bomVersionIdSet))
                .stream()
                .filter(data -> (null != data.getProductLength() && null != data.getProductWidth()) || null != data.getProductArea())
                .collect(Collectors.toList());
    }

    /**
     * 根据bom明细物料编码获取bom
     *
     * @param productCodes 物料编码
     * @return bom
     */
    public List<ProductBomVersionVO> getProductBomVersionGlassSubstitutionByProductCodeList(List<String> productCodes,
                                                                                            Map<String, List<ProductBomVO>> productBomVOMap,
                                                                                            Map<String, ProductBomVersionVO> productBomVersionVOMap) {
        // 查询物品BOM明细
        List<ProductBomVO> productBomVOList = new ArrayList<>();
        for (String productCode : productCodes) {
            if (productBomVOMap.containsKey(productCode)){
                productBomVOList.addAll(productBomVOMap.get(productCode));
            }
        }

        if (CollectionUtils.isEmpty(productBomVOList)) return new ArrayList<>();

        // 根据BOM版本id分组
        Map<String, List<ProductBomVO>> productBomVOMapOfBomVersionId =
                productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
        // 收集所有的BOM版本
        Set<String> bomVersionIdSet = productBomVOMapOfBomVersionId.keySet();
        // 获取对应的BOM版本数据
        List<ProductBomVersionVO> result = new ArrayList<>();
        for (String bomVersionId : bomVersionIdSet) {
            if (productBomVersionVOMap.containsKey(bomVersionId)){
                result.add(productBomVersionVOMap.get(bomVersionId));
            }
        }
        return result;
    }

    /**
     * 获取bom明细
     *
     * @param productCode 物料编码
     * @return bom明细
     */
    private List<ProductBomVO> getProductBom(String productCode) {
        // 查询物品BOM明细
        return newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductCode", productCode))
                .stream()
                .filter(data -> (null != data.getProductLength() && null != data.getProductWidth()) || null != data.getProductArea())
                .collect(Collectors.toList());
    }

    /**
     * 获取bom明细
     *
     * @param productCodes 物料编码
     * @return bom明细
     */
    private List<ProductBomVO> getProductBomGlassSubstitutionByProductCodeList(List<String> productCodes,
                                                                               Map<String, List<ProductBomVO>> productBomVOMap) {
        List<ProductBomVO> result = new ArrayList<>();
        for (String productCode : productCodes) {
            if (productBomVOMap.containsKey(productCode)) {
                result.addAll(productBomVOMap.get(productCode));
            }
        }
        return result;
    }

    /**
     * 获取bom明细
     *
     * @param productCodes 物料编码
     * @return bom明细
     */
    @Override
    public List<ProductBomVO> getProductBom(List<String> productCodes) {
        // 查询物品BOM明细
        return newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductCodes", productCodes))
                .stream()
                .filter(data -> (null != data.getProductLength() && null != data.getProductWidth()) || null != data.getProductArea())
                .collect(Collectors.toList());
    }

    /**
     * 获取成品物料基础数据
     *
     * @param productCode 毛坯编码
     * @return 成品物料
     */
    private NewProductStockPointVO getFinishedProduct(String productCode) {
        if (Objects.isNull(productCode)) return null;

        // 查询物品BOM明细
        List<ProductBomVO> productBomVOList =
                newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductCode", productCode));
        // 根据BOM版本id分组
        Map<String, List<ProductBomVO>> productBomVOMap =
                productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getBomVersionId));
        // 收集所有的BOM版本
        Set<String> bomVersionIdSet = productBomVOMap.keySet();
        // 获取对应的BOM版本数据
        List<ProductBomVersionVO> productBomVersionVOList =
                newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", bomVersionIdSet));
        // 获取成品料
        String finishedProductCode =
                productBomVersionVOList.stream().map(ProductBomVersionVO::getProductCode).distinct().findFirst().orElse(null);

        if (null == finishedProductCode) return null;

        // 查询产品工艺基础数据
        MdsProductStockPointBaseVO finishedProduct =
                newMdsFeign.selectProductStockPointBaseByParams(SystemHolder.getScenario(), ImmutableMap.of("productCode", finishedProductCode))
                        .stream()
                        .findFirst().orElse(null);

        if (null == finishedProduct) return null;

        NewProductStockPointVO newProductStockPointVO = new NewProductStockPointVO();
        BeanUtils.copyProperties(finishedProduct, newProductStockPointVO);
        return newProductStockPointVO;
    }

    @Override
    public List<InventoryAlternativeRelationshipVO> backlogReplace(String productCode, String cuttingRatePercentage) {

        // 获取主料对应的物料信息
        NewProductStockPointVO masterProduct;
        List<NewProductStockPointVO> productStockPointVOList =
                newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of("productCode", productCode));
        if (CollectionUtils.isNotEmpty(productStockPointVOList)) {
            masterProduct = productStockPointVOList.get(0);
        } else {
            masterProduct = getProductAttribute(productCode);
            if (null == masterProduct) {
                throw new BusinessException("物料截取失败，请检查物料格式是否正确");
            }
        }

        // 根据主料获取替代料物品数据（同颜色、厚度）因为是积压所以大找小，分别比较主料和替代料的长和宽，并且相同的物料编码只保留一个
        FeignDynamicParam feignDynamicParam02 = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code", "product_length", "product_width"))
                .queryParam(ImmutableMap.of("productColor", masterProduct.getProductColor(), "productThickness", masterProduct.getProductThickness()))
                .build();
        List<NewProductStockPointVO> alternativeProductStockPointVOList = new ArrayList<>(newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam02).stream()
                .filter(data -> null != data.getProductLength() && null != data.getProductWidth())
                .filter(data -> masterProduct.getProductLength().compareTo(data.getProductLength()) >= 0 && masterProduct.getProductWidth().compareTo(data.getProductWidth()) >= 0)
                .collect(Collectors.toMap(
                        NewProductStockPointVO::getProductCode,
                        vo -> vo,
                        (existing, replacement) -> existing
                )).values());

        if (CollectionUtils.isEmpty(alternativeProductStockPointVOList)) {
            throw new BusinessException("未找到可用替代料");
        }

        // 替代料物料
        List<String> productCodes =
                alternativeProductStockPointVOList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        // 主料
        productCodes.add(productCode);
        // 获取原片库存推移数据
        List<MaterialPlanInventoryShiftVO> inventoryShiftVOList = getMaterialPlanInventoryShiftList(productCodes);
        // 毛坯
        NewProductStockPointVO blankProduct = new NewProductStockPointVO();
        NewProductStockPointVO finishedProduct = new NewProductStockPointVO();

        getBlankAndFinishedProduct(productCode, blankProduct, finishedProduct);
        // 筛选符合要求（同厚度、同颜色、不同规格）的替代料，并计算切裁率
        List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOList = alternativeProductStockPointVOList.stream()
                .map(alternativeProduct ->
                        buildInventoryAlternativeRelationshipVO(masterProduct, alternativeProduct, blankProduct, finishedProduct))
                .collect(Collectors.toList());

        // 处理切裁率
        // 如果是积压，由于积压是大找小，所以找出的切裁率一定是大对比小的比例，所以需要用2减去当前的切裁率，求出小的所占大的比例
        // 如果是缺料则不用处理，因为缺料算出的直接值小所占大的比例
        inventoryAlternativeRelationshipVOList.forEach(data -> {
            BigDecimal cuttingRate = data.getCuttingRate();
            if (cuttingRate != null) {
                BigDecimal two = new BigDecimal("2.00");
                BigDecimal newResult = two.subtract(cuttingRate);
                data.setCuttingRate(newResult);
            }
        });

        // 过滤掉切裁率小于 cuttingRatePercentage 或大于 1 的元素 以及替代料为主料本身的数据
        inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                .filter(data -> {
                    BigDecimal cuttingRate = data.getCuttingRate();
                    return cuttingRate != null && cuttingRate.compareTo(new BigDecimal(cuttingRatePercentage)) >= 0 &&
                            cuttingRate.compareTo(new BigDecimal("1")) <= 0;
                })
                .filter(data -> {
                    String replacedCode = data.getReplacedProductCode();
                    String masterCode = masterProduct.getProductCode();

                    // 如果两个代码完全相同，直接返回false
                    if (replacedCode.equals(masterCode)) {
                        return false;
                    }

                    // 如果masterCode的第三位是*，则进行特殊比较
                    if (masterCode.charAt(2) == '*') {

                        // 构建masterCode除去*的部分
                        String masterPrefix = masterCode.substring(0, 2);
                        String masterSuffix = masterCode.length() > 3 ? masterCode.substring(3) : "";

                        // 构建replacedCode对应的部分
                        String replacedPrefix = replacedCode.substring(0, 2);
                        String replacedSuffix = replacedCode.length() > 3 ? replacedCode.substring(3) : "";

                        // 比较除去第三位的其他部分
                        return !(masterPrefix.equals(replacedPrefix) && masterSuffix.equals(replacedSuffix));
                    }

                    // 否则直接比较两个代码
                    return true;
                })
                .collect(Collectors.toList());

        // 补充现有库存
        supplementExistingInventory(inventoryAlternativeRelationshipVOList);

        // 补充月需求量
        supplementMonthTotalDemand(inventoryAlternativeRelationshipVOList, inventoryShiftVOList);

        // 过滤掉现有库存为 0 或为空的元素
        inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                .filter(data -> !"0".equals(data.getExistingInventory()) && StringUtils.isNotEmpty(data.getExistingInventory()))
                .distinct().collect(Collectors.toList());

        // 按切裁率降序排序
        inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                .sorted(Comparator.comparing(InventoryAlternativeRelationshipVO::getCuttingRate).reversed())
                .collect(Collectors.toList());

        // 设置每个元素的优先级
        for (int i = 0; i < inventoryAlternativeRelationshipVOList.size(); i++) {
            inventoryAlternativeRelationshipVOList.get(i).setPriority(i + 1);
        }

        return inventoryAlternativeRelationshipVOList;
    }

    @Override
    public Map<String, List<InventoryAlternativeRelationshipVO>> batchBacklogReplace(List<String> productCodeList,
                                                                                     String cuttingRatePercentage,
                                                                                     Map<String, ProductBomVersionVO> productBomVersionVOMap,
                                                                                     Map<String, List<ProductBomVO>> productBomGroup) {
        Map<String, List<InventoryAlternativeRelationshipVO>> result = new HashMap<>();
        List<NewProductStockPointVO> masterProductList = new ArrayList<>();
        List<NewProductStockPointVO> substituteProductList = new ArrayList<>();
        for (String productCode : productCodeList) {
            NewProductStockPointVO masterProduct = getProductAttribute(productCode);
            if (null == masterProduct) {
                throw new BusinessException("物料截取失败，请检查物料格式是否正确" + productCode);
            }
            masterProductList.add(masterProduct);
        }

        // 替代料需要从库存批次明细里面找
        List<String> substituteProductListByInventory = glassSubstitutionRelationshipService.getSubstituteProductListByInventory();
        if (CollectionUtils.isEmpty(substituteProductListByInventory)){
            throw new BusinessException("未找到可用替代料");
        }
        // 截取替代料的明细数据
        for (String substituteProductCode : substituteProductListByInventory) {
            NewProductStockPointVO substituteProduct = getProductAttribute(substituteProductCode);
            if (null == substituteProduct) {
                throw new BusinessException("物料截取失败，请检查物料格式是否正确" + substituteProductCode);
            }
            substituteProductList.add(substituteProduct);
        }
        // 按照颜色和厚度分组
        Map<String, List<NewProductStockPointVO>> productStockPointVOMap = substituteProductList.stream()
                .collect(Collectors.groupingBy(
                        data -> data.getProductColor() + "_" + data.getProductThickness()));

        for (NewProductStockPointVO masterProduct : masterProductList) {
            String productCode = masterProduct.getProductCode();
            List<NewProductStockPointVO> list = productStockPointVOMap.get(masterProduct.getProductColor() + "_" + masterProduct.getProductThickness());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }

            List<NewProductStockPointVO> alternativeProductStockPointVOList = new ArrayList<>(list.stream()
                    .filter(data ->
                            data.getProductLength() != null &&
                            data.getProductWidth() != null &&
                            masterProduct.getProductLength().compareTo(data.getProductLength()) <= 0 &&
                            masterProduct.getProductWidth().compareTo(data.getProductWidth()) <= 0)
                    .collect(Collectors.toMap(
                            NewProductStockPointVO::getProductCode,
                            vo -> vo,
                            (existing, replacement) -> existing
                    )).values());
            if (CollectionUtils.isEmpty(alternativeProductStockPointVOList)) {
                continue;
            }

            // 替代料物料
            List<String> productCodes =
                    alternativeProductStockPointVOList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
            // 主料
            productCodes.add(productCode);

            // 毛坯
            NewProductStockPointVO blankProduct = new NewProductStockPointVO();
            NewProductStockPointVO finishedProduct = new NewProductStockPointVO();

            getBlankAndFinishedProductByBatch(productCode, blankProduct, finishedProduct, productBomGroup, productBomVersionVOMap);
            // 未成功赋值成品或毛坯 抛异常
            if (null == blankProduct.getProductCode() ||
                null == blankProduct.getProductWidth() ||
                null == blankProduct.getProductLength() ||
                null == finishedProduct.getProductCode()) {
                continue;
            }

            // 筛选符合要求（同厚度、同颜色、不同规格）的替代料，并计算切裁率
            List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOList = alternativeProductStockPointVOList.stream()
                    .map(alternativeProduct ->
                            buildInventoryAlternativeRelationshipVO(masterProduct, alternativeProduct, blankProduct, finishedProduct))
                    .collect(Collectors.toList());

            // 过滤掉切裁率小于 cuttingRatePercentage 或大于 1 的元素 以及替代料为主料本身的数据
            inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                    .filter(data -> {
                        BigDecimal cuttingRate = data.getCuttingRate();
                        return cuttingRate != null && cuttingRate.compareTo(new BigDecimal(cuttingRatePercentage)) >= 0 && cuttingRate.compareTo(BigDecimal.ONE) <= 0;
                    })
                    .filter(data -> !data.getReplacedProductCode().equals(masterProduct.getProductCode()))
                    .collect(Collectors.toList());

            // 按切裁率降序排序
            inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream()
                    .sorted(Comparator.comparing(InventoryAlternativeRelationshipVO::getCuttingRate).reversed())
                    .collect(Collectors.toList());

            // 设置每个元素的优先级
            for (int i = 0; i < inventoryAlternativeRelationshipVOList.size(); i++) {
                inventoryAlternativeRelationshipVOList.get(i).setPriority(i + 1);
            }
            // inventoryAlternativeRelationshipVOList只获取10条
            inventoryAlternativeRelationshipVOList = inventoryAlternativeRelationshipVOList.stream().limit(10).collect(Collectors.toList());
            result.put(productCode, inventoryAlternativeRelationshipVOList);
        }
        return result;
    }


    /**
     * 补充月需求量
     *
     * @param inventoryAlternativeRelationshipVOList 库存替代关系
     * @param inventoryShiftVOList                   库存推移
     */
    private void supplementMonthTotalDemand(List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOList,
                                            List<MaterialPlanInventoryShiftVO> inventoryShiftVOList) {
        for (InventoryAlternativeRelationshipVO inventoryVO : inventoryAlternativeRelationshipVOList) {
            String replacedProductCode = inventoryVO.getReplacedProductCode();
            // 过滤出当前产品代码的库存推移数据
            List<MaterialPlanInventoryShiftVO> productShiftList = inventoryShiftVOList.stream()
                    .filter(shift -> replacedProductCode.equals(shift.getProductCode()))
                    .collect(Collectors.toList());

            // 计算当月需求量
            BigDecimal currentMonthTotalDemand = sumDemandForMonth(productShiftList, 0);
            // 计算次月需求量
            BigDecimal nextMonthTotalDemand = sumDemandForMonth(productShiftList, 1);

            inventoryVO.setCurrentMonthTotalDemand(currentMonthTotalDemand);
            inventoryVO.setNextMonthTotalDemand(nextMonthTotalDemand);
        }
    }

    /**
     * 补充现有库存（通过码头库存批次明细补充）
     *
     * @param inventoryAlternativeRelationshipVOList 库存替代关系
     */
    private void supplementExistingInventory(List<InventoryAlternativeRelationshipVO> inventoryAlternativeRelationshipVOList) {
        // 替代料编码汇总
        List<String> replacedProductCodeList = inventoryAlternativeRelationshipVOList.stream().map(InventoryAlternativeRelationshipVO::getReplacedProductCode).distinct().collect(Collectors.toList());
        // 获取相应本厂数据(实时库存)
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams02(ImmutableMap.of("productCodes", replacedProductCodeList));
        Map<String, List<InventoryBatchDetailVO>> productCodeInventoryBatchDetaiMap = inventoryBatchDetailVOList.stream().collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        // 获取相应码头数据
        List<InventoryQuayDetailVO> inventoryQuayDetailVOList = inventoryQuayDetailService.selectByParams(ImmutableMap.of("productCodes", replacedProductCodeList));
        Map<String, List<InventoryQuayDetailVO>> productCodeInventoryQuayDetailMap = inventoryQuayDetailVOList.stream().collect(Collectors.groupingBy(InventoryQuayDetailVO::getProductCode));


        // 补充码头库存数量
        for (InventoryAlternativeRelationshipVO inventoryAlternativeRelationshipVO : inventoryAlternativeRelationshipVOList) {
            String replacedProductCode = inventoryAlternativeRelationshipVO.getReplacedProductCode();
            BigDecimal sumBc = ZERO;
            BigDecimal sumMt = ZERO;

            // 本厂库存（实时库存）累加
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = productCodeInventoryBatchDetaiMap.get(replacedProductCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                //  现有量进行求和
                sumBc = sumBc.add(inventoryBatchDetailVOS.stream()
                        .map(data -> new BigDecimal(data.getCurrentQuantity()))
                        .reduce(ZERO, BigDecimal::add));
            }

            // 码头库存累加
            List<InventoryQuayDetailVO> inventoryQuayDetailVOS = productCodeInventoryQuayDetailMap.get(replacedProductCode);
            if (CollectionUtils.isNotEmpty(inventoryQuayDetailVOS)) {
                //  actualSentQuantity 乘以 box 之后的结果进行求和
                sumMt = sumMt.add(inventoryQuayDetailVOS.stream()
                        .map(data -> data.getActualSentQuantity().multiply(data.getBox()))
                        .reduce(ZERO, BigDecimal::add));
            }

            // 将结果保留为整数
            sumBc = sumBc.setScale(0, RoundingMode.HALF_UP);
            sumMt = sumMt.setScale(0, RoundingMode.HALF_UP);
            inventoryAlternativeRelationshipVO.setExistingInventory(String.valueOf(sumBc));
            inventoryAlternativeRelationshipVO.setExistingInventoryMt(String.valueOf(sumMt));
        }
    }

    /**
     * 构建<InventoryAlternativeRelationshipVO>
     *
     * @param masterProduct      主料
     * @param alternativeProduct 替代料
     * @param blankProduct       毛坯料
     * @param finishedProduct    成品料
     * @return InventoryAlternativeRelationshipVO
     */
    private InventoryAlternativeRelationshipVO buildInventoryAlternativeRelationshipVO(NewProductStockPointVO masterProduct,
                                                                                       NewProductStockPointVO alternativeProduct,
                                                                                       NewProductStockPointVO blankProduct,
                                                                                       NewProductStockPointVO finishedProduct) {

        // 缺料   小（主料）找大（替代料），先用大的料，大替小，替代料替换掉主料
        // 积压   大（主料）找小（替代料），先用大的料，大替小，替代料替换掉主料
        // 注意替代关系里只分需求料和替代料（A替代B，那么A是替代料B是需求料）
        InventoryAlternativeRelationshipVO vo = new InventoryAlternativeRelationshipVO();
        // 替代料 （替代料）
        vo.setReplacedProductCode(alternativeProduct.getProductCode());
        vo.setProductName(alternativeProduct.getProductName());
        // 需求料（主料）
        vo.setDemandProductCode(masterProduct.getProductCode());
        // 计算切裁率
        BigDecimal cuttingRate = cuttingMaterialShortage(masterProduct, alternativeProduct, finishedProduct, blankProduct, vo);

        vo.setCuttingRate(cuttingRate);
        vo.setReplacedProductColor(alternativeProduct.getProductColor());
        vo.setReplacedProductThickness(alternativeProduct.getProductThickness() != null ? alternativeProduct.getProductThickness().setScale(1, RoundingMode.DOWN) : null);

        // 维护毛坯规格
        vo.setBlankProductWidth(null != blankProduct.getProductWidth() ? blankProduct.getProductWidth().setScale(0, RoundingMode.DOWN) : null);
        vo.setBlankProductLength(null != blankProduct.getProductLength() ? blankProduct.getProductLength().setScale(0, RoundingMode.DOWN) : null);
        return vo;
    }

    /**
     * 切裁率计算
     *
     * @param masterProduct      主料
     * @param alternativeProduct 替代料
     * @param finishedProduct    成品料
     * @param blankProduct       毛坯料
     * @return 缺料切裁率
     */
    private BigDecimal cuttingMaterialShortage(NewProductStockPointVO masterProduct,
                                               NewProductStockPointVO alternativeProduct,
                                               NewProductStockPointVO finishedProduct,
                                               NewProductStockPointVO blankProduct,
                                               InventoryAlternativeRelationshipVO vo) {
        BigDecimal cuttingRate;

        if (null == alternativeProduct.getProductArea()) {
            alternativeProduct.setProductArea(alternativeProduct.getProductLength()
                    .multiply(alternativeProduct.getProductWidth())
                    .divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP));
        }
        if (null == finishedProduct.getProductArea()) {
            finishedProduct.setProductArea(finishedProduct.getProductLength()
                    .multiply(finishedProduct.getProductWidth())
                    .divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP));
        }
        if (null == blankProduct.getProductArea()) {
            blankProduct.setProductArea(blankProduct.getProductLength()
                    .multiply(blankProduct.getProductWidth())
                    .divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP));
        }
        if (blankProduct.getProductArea().compareTo(BigDecimal.ZERO) == 0){
            return null;
        }

        // 替代料面积
        BigDecimal alternativeArea = alternativeProduct.getProductArea();

        // 替代料单耗    1/(向下取整(替代原片的长/毛坯长) * 向下取整(替代原片的宽/毛坯宽) )
        BigDecimal denominator = alternativeProduct.getProductLength()
                .divide(blankProduct.getProductLength(), 0, RoundingMode.FLOOR)
                .multiply(alternativeProduct.getProductWidth()
                        .divide(blankProduct.getProductWidth(), 0, RoundingMode.FLOOR));

        BigDecimal alternativeIoFactor;
        if (denominator.compareTo(BigDecimal.ZERO) == 0) {
            // 处理除数为零的情况，这里返回 0 作为默认值
            alternativeIoFactor = BigDecimal.ONE;
        } else {
            // 正常进行除法运算
            alternativeIoFactor = BigDecimal.ONE.divide(denominator, 3, RoundingMode.DOWN);
        }

        // 单耗大于1 按1使用
        if (alternativeIoFactor.compareTo(BigDecimal.ONE) > 0) {
            alternativeIoFactor = BigDecimal.ONE;
        }

        // 成品面积
        BigDecimal finishedArea = finishedProduct.getProductArea();
        // 毛坯面积
        BigDecimal blankArea = blankProduct.getProductArea();
        // 毛坯单耗
        BigDecimal blankIoFactor = (null != blankProduct.getIoFactor())
                ? blankProduct.getIoFactor() : BigDecimal.ONE;
        // 标准毛坯切裁率（成品面积 / 毛坯面积 / 毛坯单耗）
        BigDecimal blankCuttingRate = finishedArea.divide(blankArea, 3, RoundingMode.DOWN)
                .divide(blankIoFactor, 3, RoundingMode.DOWN);

        // 切裁率（毛坯面积 / 替代料面积 / 替代料单耗 * 标准毛坯切裁率）
        cuttingRate = blankArea.divide(alternativeArea, 3, RoundingMode.DOWN)
                .divide(alternativeIoFactor, 3, RoundingMode.DOWN)
                .multiply(blankCuttingRate);
        // 额外增加一次精度设置，确保最终结果为三位小数
        cuttingRate = cuttingRate.setScale(3, RoundingMode.DOWN);
        vo.setBlankInputFactor(blankIoFactor);
        vo.setGlassInputFactor(denominator);
        return cuttingRate;
    }

    /**
     * 获取库存推移数据
     *
     * @return 最新版本原片库存推移
     */
    private List<MaterialPlanInventoryShiftVO> getMaterialPlanInventoryShiftList(List<String> productCodes) {
        // 获取对应版本的库存推移数据列表
        List<MaterialPlanInventoryShiftVO> inventoryShiftList = materialPlanInventoryShiftService.selectByParams(ImmutableMap.of("productCodes", productCodes));
        if (CollectionUtils.isEmpty(inventoryShiftList)) {
            return Collections.emptyList();
        }
        return inventoryShiftList;
    }


    @Override
    public List<InventoryAlternativeRelationshipVO> select4Mrp() {
        return inventoryAlternativeRelationshipDao.select4Mrp();
    }

    @Override
    public List<InventoryAlternativeRelationshipVO> selectVOByParams(Map<String, Object> params) {
        return inventoryAlternativeRelationshipDao.selectVOByParams(params);
    }


    /**
     * 汇总指定月份偏移量的毛需求
     *
     * @param materialPlanInventoryShiftVOList 物料计划库存推移数据列表
     * @param monthOffset                      月份偏移量，0 表示当月，1 表示次月
     * @return 该月份的总毛需求
     */
    private static BigDecimal sumDemandForMonth(List<MaterialPlanInventoryShiftVO> materialPlanInventoryShiftVOList, int monthOffset) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, monthOffset);

        Date monthStart = getMonthStart(calendar);
        Date monthEnd = getMonthEnd(calendar);

        return materialPlanInventoryShiftVOList.stream()
                .filter(item -> {
                    Date inventoryDate = item.getInventoryDate();
                    return inventoryDate != null && !inventoryDate.before(monthStart) && !inventoryDate.after(monthEnd);
                })
                .map(MaterialPlanInventoryShiftVO::getDemandQuantity)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取指定日期所在月份的第一天
     *
     * @param calendar 日历对象
     * @return 月份的第一天日期
     */
    private static Date getMonthStart(Calendar calendar) {
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定日期所在月份的最后一天
     *
     * @param calendar 日历对象
     * @return 月份的最后一天日期
     */
    private static Date getMonthEnd(Calendar calendar) {
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 根据物料（原片）编码 截取获取物料属性
     *
     * @param productCode
     * @return
     */
    public static NewProductStockPointVO getProductAttribute(String productCode) {
        if (productCode == null || productCode.length() < 12) {
            return null;
        }
        String trimmed = productCode.substring(3);
        BigDecimal productLength = new BigDecimal(trimmed.substring(0, 4));
        BigDecimal productWidth = new BigDecimal(trimmed.substring(4, 8));
        BigDecimal productThickness = new BigDecimal(trimmed.substring(9, 10) + "." + trimmed.substring(10, 11));
        String productColor = trimmed.substring(12);

        return NewProductStockPointVO.builder()
                .productCode(productCode)
                .productLength(productLength)
                .productWidth(productWidth)
                .productThickness(productThickness)
                .productColor(productColor).build();
    }

    @Override
    public List<InventoryAlternativeRelationshipVO> getInventoryAlternativeRelationshipVO(Map<String, List<String>> rawProductCodeToSubstituteProductCode,
                                                                                          Map<String, ProductBomVersionVO> productBomVersionVOMap,
                                                                                          Map<String, List<ProductBomVO>> productBomGroup) {
        List<InventoryAlternativeRelationshipVO> result = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : rawProductCodeToSubstituteProductCode.entrySet()) {
            List<NewProductStockPointVO> alternativeProductStockPointVOList = new ArrayList<>();
            for (String substituteProductCode : entry.getValue()) {
                NewProductStockPointVO substituteProductAttribute = getProductAttribute(substituteProductCode);
                if(null != substituteProductAttribute){
                    alternativeProductStockPointVOList.add(substituteProductAttribute);
                }
            }
            NewProductStockPointVO masterProduct = getProductAttribute(entry.getKey());
            // 毛坯
            NewProductStockPointVO blankProduct = new NewProductStockPointVO();
            NewProductStockPointVO finishedProduct = new NewProductStockPointVO();

            getBlankAndFinishedProductByBatch(entry.getKey(), blankProduct, finishedProduct, productBomGroup, productBomVersionVOMap);
            // 未成功赋值成品或毛坯 抛异常
            if (null == blankProduct.getProductCode() ||
                    null == blankProduct.getProductWidth() ||
                    null == blankProduct.getProductLength() ||
                    null == finishedProduct.getProductCode()) {
                continue;
            }


            // 筛选符合要求（同厚度、同颜色、不同规格）的替代料，并计算切裁率
            result.addAll(alternativeProductStockPointVOList.stream()
                    .map(alternativeProduct ->
                            buildInventoryAlternativeRelationshipVO(masterProduct, alternativeProduct, blankProduct, finishedProduct))
                    .collect(Collectors.toList()));
        }
        return result;
    }
}
