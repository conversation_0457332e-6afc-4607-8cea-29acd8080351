package com.yhl.scp.mrp.material.distribute.domain.entity;

import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOurFactoryDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO;
import com.yhl.scp.mrp.material.distribute.dto.MaterialDemandDTO;
import com.yhl.scp.mrp.material.distribute.dto.MaterialFulfillmentDTO;
import com.yhl.scp.mrp.material.distribute.dto.MaterialSupplyDTO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanReplaceVO;
import com.yhl.scp.mrp.materialDemand.vo.MaterialGrossDemandVO;
import com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <code>DistributeContext</code>
 * <p>
 * 齐套检查数据上下文
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 14:56:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DistributeContext {

    // 订单
    private List<WorkOrderVO> workOrderVOS;
    // 工序
    private List<OperationVO> operationVOS;
    // 计划期间
    private PlanningHorizonVO planningHorizonVO;

    // 物料
    private Map<String, NewProductStockPointVO> productOnIdMap;
    private Map<String, NewProductStockPointVO> productCodeMap;
    private List<NewProductStockPointVO> demandProductList;

    // 替代料
    private Map<String, List<MaterialGrossDemandVO>> finishedMaterialGrossDemand;
    private Map<String, List<MaterialGrossDemandVO>> replaceDemandMap;
    private Map<String, ReplaceMapping> replaceMappingMap;
    private Map<String, List<ProductSubstitutionRelationshipVO>> productSubstitutionMap;

    // BOM
    private Map<String, RoutingVO> routingVOMapOfProductCode;
    private Map<String, RoutingStepVO> routingStepMapOfId;
    private Map<String, List<RoutingStepVO>> routingStepGroupOfRoutingId;
    private Map<String, List<RoutingStepInputVO>> routingStepInputGroupOfStepId;

    // 非原片供应:库存>在途>采购订单
    private List<InventoryBatchDetailVO> inventoryBatchDetail;
    private List<MaterialArrivalTrackingVO> materialArrivalTrackingVOS;

    // 原片供应：临时替代>本厂库存>汽运在途>码头库存>海运在途
    private List<MaterialPlanReplaceVO> materialPlanReplaceVOS;
    private Map<String, List<String>> replaceProductMap;
    private List<InventoryQuayDetailVO> inventoryQuayDetailVOS;
    private List<InventoryFloatGlassShippedDetailVO> floatGlassShippedDetailVOS;

    // 生产反馈
    private Map<String, List<FeedbackProductionVO>> feedbackProductionGroup;

    // 结果集合
    private List<MaterialDemandDTO> insertMaterialDemandList;
    private List<MaterialSupplyDTO> insertMaterialSupplyList;
    private Map<String, List<MaterialSupplyDTO>> supplyProductMap;
    private List<MaterialFulfillmentDTO> insertMaterialFulfillmentList;

    private List<String> logInfo;

    /**
     * 替代关系映射类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReplaceMapping {
        private String productType;
        // 是否混合替代
        private Boolean replaceMode;
        // 需求替代
        private MaterialGrossDemandVO demandReplace;
        // 供应替代
        private ProductSubstitutionRelationshipVO supplyReplace;

        private List<GlassSubstitutionRelationshipVO> replaceGlassDemandList;
    }

}
