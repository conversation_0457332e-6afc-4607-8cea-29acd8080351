package com.yhl.scp.mrp.originalFilm.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultVersionDTO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultVersionVO;

import java.util.List;

/**
 * <code>OriginalFilmDemandConsultVersionService</code>
 * <p>
 * 原片需求征询版本应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24 14:14:16
 */
public interface OriginalFilmDemandConsultVersionService extends BaseService<OriginalFilmDemandConsultVersionDTO, OriginalFilmDemandConsultVersionVO> {

    /**
     * 查询所有
     *
     * @return list {@link OriginalFilmDemandConsultVersionVO}
     */
    List<OriginalFilmDemandConsultVersionVO> selectAll();

    String selectFinallyVersion();

}
