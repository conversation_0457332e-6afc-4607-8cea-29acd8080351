package com.yhl.scp.mrp.material.arrival.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialDeliveryNote;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.enums.TicketStatusEnum;
import com.yhl.scp.mrp.material.arrival.convertor.MaterialDeliveryNoteConvertor;
import com.yhl.scp.mrp.material.arrival.domain.entity.MaterialDeliveryNoteDO;
import com.yhl.scp.mrp.material.arrival.domain.service.MaterialDeliveryNoteDomainService;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialDeliveryNoteDTO;
import com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialDeliveryNoteDao;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialDeliveryNotePO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.arrival.service.MaterialDeliveryNoteService;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialDeliveryNoteVO;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialDeliveryNoteServiceImpl</code>
 * <p>
 * 材料送货单数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:09:03
 */
@Slf4j
@Service
public class MaterialDeliveryNoteServiceImpl extends AbstractService implements MaterialDeliveryNoteService {

    @Resource
    private MaterialDeliveryNoteDao materialDeliveryNoteDao;

    @Resource
    private MaterialDeliveryNoteService materialDeliveryNoteService;

    @Resource
    private MaterialDeliveryNoteDomainService materialDeliveryNoteDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    public BaseResponse<Void> doCreate(MaterialDeliveryNoteDTO materialDeliveryNoteDTO) {
        // 0.数据转换
        MaterialDeliveryNoteDO materialDeliveryNoteDO = MaterialDeliveryNoteConvertor.INSTANCE.dto2Do(materialDeliveryNoteDTO);
        MaterialDeliveryNotePO materialDeliveryNotePO = MaterialDeliveryNoteConvertor.INSTANCE.dto2Po(materialDeliveryNoteDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialDeliveryNoteDomainService.validation(materialDeliveryNoteDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialDeliveryNotePO);
        materialDeliveryNoteDao.insertWithPrimaryKey(materialDeliveryNotePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialDeliveryNoteDTO materialDeliveryNoteDTO) {
        // 0.数据转换
        MaterialDeliveryNoteDO materialDeliveryNoteDO = MaterialDeliveryNoteConvertor.INSTANCE.dto2Do(materialDeliveryNoteDTO);
        MaterialDeliveryNotePO materialDeliveryNotePO = MaterialDeliveryNoteConvertor.INSTANCE.dto2Po(materialDeliveryNoteDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialDeliveryNoteDomainService.validation(materialDeliveryNoteDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialDeliveryNotePO);
        materialDeliveryNoteDao.update(materialDeliveryNotePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialDeliveryNoteDTO> list) {
        List<MaterialDeliveryNotePO> newList = MaterialDeliveryNoteConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialDeliveryNoteDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialDeliveryNoteDTO> list) {
        List<MaterialDeliveryNotePO> newList = MaterialDeliveryNoteConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialDeliveryNoteDao.updateBatch(newList);
    }

    @Override
    public void doUpdateBatchSelective(List<MaterialDeliveryNoteDTO> list) {
        List<MaterialDeliveryNotePO> newList = MaterialDeliveryNoteConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialDeliveryNoteDao.updateBatchSelective(newList);
    }


    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialDeliveryNoteDao.deleteBatch(idList);
        }
        return materialDeliveryNoteDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialDeliveryNoteVO selectByPrimaryKey(String id) {
        MaterialDeliveryNotePO po = materialDeliveryNoteDao.selectByPrimaryKey(id);
        return MaterialDeliveryNoteConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_delivery_note")
    public List<MaterialDeliveryNoteVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_delivery_note")
    public List<MaterialDeliveryNoteVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialDeliveryNoteVO> dataList = materialDeliveryNoteDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialDeliveryNoteServiceImpl target = springBeanUtils.getBean(MaterialDeliveryNoteServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialDeliveryNoteVO> selectByParams(Map<String, Object> params) {
        List<MaterialDeliveryNotePO> list = materialDeliveryNoteDao.selectByParams(params);
        return MaterialDeliveryNoteConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialDeliveryNoteVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_DELIVERY_NOTE.getCode();
    }

    @Override
    public List<MaterialDeliveryNoteVO> invocation(List<MaterialDeliveryNoteVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void doDisposePredictArrivalForJob(Integer moveMinute) {
        log.info("材料到货跟踪根据送货单数据维护预计发货时间，数量开始");
       // 120小时前
        Date startModifyTime = DateUtils.moveHour(new Date(), -120);

        // 只看已发货的送货单
        List<MaterialDeliveryNoteVO> materialDeliveryNoteList = this.selectByParams(ImmutableMap.of("startModifyTime", startModifyTime));
        materialDeliveryNoteList = materialDeliveryNoteList.stream().filter(
                        e -> StringUtils.isNotEmpty(e.getPurchaseOrderCode())
                                && StringUtils.isNotEmpty(e.getPurchaseOrderLineCode())
                                && StringUtils.isNotEmpty(e.getProductCode())
                                && e.getPredictArrivalDate() != null
                                && e.getPredictArrivalQuantity() != null
                                && e.getShippingDate() != null
                                && !StringUtils.equals(e.getWhetherMatches(), YesOrNoEnum.YES.getCode())
                                && StringUtils.isNotEmpty(e.getTicketStatus())
                                && !e.getTicketStatus().equals(TicketStatusEnum.REJECT.getCode()))
                .collect(Collectors.toList());
        if(CollUtil.isEmpty(materialDeliveryNoteList)) {
            log.info("材料到货跟踪根据送货单数据维护预计发货时间，数量未获取到新发货单数据");
            return;
        }

        // 处理送货
        handleShipped(materialDeliveryNoteList);
        // 处理取消
        handleCancel(materialDeliveryNoteList);

        log.info("材料到货跟踪根据送货单数据维护预计发货时间，数量结束");
    }

    /**
     * 处理送货单（送货）数据
     *
     * @param materialDeliveryNoteList 送货单
     */
    private void handleShipped(List<MaterialDeliveryNoteVO> materialDeliveryNoteList) {
        log.info("送货单（送货）处理开始");

        // 通过采购单号，采购订单行号，物料编码匹配到送货单，维护对应的预计送达时间，预计送货数量（只取（已送货）状态的）
        Map<String, List<MaterialDeliveryNoteVO>> deliveryNoteMap = materialDeliveryNoteList.stream()
                .filter(data -> data.getTicketStatus().equals(TicketStatusEnum.SHIPPED.getCode()))
                .collect(Collectors.groupingBy(e -> e.getPurchaseOrderCode() + "_" + e.getPurchaseOrderLineCode() + "_" + e.getProductCode()));

        if (deliveryNoteMap.isEmpty()) return;

        // 获取对应的材料到货跟踪数据
        List<MaterialArrivalTrackingVO> trackingList = materialArrivalTrackingService.selectByParams(
                ImmutableMap.of("combineKeys" , new ArrayList<>(deliveryNoteMap.keySet())));
        if (CollUtil.isEmpty(trackingList)) {
            log.info("未取到需要送货的到货跟踪");
            return;
        }
        List<MaterialArrivalTrackingDTO> batchAdd = new ArrayList<>();
        List<MaterialArrivalTrackingDTO> batchUpdate = new ArrayList<>();
        List<MaterialDeliveryNoteDTO> materialDeliveryNoteUpdateList = new ArrayList<>();

        for (MaterialArrivalTrackingVO sourceTrackingVO : trackingList) {

            if (!sourceTrackingVO.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode()) ||
                    null == sourceTrackingVO.getWaitDeliveryQuantity() ||
                    sourceTrackingVO.getWaitDeliveryQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                    .id(sourceTrackingVO.getId())
                    .build();
            // 根据采购订单号+采购订单行号+物料获取对应送货单
            List<MaterialDeliveryNoteVO> deliveryNoteList = deliveryNoteMap.get(sourceTrackingVO.getPurchaseOrderCode() + "_" + sourceTrackingVO.getPurchaseOrderLineCode() + "_" + sourceTrackingVO.getMaterialCode());
            // 根据预计到货日期倒叙
            deliveryNoteList.sort(Comparator.comparing(MaterialDeliveryNoteVO::getPredictArrivalDate).reversed());
            // 取出日期最大的送货单
            MaterialDeliveryNoteVO materialDeliveryNoteVO = deliveryNoteList.get(0);

            // 判断送货单的送货数量，和采购订单待发货数量是否一致
            if (materialDeliveryNoteVO.getPredictArrivalQuantity().compareTo(sourceTrackingVO.getWaitDeliveryQuantity()) == 0) {
                // 如果一致：保持现有逻辑，根据送货单信息更新预计到货日期、预计到货数量、更新到货状态为已送货
                update.setPredictArrivalDate(materialDeliveryNoteVO.getPredictArrivalDate());
                update.setPredictArrivalQuantity(materialDeliveryNoteVO.getPredictArrivalQuantity());
                update.setDeliveryNoteCode(materialDeliveryNoteVO.getDeliveryNoteCode());
                update.setTicketNum(materialDeliveryNoteVO.getTicketNum());
                // 计算差值
                BigDecimal result = sourceTrackingVO.getWaitDeliveryQuantity().subtract(materialDeliveryNoteVO.getPredictArrivalQuantity());
                // 判断结果是否小于 0，如果小于 0 则将结果设为 0
                if (result.compareTo(BigDecimal.ZERO) < 0) result = BigDecimal.ZERO;
                update.setWaitDeliveryQuantity(result);
                if (ArrivalStatusEnum.PLAN_PRUCHASE.getCode().equals(sourceTrackingVO.getArrivalStatus())) {
                    update.setArrivalStatus(ArrivalStatusEnum.DELIVERED.getCode());
                }
                update.setShippingDate(materialDeliveryNoteVO.getShippingDate());
                update.setRemark("数量一致");
                batchUpdate.add(update);
                MaterialDeliveryNoteDTO materialDeliveryNoteDTO = new MaterialDeliveryNoteDTO();
                materialDeliveryNoteDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
                materialDeliveryNoteDTO.setId(materialDeliveryNoteVO.getId());
                materialDeliveryNoteUpdateList.add(materialDeliveryNoteDTO);
                log.info("数量一致 - 更新{}", update);
            } else {
                // 如果不一致：
                // 根据送货单匹配（采购订单号+物料编码）的到货跟踪行信息，新增一条到货跟踪记录，并更新预计到货数量、待发货数量为0，到货状态为已送货
                // 原采购订单对应的那条到货跟踪信息，更新待发货数量为原代发货数量 ➖ 送货单数量，到货状态仍为计划采购
                MaterialArrivalTrackingDTO addDto = new MaterialArrivalTrackingDTO();
                BeanUtils.copyProperties(sourceTrackingVO, addDto);
                addDto.setPredictArrivalDate(materialDeliveryNoteVO.getPredictArrivalDate());
                addDto.setPredictArrivalQuantity(materialDeliveryNoteVO.getPredictArrivalQuantity());
                addDto.setWaitDeliveryQuantity(BigDecimal.ZERO);
                addDto.setArrivalStatus(ArrivalStatusEnum.DELIVERED.getCode());
                addDto.setDeliveryNoteCode(materialDeliveryNoteVO.getDeliveryNoteCode());
                addDto.setTicketNum(materialDeliveryNoteVO.getTicketNum());
                addDto.setShippingDate(materialDeliveryNoteVO.getShippingDate());
                addDto.setRemark("送货单数量和在途数量不一致，拆单" + materialDeliveryNoteVO.getId());
                addDto.setId(null);
                batchAdd.add(addDto);

                // 计算差值
                BigDecimal result = sourceTrackingVO.getWaitDeliveryQuantity().subtract(materialDeliveryNoteVO.getPredictArrivalQuantity());
                update.setWaitDeliveryQuantity(result);
                update.setArrivalStatus(ArrivalStatusEnum.PLAN_PRUCHASE.getCode());
                update.setRemark("送货单数量和在途数量不一致，拆单" + materialDeliveryNoteVO.getId());
                batchUpdate.add(update);

                MaterialDeliveryNoteDTO materialDeliveryNoteDTO = new MaterialDeliveryNoteDTO();
                materialDeliveryNoteDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
                materialDeliveryNoteDTO.setId(materialDeliveryNoteVO.getId());
                materialDeliveryNoteUpdateList.add(materialDeliveryNoteDTO);
                log.info("数量不一致 - 添加{}更新{}", addDto, update);
            }
        }

        if (CollectionUtils.isNotEmpty(batchUpdate)) {
            materialArrivalTrackingService.doUpdateBatchSelective(batchUpdate);
        }

        if (CollectionUtils.isNotEmpty(batchAdd)) {
            materialArrivalTrackingService.doCreateBatch(batchAdd);
        }

        if (CollectionUtils.isNotEmpty(materialDeliveryNoteUpdateList)) {
            materialDeliveryNoteService.doUpdateBatchSelective(materialDeliveryNoteUpdateList);
        }

        log.info("送货单（送货）处理结束");
    }

    /**
     * 处理送货单（取消）数据
     *
     * @param materialDeliveryNoteList 送货单
     */
    private void handleCancel(List<MaterialDeliveryNoteVO> materialDeliveryNoteList) {
        log.info("送货单（取消）处理开始");

        // 汇总 采购单号 + 采购订单行号 + 物料编码（只取（取消）状态的）
        List<String> combineKeys = materialDeliveryNoteList.stream()
                .filter(data -> data.getTicketStatus().equals(TicketStatusEnum.CANCEL.getCode()))
                .map(data -> String.join("_",
                        data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(), data.getProductCode()))
                .collect(Collectors.toList());

        if (combineKeys.isEmpty()) return;

        // 根据 采购单号 + 采购订单行号 + 送货单号 + 送货明细号 + 物料编码 分组
        Map<String, MaterialDeliveryNoteVO> deliveryNoteMap = materialDeliveryNoteList.stream()
                .filter(data -> data.getTicketStatus().equals(TicketStatusEnum.CANCEL.getCode()))
                .collect(Collectors.toMap(
                        data -> String.join("_",
                                data.getPurchaseOrderCode(), data.getPurchaseOrderLineCode(), data.getDeliveryNoteCode(),
                                data.getTicketNum(), data.getProductCode()),
                        Function.identity(),
                        (v1, v2) -> {
                            v1.setPredictArrivalQuantity(v1.getPredictArrivalQuantity().add(v2.getPredictArrivalQuantity()));
                            return v1;
                        }
                ));

        // 获取对应到货跟踪
        List<MaterialArrivalTrackingVO> trackingList = materialArrivalTrackingService.selectByParams(
                ImmutableMap.of("combineKeys" , combineKeys));
        if (CollUtil.isEmpty(trackingList)) {
            log.info("未取到需要取消的到货跟踪");
            return;
        }

        List<MaterialArrivalTrackingDTO> batchUpdate = new ArrayList<>();
        List<MaterialDeliveryNoteDTO> materialDeliveryNoteUpdateList = new ArrayList<>();

        // 需要取消的到货跟踪 根据 采购单号 + 采购订单行号 + 物料编码 分组
        Map<String, List<MaterialArrivalTrackingVO>> trackingVOMap = trackingList.stream()
                .collect(Collectors.groupingBy(data -> data.getPurchaseOrderCode() + "_" + data.getPurchaseOrderLineCode() + "_" + data.getMaterialCode()));

        for (Map.Entry<String, List<MaterialArrivalTrackingVO>> entry : trackingVOMap.entrySet()) {

            List<MaterialArrivalTrackingVO> value = entry.getValue();
            // 过滤出采购类型的到货跟踪
            List<String> planPurchaseArrivalStatusList = value.stream().map(MaterialArrivalTrackingVO::getArrivalStatus)
                    .filter(data -> data.equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode()))
                    .collect(Collectors.toList());

            // 只有送货的到货跟踪，直接重置数量
            if (CollectionUtils.isEmpty(planPurchaseArrivalStatusList)){

                for (MaterialArrivalTrackingVO sourceTrackingVO : value) {
                    if (!sourceTrackingVO.getArrivalStatus().equals(ArrivalStatusEnum.DELIVERED.getCode())) {
                        continue;
                    }
                    String noteKey = String.join("_",
                            sourceTrackingVO.getPurchaseOrderCode(), sourceTrackingVO.getPurchaseOrderLineCode(),
                            sourceTrackingVO.getDeliveryNoteCode(), sourceTrackingVO.getTicketNum(),sourceTrackingVO.getMaterialCode());

                    // 获取对应送货单
                    MaterialDeliveryNoteVO materialDeliveryNoteVO = deliveryNoteMap.get(noteKey);
                    if (Objects.isNull(materialDeliveryNoteVO)) continue;

                    MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                            .id(sourceTrackingVO.getId())
                            .build();
                    BeanUtils.copyProperties(sourceTrackingVO, update);
                    // 更新到货跟踪已送货状态的为计划采购，重新赋值待发货数量，并把在途数量和在途数量送货单号，送货时间滞空
                    update.setArrivalStatus(ArrivalStatusEnum.PLAN_PRUCHASE.getCode());
                    // 更新待发货数量为在途数量
                    update.setWaitDeliveryQuantity(sourceTrackingVO.getPredictArrivalQuantity());
                    update.setPredictArrivalDate(null);
                    update.setPredictArrivalQuantity(null);
                    update.setDeliveryNoteCode(null);
                    update.setTicketNum(null);
                    update.setShippingDate(null);
                    update.setRemark("取消");
                    batchUpdate.add(update);

                    MaterialDeliveryNoteDTO materialDeliveryNoteDTO = new MaterialDeliveryNoteDTO();
                    materialDeliveryNoteDTO.setId(materialDeliveryNoteVO.getId());
                    materialDeliveryNoteDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
                    materialDeliveryNoteUpdateList.add(materialDeliveryNoteDTO);
                }
            }else {

                // 用于记录补充数量（记录取消的送货数量）
                BigDecimal supplement = BigDecimal.ZERO;

                // 有采购的到货跟踪 和 送货的到货跟踪
                Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap =
                        value.stream().collect(Collectors.groupingBy(MaterialArrivalTrackingVO::getArrivalStatus));

                if (materialArrivalTrackingMap.isEmpty()) return;

                // 收集取消数量 和 收集需要关闭的到货跟踪
                List<MaterialArrivalTrackingVO> deliveredList = materialArrivalTrackingMap.get(ArrivalStatusEnum.DELIVERED.getCode());
                if (null != deliveredList) {
                    for (MaterialArrivalTrackingVO materialArrivalTrackingVO : deliveredList) {
                        String noteKey = String.join("_",
                                materialArrivalTrackingVO.getPurchaseOrderCode(),
                                materialArrivalTrackingVO.getPurchaseOrderLineCode(),
                                materialArrivalTrackingVO.getDeliveryNoteCode(),
                                materialArrivalTrackingVO.getTicketNum(),
                                materialArrivalTrackingVO.getMaterialCode());

                        // 根据组合键获取送货单
                        MaterialDeliveryNoteVO materialDeliveryNoteVO = deliveryNoteMap.get(noteKey);
                        if (Objects.nonNull(materialDeliveryNoteVO)) {
                            // 收集取消数量
                            supplement = supplement.add(materialDeliveryNoteVO.getPredictArrivalQuantity());

                            // 准备更新到货跟踪状态为关闭
                            MaterialArrivalTrackingDTO trackingDTO = new MaterialArrivalTrackingDTO();
                            BeanUtils.copyProperties(materialArrivalTrackingVO, trackingDTO);
                            trackingDTO.setArrivalStatus(ArrivalStatusEnum.CLOSE.getCode());
                            trackingDTO.setRemark("关闭原因（取消送货）");
                            batchUpdate.add(trackingDTO);

                            // 标记送货单为已匹配
                            MaterialDeliveryNoteDTO materialDeliveryNoteDTO = new MaterialDeliveryNoteDTO();
                            materialDeliveryNoteDTO.setId(materialDeliveryNoteVO.getId());
                            materialDeliveryNoteDTO.setWhetherMatches(YesOrNoEnum.YES.getCode());
                            materialDeliveryNoteUpdateList.add(materialDeliveryNoteDTO);
                        }
                    }
                }

                // 处理采购状态的到货跟踪
                List<MaterialArrivalTrackingVO> purchaseList = materialArrivalTrackingMap.get(ArrivalStatusEnum.PLAN_PRUCHASE.getCode());
                if (null != purchaseList) {
                    for (MaterialArrivalTrackingVO materialArrivalTrackingVO : purchaseList) {
                        MaterialArrivalTrackingDTO trackingDTO = new MaterialArrivalTrackingDTO();
                        BeanUtils.copyProperties(materialArrivalTrackingVO, trackingDTO);
                        // 把取消的送货数量 加回 采购的到货跟踪待发货数量
                        trackingDTO.setWaitDeliveryQuantity(materialArrivalTrackingVO.getWaitDeliveryQuantity().add(supplement));
                        trackingDTO.setRemark("补充已取消的待发货数量");
                        batchUpdate.add(trackingDTO);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(batchUpdate)) {
            materialArrivalTrackingService.doUpdateBatchNew(batchUpdate);
        }

        if (CollectionUtils.isNotEmpty(materialDeliveryNoteUpdateList)) {
            materialDeliveryNoteService.doUpdateBatchSelective(materialDeliveryNoteUpdateList);
        }

        log.info("送货单（取消）处理结束");
    }

    @Override
    public BaseResponse<Void> handleMaterialDeliveryNote(List<MesMaterialDeliveryNote> mesMaterialDeliveryNoteList) {
        if (CollectionUtils.isEmpty(mesMaterialDeliveryNoteList)) {
            return BaseResponse.success();
        }
        List<MaterialDeliveryNoteDTO> insertDtoS = new ArrayList<>();
        List<MaterialDeliveryNoteDTO> updateDtoS = new ArrayList<>();
        List<MaterialDeliveryNotePO> oldPos = materialDeliveryNoteDao.selectMaterialDeliveryNoteIds(mesMaterialDeliveryNoteList);
        Map<String, MaterialDeliveryNotePO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getDeliveryNoteCode() + "_" + t.getProductCode()+"_"+t.getPurchaseOrderCode()+"_"+t.getPurchaseOrderLineCode()+"_"+t.getTicketStatus()+"_"+t.getPredictArrivalQuantity()+"_"+t.getGroupId(),
                        Function.identity(), (v1, v2) -> v1));
        Map<String, MaterialDeliveryNotePO> oldJunePosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getDeliveryNoteCode() + "_" + t.getProductCode()+"_"+t.getPurchaseOrderCode()+"_"+t.getPurchaseOrderLineCode()+"_"+t.getTicketStatus()+"_"+t.getPredictArrivalQuantity(),
                        Function.identity(), (v1, v2) -> v1));
        DecimalFormat df = new DecimalFormat("0.####");
        df.setMinimumFractionDigits(4);
        for (MesMaterialDeliveryNote mesMaterialDeliveryNote : mesMaterialDeliveryNoteList) {
            MaterialDeliveryNoteDTO dto =  new MaterialDeliveryNoteDTO();
           BigDecimal value = mesMaterialDeliveryNote.getDeliveryQty();
           String qty=null;
            if (value != null){
                value.setScale(3, BigDecimal.ROUND_HALF_UP);
                qty= df.format(value);
            }
            //TODO 后续删除判断6.5号的代码，先保留此代码防止更新重复
            Instant shippingInstant = mesMaterialDeliveryNote.getShippingTime().toInstant();
            LocalDate shippingDate = shippingInstant.atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDate thresholdDate = LocalDate.of(2025, 6, 5);
            String id=
                    mesMaterialDeliveryNote.getGroupNum() + "_" + mesMaterialDeliveryNote.getItemCode()+"_"+mesMaterialDeliveryNote.getPoNumber()+"_"+mesMaterialDeliveryNote.getLineNum()+"_"+mesMaterialDeliveryNote.getTicketStatus()+"_"+qty;
            boolean flag=oldJunePosMap.containsKey(id);
            if (!shippingDate.isBefore(thresholdDate)) {
                id=id+"_"+mesMaterialDeliveryNote.getGroupId();
                flag=oldPosMap.containsKey(id);
            }


            if (flag) {
                MaterialDeliveryNotePO oldPo;
                if (!shippingDate.isBefore(thresholdDate)) {
                     oldPo = oldPosMap.get(id);
                }else {
                     oldPo = oldJunePosMap.get(id);
                }
                org.springframework.beans.BeanUtils.copyProperties(oldPo, dto);
                generateDto(dto, mesMaterialDeliveryNote);
                updateDtoS.add(dto);
            } else {
                generateDto(dto, mesMaterialDeliveryNote);
                insertDtoS.add(dto);
            }

        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }

        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> syncMaterialDeliveryNote(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MATERIAL_DELIVERY_NOTE.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    private void generateDto(MaterialDeliveryNoteDTO dto, MesMaterialDeliveryNote mesMaterialDeliveryNote) {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MPS.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "PURCHASE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        String remark = scenarioBusinessRange.getData().getRemark();
        dto.setDeliveryNoteCode(mesMaterialDeliveryNote.getGroupNum());
        dto.setProductCode(mesMaterialDeliveryNote.getItemCode());
        dto.setProductName(mesMaterialDeliveryNote.getDescriptions());
        dto.setPurchaseOrderCode(mesMaterialDeliveryNote.getPoNumber());
        dto.setPredictArrivalDate(mesMaterialDeliveryNote.getExpectedArrivalTime());
        dto.setPredictArrivalQuantity(mesMaterialDeliveryNote.getDeliveryQty());
        dto.setProductUnit(mesMaterialDeliveryNote.getUom());
        dto.setShippingDate(mesMaterialDeliveryNote.getShippingTime());
        dto.setStockPointCode(rangeData);
        dto.setStockPointName(remark);
        dto.setPurchaseOrderLineCode(mesMaterialDeliveryNote.getLineNum());
        dto.setTicketStatus(mesMaterialDeliveryNote.getTicketStatus());
        dto.setGroupId(mesMaterialDeliveryNote.getGroupId());
        dto.setTicketNum(mesMaterialDeliveryNote.getTicketNum());
    }
}
