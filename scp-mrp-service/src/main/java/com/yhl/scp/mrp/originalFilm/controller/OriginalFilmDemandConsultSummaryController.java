package com.yhl.scp.mrp.originalFilm.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultParam;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultSummaryDTO;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultSummaryService;
import com.yhl.scp.mrp.originalFilm.vo.GlassPurchasePlanVO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultSummaryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.util.List;

/**
 * <code>OriginalFilmDemandConsultSummaryController</code>
 * <p>
 * 原片需求征询汇总控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 09:27:48
 */
@Slf4j
@Api(tags = "原片需求征询汇总控制器")
@RestController
@RequestMapping("originalFilmDemandConsultSummary")
public class OriginalFilmDemandConsultSummaryController extends BaseController {

    @Resource
    private OriginalFilmDemandConsultSummaryService originalFilmDemandConsultSummaryService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OriginalFilmDemandConsultSummaryVO>> page() {
        List<OriginalFilmDemandConsultSummaryVO> originalFilmDemandConsultSummaryList = originalFilmDemandConsultSummaryService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OriginalFilmDemandConsultSummaryVO> pageInfo = new PageInfo<>(originalFilmDemandConsultSummaryList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页查询2")
    @PostMapping(value = "page2")
    @BusinessMonitorLog(businessCode = "原片需求汇总", moduleCode = "MRP", businessFrequency = "MONTH")
    public BaseResponse<PageInfo<OriginalFilmDemandConsultSummaryVO>> page2(@RequestBody OriginalFilmDemandConsultParam originalFilmDemandConsultParam) {
        List<OriginalFilmDemandConsultSummaryVO> originalFilmDemandConsultSummaryList = originalFilmDemandConsultSummaryService.selectByPage2(originalFilmDemandConsultParam);
        PageInfo<OriginalFilmDemandConsultSummaryVO> pageInfo = new PageInfo<>(originalFilmDemandConsultSummaryList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页查询原片采购计划")
    @PostMapping(value = "pageGlassPurchasePlan")
    @BusinessMonitorLog(businessCode = "原片采购计划", moduleCode = "MRP", businessFrequency = "MONTH")
    public BaseResponse<PageInfo<GlassPurchasePlanVO>> pageGlassPurchasePlan(@RequestBody OriginalFilmDemandConsultParam originalFilmDemandConsultParam) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, originalFilmDemandConsultSummaryService.selectByPageGlassPurchasePlan(originalFilmDemandConsultParam));
    }


    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        return originalFilmDemandConsultSummaryService.doCreate(originalFilmDemandConsultSummaryDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        return originalFilmDemandConsultSummaryService.doUpdate(originalFilmDemandConsultSummaryDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        originalFilmDemandConsultSummaryService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OriginalFilmDemandConsultSummaryVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, originalFilmDemandConsultSummaryService.selectByPrimaryKey(id));
    }
    
    @ApiOperation(value = "导出")
    @GetMapping(value = "export")
    @BusinessMonitorLog(businessCode = "原片需求汇总", moduleCode = "MRP", businessFrequency = "MONTH")
    public void export(HttpServletResponse response) {
    	String exportType = request.getParameter("exportType");
    	originalFilmDemandConsultSummaryService.exportNew(response,getPagination(), getSortParam(), getQueryCriteriaParam(), exportType);
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "exportGlassPurchasePlan")
    @BusinessMonitorLog(businessCode = "原片需求汇总", moduleCode = "MRP", businessFrequency = "MONTH")
    public void exportGlassPurchasePlan(HttpServletResponse response) {
        String exportType = request.getParameter("exportType");
        originalFilmDemandConsultSummaryService.exportNew(response,getPagination(), getSortParam(), getQueryCriteriaParam(), exportType);
    }
    
    @ApiOperation(value = "需求计算")
    @PostMapping(value = "doDemandCalculation")
    @BusinessMonitorLog(businessCode = "原片需求汇总计算", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> doDemandCalculation(@RequestBody OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        return originalFilmDemandConsultSummaryService.doDemandCalculation(originalFilmDemandConsultSummaryDTO);
    }
    
    @ApiOperation(value = "原片下拉接口")
    @GetMapping(value = "selectAllColor")
    public BaseResponse<List<LabelValue<String>>> selectAllColor() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, originalFilmDemandConsultSummaryService.selectAllColor());
    }

    @ApiOperation(value = "导入")
    @PostMapping(value = "upload")
    public BaseResponse<Void> upload(@RequestPart MultipartFile file, @RequestParam(value = "versionId", required = false) String versionId) {
        try {
            originalFilmDemandConsultSummaryService.doUpload(file,versionId);
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("未生产订单导入失败", e);
            throw new BusinessException(e.getLocalizedMessage());
        }
    }

    @ApiOperation(value = "模板导出（未生产订单）")
    @GetMapping(value = "exportTemplate")
    public void exportTemplateOutsourcing(HttpServletResponse response) {
        originalFilmDemandConsultSummaryService.exportTemplate(response);
    }
}
