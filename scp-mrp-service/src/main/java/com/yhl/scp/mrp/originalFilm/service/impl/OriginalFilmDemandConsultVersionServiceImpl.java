package com.yhl.scp.mrp.originalFilm.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.originalFilm.convertor.OriginalFilmDemandConsultVersionConvertor;
import com.yhl.scp.mrp.originalFilm.domain.entity.OriginalFilmDemandConsultVersionDO;
import com.yhl.scp.mrp.originalFilm.domain.service.OriginalFilmDemandConsultVersionDomainService;
import com.yhl.scp.mrp.originalFilm.dto.OriginalFilmDemandConsultVersionDTO;
import com.yhl.scp.mrp.originalFilm.infrastructure.dao.OriginalFilmDemandConsultVersionDao;
import com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultVersionPO;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultVersionService;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultVersionVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>OriginalFilmDemandConsultVersionServiceImpl</code>
 * <p>
 * 原片需求征询版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24 14:13:54
 */
@Slf4j
@Service
public class OriginalFilmDemandConsultVersionServiceImpl extends AbstractService implements OriginalFilmDemandConsultVersionService {

    @Resource
    private OriginalFilmDemandConsultVersionDao originalFilmDemandConsultVersionDao;

    @Resource
    private OriginalFilmDemandConsultVersionDomainService originalFilmDemandConsultVersionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(OriginalFilmDemandConsultVersionDTO originalFilmDemandConsultVersionDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultVersionDO originalFilmDemandConsultVersionDO = OriginalFilmDemandConsultVersionConvertor.INSTANCE.dto2Do(originalFilmDemandConsultVersionDTO);
        OriginalFilmDemandConsultVersionPO originalFilmDemandConsultVersionPO = OriginalFilmDemandConsultVersionConvertor.INSTANCE.dto2Po(originalFilmDemandConsultVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        originalFilmDemandConsultVersionDomainService.validation(originalFilmDemandConsultVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(originalFilmDemandConsultVersionPO);
        originalFilmDemandConsultVersionDao.insertWithPrimaryKey(originalFilmDemandConsultVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(OriginalFilmDemandConsultVersionDTO originalFilmDemandConsultVersionDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultVersionDO originalFilmDemandConsultVersionDO = OriginalFilmDemandConsultVersionConvertor.INSTANCE.dto2Do(originalFilmDemandConsultVersionDTO);
        OriginalFilmDemandConsultVersionPO originalFilmDemandConsultVersionPO = OriginalFilmDemandConsultVersionConvertor.INSTANCE.dto2Po(originalFilmDemandConsultVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        originalFilmDemandConsultVersionDomainService.validation(originalFilmDemandConsultVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(originalFilmDemandConsultVersionPO);
        originalFilmDemandConsultVersionDao.update(originalFilmDemandConsultVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OriginalFilmDemandConsultVersionDTO> list) {
        List<OriginalFilmDemandConsultVersionPO> newList = OriginalFilmDemandConsultVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        originalFilmDemandConsultVersionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<OriginalFilmDemandConsultVersionDTO> list) {
        List<OriginalFilmDemandConsultVersionPO> newList = OriginalFilmDemandConsultVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        originalFilmDemandConsultVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return originalFilmDemandConsultVersionDao.deleteBatch(idList);
        }
        return originalFilmDemandConsultVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OriginalFilmDemandConsultVersionVO selectByPrimaryKey(String id) {
        OriginalFilmDemandConsultVersionPO po = originalFilmDemandConsultVersionDao.selectByPrimaryKey(id);
        return OriginalFilmDemandConsultVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "ORIGINAL_FILM_DEMAND_CONSULT_VERSION")
    public List<OriginalFilmDemandConsultVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "ORIGINAL_FILM_DEMAND_CONSULT_VERSION")
    public List<OriginalFilmDemandConsultVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OriginalFilmDemandConsultVersionVO> dataList = originalFilmDemandConsultVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        OriginalFilmDemandConsultVersionServiceImpl target = SpringBeanUtils.getBean(OriginalFilmDemandConsultVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OriginalFilmDemandConsultVersionVO> selectByParams(Map<String, Object> params) {
        List<OriginalFilmDemandConsultVersionPO> list = originalFilmDemandConsultVersionDao.selectByParams(params);
        return OriginalFilmDemandConsultVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OriginalFilmDemandConsultVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String selectFinallyVersion() {
        return originalFilmDemandConsultVersionDao.selectFinallyVersion();
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<OriginalFilmDemandConsultVersionVO> invocation(List<OriginalFilmDemandConsultVersionVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
