<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedCompareDayDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO">
        <!--@Table fdp_delivery_plan_published_compare_day-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="publisher" jdbcType="VARCHAR" property="publisher"/>
        <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime"/>
        <result column="day_str" jdbcType="VARCHAR" property="dayStr"/>
        <result column="new_demand_quantity" jdbcType="INTEGER" property="newDemandQuantity"/>
        <result column="old_demand_quantity" jdbcType="INTEGER" property="oldDemandQuantity"/>
        <result column="variable_quantity" jdbcType="INTEGER" property="variableQuantity"/>
        <result column="rate_of_change" jdbcType="VARCHAR" property="rateOfChange"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,oem_code,product_code,demand_category,publisher,publish_time,day_str,new_demand_quantity,old_demand_quantity,variable_quantity,rate_of_change,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.publisher != null and params.publisher != ''">
                and publisher = #{params.publisher,jdbcType=VARCHAR}
            </if>
            <if test="params.publishTime != null">
                and publish_time = #{params.publishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.dayStr != null and params.dayStr != ''">
                and day_str = #{params.dayStr,jdbcType=VARCHAR}
            </if>
            <if test="params.dayStrList != null and params.dayStrList.size() > 0">
                and day_str in
                <foreach item="item" index="index" collection="params.dayStrList" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.newDemandQuantity != null">
                and new_demand_quantity = #{params.newDemandQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.oldDemandQuantity != null">
                and old_demand_quantity = #{params.oldDemandQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.variableQuantity != null">
                and variable_quantity = #{params.variableQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.rateOfChange != null">
                and rate_of_change = #{params.rateOfChange,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan_published_compare_day
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan_published_compare_day
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_delivery_plan_published_compare_day
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan_published_compare_day
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_delivery_plan_published_compare_day(
        id,
        oem_code,
        product_code,
        demand_category,
        publisher,
        publish_time,
        day_str,
        new_demand_quantity,
        old_demand_quantity,
        variable_quantity,
        rate_of_change,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{demandCategory,jdbcType=VARCHAR},
        #{publisher,jdbcType=VARCHAR},
        #{publishTime,jdbcType=TIMESTAMP},
        #{dayStr,jdbcType=VARCHAR},
        #{newDemandQuantity,jdbcType=INTEGER},
        #{oldDemandQuantity,jdbcType=INTEGER},
        #{variableQuantity,jdbcType=INTEGER},
        #{rateOfChange,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO">
        insert into fdp_delivery_plan_published_compare_day(id,
                                                            oem_code,
                                                            product_code,
                                                            demand_category,
                                                            publisher,
                                                            publish_time,
                                                            day_str,
                                                            new_demand_quantity,
                                                            old_demand_quantity,
                                                            variable_quantity,
                                                            rate_of_change,
                                                            remark,
                                                            enabled,
                                                            creator,
                                                            create_time,
                                                            modifier,
                                                            modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{demandCategory,jdbcType=VARCHAR},
                #{publisher,jdbcType=VARCHAR},
                #{publishTime,jdbcType=TIMESTAMP},
                #{dayStr,jdbcType=VARCHAR},
                #{newDemandQuantity,jdbcType=INTEGER},
                #{oldDemandQuantity,jdbcType=INTEGER},
                #{variableQuantity,jdbcType=INTEGER},
                #{rateOfChange,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_delivery_plan_published_compare_day(
        id,
        oem_code,
        product_code,
        demand_category,
        publisher,
        publish_time,
        day_str,
        new_demand_quantity,
        old_demand_quantity,
        variable_quantity,
        rate_of_change,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.publisher,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.dayStr,jdbcType=VARCHAR},
            #{entity.newDemandQuantity,jdbcType=INTEGER},
            #{entity.oldDemandQuantity,jdbcType=INTEGER},
            #{entity.variableQuantity,jdbcType=INTEGER},
            #{entity.rateOfChange,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_delivery_plan_published_compare_day(
        id,
        oem_code,
        product_code,
        demand_category,
        publisher,
        publish_time,
        day_str,
        new_demand_quantity,
        old_demand_quantity,
        variable_quantity,
        rate_of_change,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.publisher,jdbcType=VARCHAR},
            #{entity.publishTime,jdbcType=TIMESTAMP},
            #{entity.dayStr,jdbcType=VARCHAR},
            #{entity.newDemandQuantity,jdbcType=INTEGER},
            #{entity.oldDemandQuantity,jdbcType=INTEGER},
            #{entity.variableQuantity,jdbcType=INTEGER},
            #{entity.rateOfChange,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO">
        update fdp_delivery_plan_published_compare_day
        set oem_code            = #{oemCode,jdbcType=VARCHAR},
            product_code        = #{productCode,jdbcType=VARCHAR},
            demand_category     = #{demandCategory,jdbcType=VARCHAR},
            publisher           = #{publisher,jdbcType=VARCHAR},
            publish_time        = #{publishTime,jdbcType=TIMESTAMP},
            day_str             = #{dayStr,jdbcType=VARCHAR},
            new_demand_quantity = #{newDemandQuantity,jdbcType=INTEGER},
            old_demand_quantity = #{oldDemandQuantity,jdbcType=INTEGER},
            variable_quantity   = #{variableQuantity,jdbcType=INTEGER},
            rate_of_change      = #{rateOfChange,jdbcType=VARCHAR},
            remark              = #{remark,jdbcType=VARCHAR},
            enabled             = #{enabled,jdbcType=VARCHAR},
            modifier            = #{modifier,jdbcType=VARCHAR},
            modify_time         = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO">
        update fdp_delivery_plan_published_compare_day
        <set>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.publisher != null and item.publisher != ''">
                publisher = #{item.publisher,jdbcType=VARCHAR},
            </if>
            <if test="item.publishTime != null">
                publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.dayStr != null and item.dayStr != ''">
                day_str = #{item.dayStr,jdbcType=VARCHAR},
            </if>
            <if test="item.newDemandQuantity != null">
                new_demand_quantity = #{item.newDemandQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.oldDemandQuantity != null">
                old_demand_quantity = #{item.oldDemandQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.variableQuantity != null">
                variable_quantity = #{item.variableQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.rateOfChange != null">
                rate_of_change = #{item.rateOfChange,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_delivery_plan_published_compare_day
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publisher = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publisher,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="day_str = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dayStr,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="new_demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newDemandQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="old_demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldDemandQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="variable_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.variableQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="rate_of_change = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rateOfChange,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_delivery_plan_published_compare_day
            <set>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.demandCategory != null and item.demandCategory != ''">
                    demand_category = #{item.demandCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.publisher != null and item.publisher != ''">
                    publisher = #{item.publisher,jdbcType=VARCHAR},
                </if>
                <if test="item.publishTime != null">
                    publish_time = #{item.publishTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.dayStr != null and item.dayStr != ''">
                    day_str = #{item.dayStr,jdbcType=VARCHAR},
                </if>
                <if test="item.newDemandQuantity != null">
                    new_demand_quantity = #{item.newDemandQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.oldDemandQuantity != null">
                    old_demand_quantity = #{item.oldDemandQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.variableQuantity != null">
                    variable_quantity = #{item.variableQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.rateOfChange != null">
                    rate_of_change = #{item.rateOfChange,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_delivery_plan_published_compare_day
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_delivery_plan_published_compare_day where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectViewByDayStrAndProductCodeList" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        `fdp_delivery_plan_published_compare_day` a
        WHERE
        EXISTS (
        SELECT
        1
        FROM
        (
        SELECT
        fdppcd.oem_code,
        fdppcd.product_code,
        max( fdppcd.create_time ) AS create_time
        FROM
        fdp_delivery_plan_published_compare_day fdppcd
        GROUP BY
        fdppcd.oem_code,
        fdppcd.product_code
        ) b
        WHERE
        a.oem_code = b.oem_code
        AND a.product_code = b.product_code
        AND a.create_time = b.create_time
        )
        <if test="dayStrList != null and dayStrList.size() > 0">
            and day_str in
            <foreach item="item" index="index" collection="dayStrList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="productCodeList != null and productCodeList.size() > 0">
            and product_code in
            <foreach item="item" index="index" collection="productCodeList" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <resultMap id="UserMap" type="com.yhl.scp.mds.extension.resource.vo.StandardResourceVO">
        <result property="standardResourceCode" column="product_code"/>
        <result property="productionPlanner" column="production_planner"/>

    </resultMap>
    <select id="selectUserIdByDate" resultMap="UserMap">
        SELECT
        mpspb.product_code,
        mrsr.production_planner
        FROM
        mds_product_stock_point_base mpspb
        LEFT JOIN mds_res_standard_resource mrsr ON mpspb.line_group = mrsr.standard_resource_code
        WHERE
        mpspb.product_code IN (
        SELECT
        mpsp.product_code
        FROM
        sds_ord_operation t
        INNER JOIN mds_product_stock_point mpsp ON t.product_id = mpsp.id
        INNER JOIN mds_rou_standard_step mrss ON ( t.standard_step_id = mrss.id AND mrss.key_step = 'YES' )
        WHERE
        t.end_time &gt;= #{startTime,jdbcType=VARCHAR}
        AND t.start_time &lt;= #{endTime,jdbcType=VARCHAR}
        GROUP BY
        mpsp.product_code,mrsr.production_planner
        )
        GROUP BY
            mpspb.product_code,
            mrsr.production_planner
    </select>

    <select id="selectPlanProductCodeByDate" resultType="String">
        SELECT DISTINCT
            mpsp.product_code
        FROM
            `sds_ord_work_order` t,
            mds_product_stock_point mpsp
        WHERE
            t.product_id = mpsp.id
          AND t.end_time &gt;= #{startTime,jdbcType=VARCHAR}
    </select>

    <select id="selectUserIdProductCodeList" resultMap="UserMap">
        SELECT
            mpspb.product_code,
            mrsr.production_planner
        FROM
            mds_product_stock_point_base mpspb
                LEFT JOIN mds_res_standard_resource mrsr ON mpspb.line_group = mrsr.standard_resource_code
        WHERE
            mpspb.product_code IN
        <foreach item="item" index="index" collection="productCodeList" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        GROUP BY
            mpspb.product_code,
            mrsr.production_planner
    </select>
</mapper>
