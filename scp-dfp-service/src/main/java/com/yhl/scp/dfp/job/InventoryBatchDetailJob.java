package com.yhl.scp.dfp.job;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.ImmutableMap;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>InventoryBatchDetailJob</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-16 12:52:34
 */
@Component
@Slf4j
public class InventoryBatchDetailJob {
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;
    @Resource
    private NewMdsFeign mdsFeign;
    @XxlJob("mesInventoryBatchDetailJob")
    public ReturnT<String> mesInventoryBatchDetailJob() {
        //任务1 - 处理分片0的库存点
        //{"shardRules":[0],"shardSize":3}
        //
        //// 任务2 - 处理分片1的库存点
        //{"shardRules":[1],"shardSize":3}
        //
        //// 任务3 - 处理分片2的库存点
        //{"shardRules":[2],"shardSize":3}
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.isBlank(jobParam)) {
            XxlJobHelper.log("任务参数为空，任务终止。请在XXL-JOB调度中心为该任务配置分片规则（如：{\"shardRules\":[0],\"shardSize\":3}）。");
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务参数（分片规则）不能为空");
        }

        List<Integer> shardRules;
        int shardSize;
        try {
            JSONObject config = parseShardConfig(jobParam);
            shardRules = config.getBeanList("shardRules", Integer.class);
            shardSize = config.getInt("shardSize", 10); // 默认10个分片
            
            if (CollectionUtils.isEmpty(shardRules)) {
                XxlJobHelper.log("解析任务参数失败，未找到有效的分片规则");
                return new ReturnT<>(ReturnT.FAIL_CODE, "任务参数格式错误，未找到有效的分片规则");
            }
        } catch (Exception e) {
            XxlJobHelper.log("解析任务参数失败：{}，参数格式应为：{\"rules\":[0],\"shardSize\":3}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务参数格式错误：" + e.getMessage());
        }

        XxlJobHelper.log("解析到的分片规则：{}，总分片数：{}", shardRules, shardSize);

        return executeMesInventoryBatchDetailJob(shardRules, shardSize);
    }

    /**
     * 解析分片配置参数
     * @param jobParam 任务参数，格式：{"rules":[0,1,3,5],"total":10}
     * @return 解析后的分片配置对象
     */
    private JSONObject parseShardConfig(String jobParam) {
        try {
            return JSONUtil.parseObj(jobParam);
        } catch (Exception e) {
            throw new IllegalArgumentException("任务参数格式错误，必须为有效的JSON格式：" + e.getMessage(), e);
        }
    }

    /**
     * 执行MES库存批次明细同步任务的通用方法
     * @param shardRuleIndexes 分片规则索引列表
     * @param shardSize 总分片数
     * @return 执行结果
     */
    private ReturnT<String> executeMesInventoryBatchDetailJob(List<Integer> shardRuleIndexes, int shardSize) {
        try {
            List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
            if (CollectionUtils.isEmpty(scenarios)) {
                XxlJobHelper.log("租户下不存在DFP模块信息");
                return ReturnT.SUCCESS;
            }

            for (Scenario scenario : scenarios) {
                XxlJobHelper.log("scenario：{}下的同步MES库存批次明细job开始，分片规则：{}", scenario, shardRuleIndexes);
                
                try {
                    DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());

                    List<NewStockPointVO> newStockPointVOS = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(),
                            ImmutableMap.of("enabled",YesOrNoEnum.YES.getCode()));
                    
                    if (CollectionUtils.isEmpty(newStockPointVOS)) {
                        XxlJobHelper.log("scenario：{}下库存点信息为空", scenario);
                        continue;
                    }

                    // 使用传入的分片规则参数来过滤库存点
                    List<NewStockPointVO> stockPoints = newStockPointVOS.stream()
                            .filter(t -> StringUtils.isNotEmpty(t.getOrganizeId()) && StringUtils.isNotEmpty(t.getInterfaceFlag()))
                            .filter(t -> t.getInterfaceFlag().contains(ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode()))
                            .filter(t -> {
                                int shardIndex = Math.abs(Integer.parseInt(t.getOrganizeId())) % shardSize;
                                boolean shouldProcess = shardRuleIndexes.contains(shardIndex);
                                
                                XxlJobHelper.log("库存点代码：{} | 库存点ID：{} → shardIndex：{} → 是否处理：{}",
                                    t.getStockPointCode(),
                                    t.getOrganizeId(), 
                                    shardIndex, 
                                    shouldProcess ? "是" : "否");
                                
                                return shouldProcess;
                            })
                            .collect(Collectors.toList());

                    XxlJobHelper.log("scenario：{}下总库存点数：{}，过滤后的库存点数：{}", scenario, newStockPointVOS.size(), stockPoints.size());
                    XxlJobHelper.log("scenario：{}下分片规则{}过滤出的库存点列表：{}", scenario, shardRuleIndexes, stockPoints);

                    if (CollectionUtils.isEmpty(stockPoints)) {
                        XxlJobHelper.log("scenario：{}下分片规则{}无匹配的库存点", scenario, shardRuleIndexes);
                        continue;
                    }

                    XxlJobHelper.log("scenario：{}下分片规则{}开始同步库存点：{}", scenario, shardRuleIndexes, stockPoints);
                    inventoryBatchDetailService.syncMesStockBatchDetail(scenario, stockPoints);
                    XxlJobHelper.log("scenario：{}下分片规则{}同步完成", scenario, shardRuleIndexes);
                    
                } catch (Exception e) {
                    log.error("scenario：{}下分片规则{}同步异常", scenario, shardRuleIndexes, e);
                    XxlJobHelper.log("scenario：{}下分片规则{}同步异常：{}", scenario, shardRuleIndexes, e.getMessage());
                } finally {
                    DynamicDataSourceContextHolder.clearDataSource();
                }
                
                XxlJobHelper.log("scenario：{}下的同步MES库存批次明细job结束，分片规则：{}", scenario, shardRuleIndexes);
            }
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("MES库存批次明细同步任务执行失败，分片规则：{}", shardRuleIndexes, e);
            XxlJobHelper.log("MES库存批次明细同步任务执行失败，分片规则：{}，错误：{}", shardRuleIndexes, e.getMessage());
            return ReturnT.FAIL;
        }
    }

    @XxlJob("erpInventoryBatchDetailJob")
    public ReturnT<String> erpInventoryBatchDetailJob() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在DFP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("scenario：{}下的同步ERP库存批次明细job开始", scenario);
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            inventoryBatchDetailService.syncErpStockBatchDetail(scenario,null);
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("scenario：{}下的同步ERP库存批次明细job结束", scenario);
        }
        return ReturnT.SUCCESS;
    }

}
