package com.yhl.scp.dfp.delivery.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>DeliveryPlanPublishedCompareDayDao</code>
 * <p>
 * 发货计划按天对比表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 14:26:24
 */
public interface DeliveryPlanPublishedCompareDayDao extends BaseDao<DeliveryPlanPublishedCompareDayPO, DeliveryPlanPublishedCompareDayVO> {


    List<DeliveryPlanPublishedCompareDayPO> selectViewByDayStrAndProductCodeList(@Param("dayStrList") List<String> dayStrList,
                                                                   @Param("productCodeList") List<String> productCodeList);

    /**
     * 根据计划期间查询物料，并查询物料对应有权限的userId
     * @param startTime
     * @param endTime
     * @return
     */
    List<StandardResourceVO> selectUserIdByDate(@Param("startTime") String startTime,
                                              @Param("endTime") String endTime);


    /**
     * 根据计划期间查询有制造订单的物料
     * @param startTime
     * @return
     */
    List<String> selectPlanProductCodeByDate(@Param("startTime") String startTime);


    /**
     * 根查询物料对应有权限的userId
     * @param productCodeList
     * @return
     */
    List<StandardResourceVO> selectUserIdProductCodeList(@Param("productCodeList") List<String> productCodeList);


}
