package com.yhl.scp.dfp.version.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.common.constants.DfpConstants;
import com.yhl.scp.dfp.common.enums.GenerateTypeEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastVersionPO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.release.enums.ReleaseDemandTypeEnum;
import com.yhl.scp.dfp.verison.dto.OemResourceDTO;
import com.yhl.scp.dfp.verison.dto.VersionCreateDTO;
import com.yhl.scp.dfp.version.service.DemandVersionCreateBase;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.feign.MpsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ConsistenceDemandForecastVersionCreate</code>
 * <p>
 * 一致性业务预测版本创建
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-08 10:48:56
 */
@Slf4j
@Component
public class ConsistenceDemandForecastVersionCreate extends DemandVersionCreateBase {

    @Resource
    private MpsFeign mpsFeign;

    @Override
    protected String createFirstDemandVersion(VersionCreateDTO versionCreateDTO) {
        // 根据计划周期查询当前计划周期一级需求版本是否存在
        ConsistenceDemandForecastVersionPO firstVersion =
                consistenceDemandForecastVersionDao.selectFirstVersionInfoByPlanPeriod(versionCreateDTO.getPlanPeriod());
        if (Objects.nonNull(firstVersion)) {
            return firstVersion.getId();
        }
        ConsistenceDemandForecastVersionPO newFirstDemandVersionPo = new ConsistenceDemandForecastVersionPO();
        newFirstDemandVersionPo.setPlanPeriod(versionCreateDTO.getPlanPeriod());
        newFirstDemandVersionPo.setGenerateType(versionCreateDTO.getGenerateType());
        newFirstDemandVersionPo.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
        BasePOUtils.insertFiller(newFirstDemandVersionPo);
        consistenceDemandForecastVersionDao.insertWithPrimaryKey(newFirstDemandVersionPo);
        return newFirstDemandVersionPo.getId();
    }

    @Override
    protected String createSecondDemandVersion(VersionCreateDTO versionCreateDTO, String newVersionCode,
                                               String firstVersionId) {
        // 根据新生成的版本号查询二级需求是否存在
        ConsistenceDemandForecastVersionPO secondDemandVersionPo =
                consistenceDemandForecastVersionDao.selectSecondVersionInfoByVersionCode(newVersionCode);
        if (Objects.nonNull(secondDemandVersionPo)) {
            return secondDemandVersionPo.getId();
        }
        // 二级需求生成
        ConsistenceDemandForecastVersionPO nextDemandVersionPo = new ConsistenceDemandForecastVersionPO();
        nextDemandVersionPo.setPlanPeriod(versionCreateDTO.getPlanPeriod());
        nextDemandVersionPo.setPlanHorizon(12);
        nextDemandVersionPo.setPlanGranularity(GranularityEnum.MONTH.getCode());
        nextDemandVersionPo.setVersionCode(newVersionCode);
        nextDemandVersionPo.setParentVersionId(firstVersionId);
        nextDemandVersionPo.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
        nextDemandVersionPo.setDemandForecastVersionId(versionCreateDTO.getTargetConsistenceDemandForecastVersionId());
        nextDemandVersionPo.setGenerateType(versionCreateDTO.getGenerateType());
        BasePOUtils.insertFiller(nextDemandVersionPo);
        consistenceDemandForecastVersionDao.insertWithPrimaryKey(nextDemandVersionPo);
        return nextDemandVersionPo.getId();
    }

    @Override
    protected void createThirdDemandVersion(VersionCreateDTO versionCreateDTO, String newVersionCode,
                                            List<String> oemCodeList, String secondVersionId) {
        List<ConsistenceDemandForecastVersionPO> sonVersionPoList = Lists.newArrayList();
        String lastOriginVersionId = demandForecastVersionDao.selectLatestVersionId();
        Map<String, String> demandForecastVersionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(versionCreateDTO.getOemCodeResource())) {
            demandForecastVersionMap = versionCreateDTO.getOemCodeResource().stream().collect(Collectors
                    .toMap(OemResourceDTO::getOemCode, OemResourceDTO::getDemandForecastVersionId, (o1, o2) -> o1));
        }
        for (String oemCode : oemCodeList) {
            ConsistenceDemandForecastVersionPO sonDemandVersionPo = new ConsistenceDemandForecastVersionPO();
            sonDemandVersionPo.setPlanPeriod(versionCreateDTO.getPlanPeriod());
            sonDemandVersionPo.setPlanHorizon(12);
            sonDemandVersionPo.setPlanGranularity(GranularityEnum.MONTH.getCode());
            sonDemandVersionPo.setVersionCode(newVersionCode);
            sonDemandVersionPo.setParentVersionId(secondVersionId);
            sonDemandVersionPo.setOemCode(oemCode);
            // 判断业务预测版本是否为空，如果为空则获取计划周期内生成时间最晚的原始需求版本
            sonDemandVersionPo.setDemandForecastVersionId(demandForecastVersionMap.getOrDefault(oemCode,
                    lastOriginVersionId));
            sonDemandVersionPo.setGenerateType(versionCreateDTO.getGenerateType());
            sonDemandVersionPo.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
            BasePOUtils.insertFiller(sonDemandVersionPo);
            sonVersionPoList.add(sonDemandVersionPo);
        }
        consistenceDemandForecastVersionDao.insertBatchWithPrimaryKey(sonVersionPoList);
    }

    @Override
    protected List<String> getManualOemCodeList(VersionCreateDTO versionCreateDTO) {
        // 如果目标原始需求版本为空，则根据用户权限查询所属主机厂编码列表
        List<OemVO> oemVOList = super.getOemCodeByUserPermission();
        return oemVOList.stream()
                .map(OemVO::getOemCode)
                .collect(Collectors.toList());
    }

    @Override
    protected List<String> getAutoOemCodeList(VersionCreateDTO versionCreateDTO) {
        return oemService.selectAll().stream()
                .map(OemVO::getOemCode)
                .collect(Collectors.toList());
    }

    @Override
    protected void copyDemandVersionData(String tenantId, String secondVersionId, List<String> oemCodeList,
                                         VersionCreateDTO versionCreateDTO) {
        // 下挂主数据
        List<ConsistenceDemandForecastDataPO> consistenceDemandForecastDataPOS = Lists.newArrayList();
        // 下挂详情数据
        List<ConsistenceDemandForecastDataDetailPO> consistenceDemandForecastDataDetailPOS = Lists.newArrayList();
        String targetConsistenceDemandForecastVersionId = versionCreateDTO.getTargetConsistenceDemandForecastVersionId();
        // 复制版本的版本信息为空
        if (StringUtils.isBlank(targetConsistenceDemandForecastVersionId)) {
            // 获取目标最新版本的二级版本数据
            targetConsistenceDemandForecastVersionId = consistenceDemandForecastVersionDao.selectLatestVersionId();
        }
        // 获取目标版本的一致性需求数据
        Map<String, Map<String, ConsistenceDemandForecastDataVO>> targetConsistenceForecastDataMap = new HashMap<>();
        // 获取预测详情数据
        List<String> parentIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(targetConsistenceDemandForecastVersionId)) {
            // 一致性业务预测版本数据
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("versionId", targetConsistenceDemandForecastVersionId);
            List<ConsistenceDemandForecastDataVO> dataList = consistenceDemandForecastDataService.selectByParams(queryMap);
            if (CollectionUtils.isNotEmpty(dataList)) {
                targetConsistenceForecastDataMap.putAll(dataList.stream()
                        .collect(Collectors.groupingBy(ConsistenceDemandForecastDataVO::getOemCode,
                                Collectors.toMap(ConsistenceDemandForecastDataVO::getForecastType, Function.identity(), (v1, v2) -> v1))));
                // 获取主表ID集合
                parentIdList.addAll(dataList.stream().map(ConsistenceDemandForecastDataVO::getId).collect(Collectors.toList()));
            }
        }
        // 用户指定版本数据
        Map<String, List<DemandForecastEstablishmentVO>> specialDataMap = getSpecialDemandForecastEstablishmentData();

        for (Map.Entry<String, List<DemandForecastEstablishmentVO>> entry : specialDataMap.entrySet()) {
            String oemCode = entry.getKey();
            List<DemandForecastEstablishmentVO> establishments = specialDataMap.get(oemCode);
            if (CollectionUtils.isEmpty(establishments)) {
                continue;
            }
            establishments.stream().collect(Collectors.groupingBy(x -> String.join("##",
                            x.getProductCode(), (StringUtils.isBlank(x.getVehicleModelCode())
                                    ? "" : x.getVehicleModelCode()))))
                    .forEach((key, value) -> {
                        String dataId = UUIDUtil.getUUID();
                        ConsistenceDemandForecastDataPO dataPO = new ConsistenceDemandForecastDataPO();
                        dataPO.setId(dataId);
                        dataPO.setVersionId(secondVersionId);
                        dataPO.setForecastType(ReleaseDemandTypeEnum.LOADING_DEMAND.getCode());
                        dataPO.setOemCode(oemCode);
                        String[] split = key.split("##");
                        dataPO.setProductCode(split[0]);
                        if (split.length > 1) {
                            dataPO.setVehicleModelCode(split[1]);
                        }
                        dataPO.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
                        BasePOUtils.insertFiller(dataPO);
                        consistenceDemandForecastDataPOS.add(dataPO);
                        for (DemandForecastEstablishmentVO establishmentVO : value) {
                            ConsistenceDemandForecastDataDetailPO detailPO = new ConsistenceDemandForecastDataDetailPO();
                            detailPO.setConsistenceDemandForecastDataId(dataId);
                            detailPO.setForecastTime(establishmentVO.getForecastTime());
                            detailPO.setForecastQuantity(establishmentVO.getDemandForecast());
                            detailPO.setCustomerForecastsQuantity(establishmentVO.getCustomerForecast());
                            detailPO.setAlgorithmForecastsQuantity(establishmentVO.getAlgorithmForecast());
                            BasePOUtils.insertFiller(detailPO);
                            consistenceDemandForecastDataDetailPOS.add(detailPO);
                        }
                    });
        }
        // 插入一致性业务预测版本数据
        if (CollectionUtils.isNotEmpty(consistenceDemandForecastDataPOS)) {
            Lists.partition(consistenceDemandForecastDataPOS, 500).forEach(consistenceDemandForecastDataDao::insertBatchWithPrimaryKey);
        }
        // 插入一致性业务预测版本明细数据
        if (CollectionUtils.isNotEmpty(consistenceDemandForecastDataDetailPOS)) {
            Lists.partition(consistenceDemandForecastDataDetailPOS, 500).forEach(consistenceDemandForecastDataDetailDao::insertBatchWithPrimaryKey);
        }
    }

    private Map<String, List<DemandForecastEstablishmentVO>> getSpecialDemandForecastEstablishmentData() {
        Map<String, List<DemandForecastEstablishmentVO>> result = new HashMap<>();
        Set<String> versionIds = Sets.newHashSet();
        Map<String, Object> params = new HashMap<>();
        params.put("versionStatus", VersionStatusEnum.PUBLISHED.getCode());
        //查询最新已发布的需求预测版本 fdp_demand_forecast_version
        DemandForecastVersionPO demandForecastVersionPO = demandForecastVersionDao.selectLatestVersionIdByParams(params);
        if (null == demandForecastVersionPO) {
            throw new BusinessException("未找到最新已发布的需求预测版本");
        }
        versionIds.add(demandForecastVersionPO.getId());
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("forecastVersionIdList", versionIds);
        List<DemandForecastEstablishmentVO> establishmentVOS = demandForecastEstablishmentService.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(establishmentVOS)) {
            return result;
        }

        result = establishmentVOS.stream().collect(
                Collectors.groupingBy(DemandForecastEstablishmentVO::getOemCode));
        return result;
    }

    @SuppressWarnings("unused")
    private void assembleDataAndDetailData(ConsistenceDemandForecastVersionPO consistenceDemandForecastVersionPO,
                                           Map<String, ConsistenceDemandForecastVersionVO> collect, Map<String,
                    List<ConsistenceDemandForecastDataVO>> versionDataMapOfVersionId,
                                           Map<String, List<ConsistenceDemandForecastDataDetailVO>> versionDataDetailMapOfDataId,
                                           List<ConsistenceDemandForecastDataPO> consistenceDemandForecastDataPOS,
                                           List<ConsistenceDemandForecastDataDetailPO> consistenceDemandForecastDataDetailPOS) {
        ConsistenceDemandForecastVersionVO consistenceDemandForecastVersionVO =
                collect.get(consistenceDemandForecastVersionPO.getOemCode());
        if (null == consistenceDemandForecastVersionVO) {
            return;
        }
        // 获取明细数据
        List<ConsistenceDemandForecastDataVO> consistenceDemandForecastDataVOList =
                versionDataMapOfVersionId.get(consistenceDemandForecastVersionVO.getId());
        if (CollectionUtils.isEmpty(consistenceDemandForecastDataVOList)) {
            return;
        }
        for (ConsistenceDemandForecastDataVO consistenceDemandForecastDataVO : consistenceDemandForecastDataVOList) {
            List<ConsistenceDemandForecastDataDetailVO> demandForecastDataDetailVOS =
                    versionDataDetailMapOfDataId.get(consistenceDemandForecastDataVO.getId());
            if (CollectionUtils.isEmpty(demandForecastDataDetailVOS)) {
                continue;
            }
            // 生成一致性业务预测版本数据
            ConsistenceDemandForecastDataPO consistenceDemandForecastDataPO = new ConsistenceDemandForecastDataPO();
            consistenceDemandForecastDataPO.setId(UUIDUtil.getUUID());
            consistenceDemandForecastDataPO.setVersionId(consistenceDemandForecastVersionPO.getId());
            consistenceDemandForecastDataPO.setOemCode(consistenceDemandForecastDataVO.getOemCode());
            consistenceDemandForecastDataPO.setVehicleModelCode(consistenceDemandForecastDataVO.getVehicleModelCode());
            consistenceDemandForecastDataPO.setProductCode(consistenceDemandForecastDataVO.getProductCode());
            consistenceDemandForecastDataPO.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
            consistenceDemandForecastDataPO.setForecastType(consistenceDemandForecastDataVO.getForecastType());
            BasePOUtils.insertFiller(consistenceDemandForecastDataPO);
            consistenceDemandForecastDataPOS.add(consistenceDemandForecastDataPO);
            // 生成一致性业务预测版本明细数据
            for (ConsistenceDemandForecastDataDetailVO consistenceDemandForecastDataDetailVO :
                    demandForecastDataDetailVOS) {
                ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO =
                        new ConsistenceDemandForecastDataDetailPO();
                consistenceDemandForecastDataDetailPO.setId(UUIDUtil.getUUID());
                consistenceDemandForecastDataDetailPO.setConsistenceDemandForecastDataId(consistenceDemandForecastDataPO.getId());
                consistenceDemandForecastDataDetailPO.setForecastTime(consistenceDemandForecastDataDetailVO.getForecastTime());
                consistenceDemandForecastDataDetailPO.setForecastQuantity(consistenceDemandForecastDataDetailVO.getForecastQuantity());
                consistenceDemandForecastDataDetailPO.setCustomerForecastsQuantity(consistenceDemandForecastDataDetailVO.getCustomerForecastsQuantity());
                consistenceDemandForecastDataDetailPO.setAlgorithmForecastsQuantity(consistenceDemandForecastDataDetailVO.getAlgorithmForecastsQuantity());
                consistenceDemandForecastDataDetailPO.setActualShipmentQuantity(consistenceDemandForecastDataDetailVO.getActualShipmentQuantity());
                consistenceDemandForecastDataDetailPO.setAdjustQuantity(consistenceDemandForecastDataDetailVO.getAdjustQuantity());
                consistenceDemandForecastDataDetailPO.setAdjustType(consistenceDemandForecastDataDetailVO.getAdjustType());
                BasePOUtils.insertFiller(consistenceDemandForecastDataDetailPO);
                consistenceDemandForecastDataDetailPOS.add(consistenceDemandForecastDataDetailPO);
            }
        }
    }

    @Override
    protected String getVersionCode(VersionCreateDTO versionCreateDTO, List<String> oemCodeList) {
        String prefix = DfpConstants.CONSISTENCE_DEMAND_FORECAST_VERSION_CODE_PRE + versionCreateDTO.getPlanPeriod();
        if (GenerateTypeEnum.AUTO.getCode().equals(versionCreateDTO.getGenerateType())) {
            return prefix + DfpConstants.CONSISTENCE_DEMAND_FORECAST_VERSION_CODE;
        } else {
            // 如果手动创建版本，需要根据当前用户权限获取主机厂编码，并判断编码的最大版本号
            String versionCode =
                    consistenceDemandForecastVersionDao.selectMaxVersionCodeByOemCodeList(versionCreateDTO.getPlanPeriod(), oemCodeList);
            return prefix + getNewVersionCode(versionCode);
        }
    }

    @Override
    @BusinessMonitorLog(businessCode = "需求预测发布", moduleCode = "DFP", businessFrequency = "MONTH")
    public BaseResponse<Void> doPublishVersion(String versionType, String versionId) {
        ConsistenceDemandForecastVersionPO version = consistenceDemandForecastVersionDao.selectByPrimaryKey(versionId);
        if (null == version) {
            return BaseResponse.error("版本不存在");
        }
        if (PublishStatusEnum.PUBLISHED.getCode().equals(version.getVersionStatus())) {
            return BaseResponse.error("版本已发布");
        }
        version.setVersionStatus(PublishStatusEnum.PUBLISHED.getCode());
        BasePOUtils.updateFiller(version);
        consistenceDemandForecastVersionDao.publishVersion(version);

        String scenario = SystemHolder.getScenario();
        String userId = SystemHolder.getUserId();
        Integer planHorizon = version.getPlanHorizon();
        String planPeriod = version.getPlanPeriod();
        String capacityPeriod = planHorizon == null ? null : String.valueOf(planHorizon);
        CompletableFuture.runAsync(() -> {
            try {
                mpsFeign.doRefreshCapacityBalance(scenario, userId, planPeriod, capacityPeriod);
            } catch (Exception e) {
                log.error("产能平衡计算失败", e);
            }
        });
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

}