package com.yhl.scp.dfp.loading.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>LoadingDemandSubmissionDO</code>
 * <p>
 * 装车需求提报DO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:12:19
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class LoadingDemandSubmissionDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 637547094571821780L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 原始版本ID
     */
    private String versionId;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂零件号
     */
    private String partNumber;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 需求类型
     */
    private String demandCategory;
    /**
     * 欠交数量
     */
    private BigDecimal oweQuantity;

    /**
     * 导入时间
     */
    private Date importTime;

    /**
     * 上次导入时间
     */
    private Date lastImportTime;

    /**
     * 上次导入时间
     */
    private BigDecimal historyDemandQuantity;

}
