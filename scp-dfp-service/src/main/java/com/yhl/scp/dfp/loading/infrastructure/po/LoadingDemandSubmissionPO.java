package com.yhl.scp.dfp.loading.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>LoadingDemandSubmissionPO</code>
 * <p>
 * 装车需求提报PO
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:12:19
 */
public class LoadingDemandSubmissionPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 910407008129977796L;

    /**
     * 原始需求版本ID
     */
    private String versionId;
    /**
     * 主机厂编码
     */
    private String oemCode;
    /**
     * 主机厂零件号
     */
    private String partNumber;
    /**
     * 本厂编码
     */
    private String productCode;
    /**
     * 需求类型
     */
    private String demandCategory;
    /**
     * 欠交数量
     */
    private BigDecimal oweQuantity;

    /**
     * 导入时间
     */
    private Date importTime;

    /**
     * 上次导入时间
     */
    private Date lastImportTime;

    /**
     * 上次导入时间
     */
    private BigDecimal historyDemandQuantity;

    public String getDemandCategory() {
        return demandCategory;
    }

    public void setDemandCategory(String demandCategory) {
        this.demandCategory = demandCategory;
    }

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public String getOemCode() {
        return oemCode;
    }

    public void setOemCode(String oemCode) {
        this.oemCode = oemCode;
    }

    public String getPartNumber() {
        return partNumber;
    }

    public void setPartNumber(String partNumber) {
        this.partNumber = partNumber;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getOweQuantity() {
        return oweQuantity;
    }

    public void setOweQuantity(BigDecimal oweQuantity) {
        this.oweQuantity = oweQuantity;
    }
	public Date getImportTime() {
		return importTime;
	}

	public void setImportTime(Date importTime) {
		this.importTime = importTime;
	}

	public Date getLastImportTime() {
		return lastImportTime;
	}

	public void setLastImportTime(Date lastImportTime) {
		this.lastImportTime = lastImportTime;
	}

	public BigDecimal getHistoryDemandQuantity() {
		return historyDemandQuantity;
	}

	public void setHistoryDemandQuantity(BigDecimal historyDemandQuantity) {
		this.historyDemandQuantity = historyDemandQuantity;
	}
}
