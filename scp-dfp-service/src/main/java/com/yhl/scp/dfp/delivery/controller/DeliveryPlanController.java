package com.yhl.scp.dfp.delivery.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDateUtils;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanCalculateDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanDTO;
import com.yhl.scp.dfp.delivery.dto.InventoryAndDeliveryDTO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareDayService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanDoPublishVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishCheckVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.InventoryAndDeliveryDataVO;
import com.yhl.scp.ips.common.SystemHolder;

import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>DeliveryPlanController</code>
 * <p>
 * 发货计划表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:23
 */
@Slf4j
@Api(tags = "发货计划表控制器")
@RestController
@RequestMapping("deliveryPlan")
public class DeliveryPlanController extends BaseController {

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Resource
    private RedisUtil redisUtil;
    
    @Resource
    private DeliveryPlanPublishedCompareDayService deliveryPlanPublishedCompareDayService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<DeliveryPlanVO>> page() {
        List<DeliveryPlanVO> deliveryPlanList = deliveryPlanService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DeliveryPlanVO> pageInfo = new PageInfo<>(deliveryPlanList);
        if (CollectionUtils.isEmpty(deliveryPlanList)) {
            return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
        }
        if (deliveryPlanList.get(0) != null) {
            deliveryPlanList.get(0).setDateList(DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanList.get(0).getVersionCode()));
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "查询所有Id")
    @GetMapping(value = "selectIds")
    public BaseResponse<List<String>> selectIds(@RequestParam(value = "versionId") String versionId) {
        List<DeliveryPlanVO> deliveryPlanList = deliveryPlanService.selectByParams(ImmutableMap.of("versionId",
                versionId));
        if (CollectionUtils.isEmpty(deliveryPlanList)) {
            return BaseResponse.success(BaseResponse.OP_SUCCESS, new ArrayList<>());
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryPlanList.stream().map(DeliveryPlanVO::getId).collect(Collectors.toList()));
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DeliveryPlanDTO deliveryPlanDTO) {
        return deliveryPlanService.doCreate(deliveryPlanDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DeliveryPlanDTO deliveryPlanDTO) {
        return deliveryPlanService.doUpdate(deliveryPlanDTO);
    }

    @ApiOperation(value = "发布")
    @PostMapping(value = "publish")
    @BusinessMonitorLog(businessCode = "发货计划发布", moduleCode = "DFP", businessFrequency = "DAY")
    public BaseResponse<List<String>> publish(@RequestBody List<String> ids) {
        String redisKey = RedisKeyManageEnum.DELIVERY_PLAN_PUBLISH.getKey().replace("{userId}", SystemHolder.getUserId());
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有发货计划在发布，请稍后提交");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId());
            //维护发布数据
            DeliveryPlanDoPublishVO doPublish = deliveryPlanService.doPublish(ids, SystemHolder.getUserId(),
                    SystemHolder.getUserName());
            //维护发布比较数据（异步）
            deliveryPlanPublishedCompareDayService.doCompare(doPublish.getOldDeliveryPlanPublishedDTOS(), 
            		SystemHolder.getUserId(), new Date(), SystemHolder.getScenario());
            return doPublish.getSyncMesResp();
        } catch (Exception e) {
            log.error("发货计划发布失败", e);
            throw new BusinessException("发货计划发布失败, {0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        deliveryPlanService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<DeliveryPlanVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        deliveryPlanService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "发货计划保存")
    @PostMapping(value = "deliveryPlanSave")
    @BusinessMonitorLog(businessCode = "发货计划编制", moduleCode = "DFP", businessFrequency = "DAY")
    public BaseResponse<Void> deliveryPlanSave(@RequestBody List<DeliveryPlanDTO> deliveryPlans) {
        return deliveryPlanService.doDeliveryPlanSave(deliveryPlans, YesOrNoEnum.NO.getCode());
    }

    @ApiOperation(value = "发货计划计算")
    @PostMapping(value = "deliveryPlanCalc")
    @BusinessMonitorLog(businessCode = "发货计划计算", moduleCode = "DFP", businessFrequency = "DAY")
    public BaseResponse<Void> deliveryPlanCalc(@RequestBody DeliveryPlanCalculateDTO deliveryPlanCalculateDTO) {
        String userId = SystemHolder.getUserId();
        String scenario = SystemHolder.getScenario();
        return deliveryPlanService.doDeliveryPlanCalc(deliveryPlanCalculateDTO, false, scenario, userId);
    }

    @ApiOperation(value = "使用上版计划")
    @GetMapping(value = "usePreviousPlan")
    public BaseResponse<Void> usePreviousPlan(@RequestParam(value = "oemCode") String oemCodes,
                                              @RequestParam(value = "versionCode") String versionCode,
                                              @RequestParam(value = "prevVersionCode") String prevVersionCode) {
        deliveryPlanService.doUsePreviousPlan(oemCodes, versionCode, prevVersionCode);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "主机厂名称与编码下拉")
    @GetMapping(value = "oemDropdown")
    public BaseResponse<List<LabelValue<String>>> queryOemDropdownInfo(@RequestParam(value = "versionCode") String versionCode) {
        return BaseResponse.success(deliveryPlanService.queryOemDropdownInfo(versionCode));
    }

    @ApiOperation(value = "发货计划导出")
    @GetMapping(value = "exportData")
    public void exportData(@RequestParam(value = "versionCode") String versionCode) {
        deliveryPlanService.exportData(this.response, versionCode);
    }

    @ApiOperation(value = "查询工序库存与日计划发货量")
    @PostMapping(value = "selectInventoryAndDelivery")
    public BaseResponse<PageInfo<InventoryAndDeliveryDataVO>> selectInventoryAndDelivery(@RequestBody InventoryAndDeliveryDTO inventoryAndDeliveryDTO) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanService.selectInventoryAndDelivery(inventoryAndDeliveryDTO));
    }

    @ApiOperation(value = "发布校验")
    @PostMapping(value = "doPublishCheck")
    public BaseResponse<DeliveryPlanPublishCheckVO> doPublishCheck(@RequestBody DeliveryPlanDTO deliveryPlanDTO) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryPlanService.doPublishCheck(deliveryPlanDTO));
    }

    @ApiOperation(value = "分页查询发货计划报表")
    @GetMapping(value = "pageDeliveryReport")
    public BaseResponse<PageInfo<DeliveryPlanVO>> pageDeliveryReport() {
        List<DeliveryPlanVO> deliveryPlanList = deliveryPlanService.pageDeliveryReport(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<DeliveryPlanVO> pageInfo = new PageInfo<>(deliveryPlanList);
        if (CollectionUtils.isEmpty(deliveryPlanList)) {
            return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
        }
        if (deliveryPlanList.get(0) != null) {
            deliveryPlanList.get(0).setDateList(DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanList.get(0).getVersionCode()));
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

}