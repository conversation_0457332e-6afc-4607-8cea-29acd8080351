package com.yhl.scp.dfp.delivery.domain.service;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Sets;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailExitService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataExitService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailExitVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataExitVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.delivery.calc.DeliveryPlanCalcSpace;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDateUtils;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanDO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanLockConfigVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanReplenishConfigVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.oem.enums.OemTradeTypeEnum;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionExitService;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionExitVO;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryShiftDao;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.service.InventoryRecieveConfirmationExitService;
import com.yhl.scp.dfp.stock.service.InventoryRecieveConfirmationService;
import com.yhl.scp.dfp.stock.vo.InventoryRecieveConfirmationExitVO;
import com.yhl.scp.dfp.stock.vo.InventoryRecieveConfirmationVO;
import com.yhl.scp.dfp.warehouse.service.AbroadWarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReceiveCheckExitVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReceiveCheckVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>DeliveryPlanDomainService</code>
 * <p>
 * 发货计划表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:24
 */
@Slf4j
@Service
public class DeliveryPlanDomainService {

    @Resource
    InventoryShiftDao inventoryShiftDao;
    @Resource
    InventoryBatchDetailService inventoryBatchDetailService;
    @Resource
    DfpResourceCalendarService dfpResourceCalendarService;
    @Resource
    ConsistenceDemandForecastDataService consistenceDemandForecastDataService;
    @Resource
    ConsistenceDemandForecastDataDetailService consistenceDemandForecastDataDetailService;
    @Resource
    CleanDemandDataService cleanDemandDataService;
    @Resource
    CleanDemandDataDetailService cleanDemandDataDetailService;
    @Resource
    OemInventorySubmissionService oemInventorySubmissionService;
    
    @Resource
    CleanDemandDataExitService cleanDemandDataExitService;
    
    @Resource
    CleanDemandDataDetailExitService cleanDemandDataDetailExitService;
    
    @Resource
    private OemStockPointMapService oemStockPointMapService;
    
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    
    @Resource
    private InventoryRecieveConfirmationService inventoryRecieveConfirmationService;
    
    @Resource
    private InventoryRecieveConfirmationExitService inventoryRecieveConfirmationExitService;
    
    @Resource
    private OemInventorySubmissionExitService oemInventorySubmissionExitService;
    
    @Resource
    private AbroadWarehouseReleaseRecordService abroadWarehouseReleaseRecordService;
    
    @Resource
    private NewMdsFeign newMdsFeign;

    @SuppressWarnings("unused")
    public Map<String, Integer> getOemInventory() {
        List<OemInventorySubmissionVO> oemInventorySubmissionVOS = oemInventorySubmissionService.selectAll();
        // 转map，key为oemCode&&productCode，value为stockInventoryQuantity,oemInventoryQuantity求和
        return oemInventorySubmissionVOS.stream().collect(Collectors.toMap(
                vo -> vo.getOemCode() + "&&" + vo.getProductCode(),
                vo -> vo.getStockInventoryQuantity().add(vo.getOemInventoryQuantity()).intValue(),
                (t1, t2) -> t2));
    }


    /**
     * 数据校验
     *
     * @param deliveryPlanDO 领域对象
     */
    public void validation(@SuppressWarnings("unused") DeliveryPlanDO deliveryPlanDO) {
        // TODO document why this method is empty
    }

    /**
     *
     * 校验主机厂发货配置是否存在
     * @param mtsOemCodes
     * @param deliveryPlanLockConfigVOMap
     * @param deliveryPlanReplenishConfigVOMap
     */
    @SuppressWarnings("unused")
    public void checkMtsOemDeliveryConfigExist(List<String> mtsOemCodes,
                                               Map<String, DeliveryPlanLockConfigVO> deliveryPlanLockConfigVOMap,
                                               Map<String, DeliveryPlanReplenishConfigVO> deliveryPlanReplenishConfigVOMap) {
        String errorMessage = "";
        List<String> mtsOemDeliveryLockConfigNotExist = mtsOemCodes.stream()
                .filter(oemCode -> !deliveryPlanLockConfigVOMap.containsKey(oemCode))
                .collect(Collectors.toList());
        List<String> mtsOemDeliveryReplenishNotExist = mtsOemCodes.stream()
                .filter(oemCode -> !deliveryPlanReplenishConfigVOMap.containsKey(oemCode))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mtsOemDeliveryLockConfigNotExist)) {
            // 换行符结尾
            errorMessage += "主机厂锁定配置不存在：" + String.join(",", mtsOemDeliveryLockConfigNotExist) + "\n";
        }
        if (CollectionUtils.isNotEmpty(mtsOemDeliveryReplenishNotExist)) {
            errorMessage += "主机厂补货配置不存在：" + String.join(",", mtsOemDeliveryReplenishNotExist);
        }
        if (StringUtils.isNotEmpty(errorMessage)) {
            throw new BusinessException(errorMessage);
        }
    }

    /**
     * 获取实时库存
     *
     * @param deliveryPlanVersionCode
     * @param oemTransitInventoryMap
     * @param productScope
     * @return java.util.Map<java.lang.String, java.lang.Integer>
     */
    public Map<String, Integer> getRealTimeInventory(String deliveryPlanVersionCode,
                                                     Map<String, Integer> oemTransitInventoryMap,
                                                     List<String> productScope) {
        Map<String, Integer> productInventory = new HashMap<>();
        // if (deliveryPlanVersionCode.endsWith("00001")) {
        //
        //     return productInventory;
        // }
        // key:oemCode+"&&"+productCode
        log.info("productScope====== {}", JacksonUtils.toJson(productScope));
        Map<String, Integer> realTimeInventoryMap = inventoryBatchDetailService.getRealTimeInventory(productScope);
        if (MapUtils.isEmpty(realTimeInventoryMap) && MapUtils.isEmpty(oemTransitInventoryMap)) {
            return productInventory;
        }
        log.info("realTimeInventoryMap====== {}", JacksonUtils.toJson(realTimeInventoryMap));
        Set<String> keys = Sets.newHashSet();
        if (MapUtils.isNotEmpty(oemTransitInventoryMap)) {
            keys.addAll(oemTransitInventoryMap.keySet());
        }
        if (MapUtils.isNotEmpty(realTimeInventoryMap)) {
            keys.addAll(realTimeInventoryMap.keySet());
        }
        keys.forEach(key -> {
            Integer realTimeInventoryQuantity = realTimeInventoryMap.get(key) == null
                    ? 0 : realTimeInventoryMap.get(key);
            Integer oemTransitInventoryQuantity = oemTransitInventoryMap.get(key) == null
                    ? 0 : oemTransitInventoryMap.get(key);
            productInventory.put(key, realTimeInventoryQuantity + oemTransitInventoryQuantity);
        });
        // 当前版本不是首版，则从首版库存推移表中获取
        // StringBuilder sb = new StringBuilder(deliveryPlanVersionCode);
        // sb.replace(sb.length() - 5, sb.length(), "00001");
        // Date planDate =
        //         DateUtils.stringToDate(deliveryPlanVersionCode.substring(deliveryPlanVersionCode.length() - 13,
        //                 deliveryPlanVersionCode.length() - 5), "yyyyMMdd");
        // List<InventoryShiftVO> inventoryShiftVOS = inventoryShiftDao
        //         .selectVOByParams(ImmutableMap.of("plannedDate", planDate, "versionCode", sb.toString()));
        // if (CollectionUtils.isNotEmpty(inventoryShiftVOS)) {
        //     return inventoryShiftVOS.stream().collect(Collectors.toMap(t -> t.getOemCode() + "&&" + t.getProductCode(),
        //             t -> t.getOpeningInventory() == null ? 0 : t.getOpeningInventory(), (t1, t2) -> t2));
        // }
        return productInventory;
    }

    /**
     * 获取未来30天装车日,如果未来三天没有，默认每天都有
     *
     * @param oemCodes
     * @param dateList
     * @return java.util.Map<java.lang.String, java.util.List < com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO>>
     */
    public Map<String, List<ResourceCalendarVO>> getFuture30DaysCalendar(List<String> oemCodes, List<Date> dateList) {
        return dfpResourceCalendarService.getResourceByOem(oemCodes, dateList, false);
    }

    /**
     *
     * @param deliveryPlanVersion 发货计划版本
     * @param oemCodeScope 主机厂范围
     * @param productScope 物料范围
     * @param vehicleModelScope 车型代码范围
     * @param months 计算时间范围
     * @param lastDay 起始时间
     * @param bySearch true表示只作查询用，查不到数据会返回空，false表示计算用，查不到数据会报错
     * @return
     */
    public Map<String,List<CleanDemandDataDetailVO>> mergeCleanDemandAndForecastDemandData(
            DeliveryPlanVersionVO deliveryPlanVersion,
            List<String> oemCodeScope,
            List<String> productScope, List<String> vehicleModelScope,List<String> months,Date lastDay,
            boolean bySearch) {

        List<Date> deliveryPlanDateList =
                DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanVersion.getVersionCode());
        Date cleanDemandLastDay = deliveryPlanDateList.get(deliveryPlanDateList.size() - 1);


        List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS = cleanDemandDataDetailService
                .selectByParams(ImmutableMap.of(
                        "versionId", deliveryPlanVersion.getDemandVersionId(),
                        "oemCodeList", oemCodeScope,
                        "productCodeList", productScope));
        //日需求数据（出口）
        List<CleanDemandDataDetailExitVO> cleanDemandDataDetailExitVOS = cleanDemandDataDetailExitService
                .selectByParams(ImmutableMap.of(
                        "versionId", deliveryPlanVersion.getDemandVersionId(),
                        "oemCodeList", oemCodeScope,
                        "productCodeList", productScope));
        if (CollectionUtils.isEmpty(cleanDemandDataDetailVOS) && CollectionUtils.isEmpty(cleanDemandDataDetailExitVOS)) {
            if (bySearch){
                return new HashMap<>();
            }else {
                throw new BusinessException("未找到当前用户管理物料的日需求");
            }
        }
        //id和demandCategory映射关系
        Map<String,String> cleanDemandCategory = new HashMap<>();
        if(CollectionUtils.isNotEmpty(cleanDemandDataDetailVOS)) {
        	cleanDemandCategory = getCleanDemandCategory(cleanDemandDataDetailVOS);
        }
        //维护出口的日需求数据
        if(CollectionUtils.isNotEmpty(cleanDemandDataDetailExitVOS)) {
        	//id和demandCategory映射关系
        	Map<String,String> cleanDemandExitCategory = getCleanDemandExitCategory(cleanDemandDataDetailExitVOS);
        	cleanDemandDataDetailExitVOS.forEach( e-> {
        		CleanDemandDataDetailVO add = new CleanDemandDataDetailVO();
        		BeanUtils.copyProperties(e, add);
        		add.setCleanDemandDataId(e.getCleanDemandDataExitId());
        		if(e.getDemandQuantity() != null) {
        			add.setDemandQuantity(BigDecimal.valueOf(e.getDemandQuantity()));
        		}
        		cleanDemandDataDetailVOS.add(add);
        	});
        	cleanDemandCategory.putAll(cleanDemandExitCategory);
        }
        List<String> oemCodes = cleanDemandDataDetailVOS.stream().map(CleanDemandDataDetailVO::getOemCode).distinct()
                .collect(Collectors.toList());
        //量产需求
        Map<String, List<CleanDemandDataDetailVO>> outputCleanDemandDataDetailByOemCodeAndProductCode =
                getCleanDemandDataByCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),
                        cleanDemandCategory,cleanDemandDataDetailVOS);
        //项目需求
        Map<String, List<CleanDemandDataDetailVO>> projectCleanDemandDataDetailByOemCodeAndProductCode =
                getCleanDemandDataByCategory(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(),
                        cleanDemandCategory,cleanDemandDataDetailVOS);


        // 查询主机厂装车日历

        Map<String, Map<String, List<Date>>> resourceCalendarVOMap = getResourceByOemByProduct(oemCodes,
                months, vehicleModelScope);

        // 查询最近两个月(包含当月)的一致性预测需求数据
        List<ConsistenceDemandForecastDataDetailVO> latestPublishedVersionData =
                consistenceDemandForecastDataDetailService.getLatestPublishedVersionData(oemCodeScope, productScope);
        // 一致性预测需求数据id和demandCategory映射关系
        Map<String,String> consistenceDemandForecastDataIdMap =
                getConsistenceDemandCategory(latestPublishedVersionData);
        //量产需求
        Map<String, CleanDemandDataDetailVO> forecastCleanDemandDataDetailVOMap = new HashMap<>();
        //项目需求
        Map<String, CleanDemandDataDetailVO> projectForecastCleanDemandDataDetailVOMap = new HashMap<>();
        latestPublishedVersionData.stream()
                .filter(t -> months.contains(DateUtils.dateToString(t.getForecastTime(), "yyyy-MM")))
                .forEach(t -> {
                    Map<String, List<Date>> map = resourceCalendarVOMap.computeIfAbsent(t.getOemCode(),
                            key -> new HashMap<>());
                    List<Date> resourceCalendarVOS = map.computeIfAbsent(DateUtils
                            .dateToString(t.getForecastTime(), "yyyy-MM"), k -> Lists.newArrayList());
                    // TODO:resourceMap已经对日期做了判空，此处应该不需要了
                    if (CollectionUtils.isEmpty(resourceCalendarVOS)) {
                        List<Date> list = DateUtils.getIntervalDates(DateUtils.getMonthFirstDay(t.getForecastTime()),
                                DateUtils.getMonthLastDay(t.getForecastTime()));
                        // if (isWeekend(d)) {
                        //     continue;
                        // }
                        resourceCalendarVOS.addAll(list);
                        //map.put(t.getOemCode(), resourceCalendarVOS);
                    }
                    BigDecimal forecastQuantity = t.getForecastQuantity() == null ? BigDecimal.ZERO : t.getForecastQuantity();
                    int forecastQtyAvg = forecastQuantity.intValue() / resourceCalendarVOS.size();
                    int remainder = forecastQuantity.intValue() % resourceCalendarVOS.size();
                    Collections.sort(resourceCalendarVOS);
                    String demandCategory = consistenceDemandForecastDataIdMap.get(t.getConsistenceDemandForecastDataId());
                    for (int i = 0; i < resourceCalendarVOS.size(); i++) {

                        int share = forecastQtyAvg;
                        CleanDemandDataDetailVO cleanDemandDataDetail = new CleanDemandDataDetailVO();
                        cleanDemandDataDetail.setDemandTime(resourceCalendarVOS.get(i));
                        cleanDemandDataDetail.setOemCode(t.getOemCode());
                        cleanDemandDataDetail.setProductCode(t.getProductCode());
                        if (i < remainder) {
                            share++;
                        }
                        cleanDemandDataDetail.setDemandQuantity(BigDecimal.valueOf(share));
                        String key = t.getOemCode() + "&&" + t.getProductCode() + "&&"
                                + DateUtils.dateToString(resourceCalendarVOS.get(i),
                                DateUtils.COMMON_DATE_STR3);
                        if (StringUtils.isEmpty(demandCategory) || demandCategory.equals(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode())){
                            forecastCleanDemandDataDetailVOMap.put(key, cleanDemandDataDetail);
                        }else {
                            projectForecastCleanDemandDataDetailVOMap.put(key, cleanDemandDataDetail);
                        }

                        //forecastCleanDemandDataDetailVOMap.put(key, cleanDemandDataDetail);
                    }
                });

        List<CleanDemandDataDetailVO> outputCleanDemandDataDetailList =
                mergeCleanDemandAndForecastDemandByCategory(outputCleanDemandDataDetailByOemCodeAndProductCode,
                        resourceCalendarVOMap,lastDay,cleanDemandLastDay,forecastCleanDemandDataDetailVOMap);
        List<CleanDemandDataDetailVO> projectCleanDemandDataDetailList =
                mergeCleanDemandAndForecastDemandByCategory(projectCleanDemandDataDetailByOemCodeAndProductCode,
                        resourceCalendarVOMap,lastDay,cleanDemandLastDay,projectForecastCleanDemandDataDetailVOMap);
        Map<String, List<CleanDemandDataDetailVO>> result = new HashMap<>();
        result.put(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),outputCleanDemandDataDetailList);
        result.put(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(),projectCleanDemandDataDetailList);
        return result;
    }

    private Map<String,List<CleanDemandDataDetailVO>> getCleanDemandDataByCategory(
            String demandCategory, Map<String,String> cleanDemandCategory,
            List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS){
        return cleanDemandDataDetailVOS.stream()
                        .filter(t ->
                                StringUtils.isNotEmpty(cleanDemandCategory.get(t.getCleanDemandDataId())) && cleanDemandCategory.get(t.getCleanDemandDataId()).equals(demandCategory))
                        .collect(Collectors.groupingBy(t -> t.getOemCode() + "&&" + t.getProductCode()));
    }

    private List<CleanDemandDataDetailVO> mergeCleanDemandAndForecastDemandByCategory(
            Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailByOemCodeAndProductCode,
            Map<String, Map<String,List<Date>>> resourceCalendarVOMap, Date lastDay,Date cleanDemandLastDay,
            Map<String,CleanDemandDataDetailVO> forecastCleanDemandDataDetailVOMap) {

        List<CleanDemandDataDetailVO> result = new ArrayList<>();
        cleanDemandDataDetailByOemCodeAndProductCode.keySet().forEach(oemCodeProductCode -> {
            String oemCode = oemCodeProductCode.split("&&")[0];
            Map<String, List<Date>> resourceCalendarByMonth =
                    resourceCalendarVOMap.computeIfAbsent(oemCode, key -> new HashMap<>());
            List<Date> resourceCalendarPerOem =
                    resourceCalendarByMonth.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(resourceCalendarByMonth)) {
                Date monthFirstDay = DateUtils.getMonthFirstDay(lastDay);
                Date monthLastDay = DateUtils.getMonthLastDay(DateUtils.moveMonth(lastDay, 3));
                List<Date> list = DateUtils.getIntervalDates(monthFirstDay, monthLastDay);
                for (Date date : list) {
                    if (isWeekend(date)) {
                        continue;
                    }
                    resourceCalendarPerOem.add(date);
                }
            }

            Map<String, CleanDemandDataDetailVO> cleanDemandDataDetailMapDemandTime =
                    cleanDemandDataDetailByOemCodeAndProductCode.get(oemCodeProductCode).stream()
                            .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime(),
                                    DateUtils.COMMON_DATE_STR3), Function.identity(), (t1, t2) -> t2));

            resourceCalendarPerOem.stream().distinct().forEach(resourceCalendarVO -> {
                String key = DateUtils.dateToString(resourceCalendarVO, DateUtils.COMMON_DATE_STR3);
                CleanDemandDataDetailVO cleanDemandDataDetailVO = cleanDemandDataDetailMapDemandTime.remove(key);

                if (cleanDemandDataDetailVO != null) {
                    result.add(cleanDemandDataDetailVO);
                    return;
                }
                if (!resourceCalendarVO.after(cleanDemandLastDay)) {
                    return;
                }
                // 当前装车日历没有日需求，用预测数据填充
                String key2 = oemCodeProductCode + "&&" + key;
                CleanDemandDataDetailVO cleanDemandDataDetailVO1 = forecastCleanDemandDataDetailVOMap.get(key2);
                if (cleanDemandDataDetailVO1 != null) {
                    result.add(cleanDemandDataDetailVO1);
                }
            });
            result.addAll(cleanDemandDataDetailMapDemandTime.values());
        });

        return result;
    }

    private Map<String,String> getCleanDemandCategory(List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS){
        List<String> cleanDemandDataIds = cleanDemandDataDetailVOS.stream().map(CleanDemandDataDetailVO::getCleanDemandDataId)
                .distinct().collect(Collectors.toList());
        List<CleanDemandDataVO> cleanDemandDataVOS = cleanDemandDataService
                .selectByPrimaryKeys(cleanDemandDataIds);

        return cleanDemandDataVOS.stream().collect(Collectors.toMap(BaseVO::getId,
                CleanDemandDataVO::getDemandCategory));
    }
    
    private Map<String,String> getCleanDemandExitCategory(List<CleanDemandDataDetailExitVO> cleanDemandDataDetailExitVOS){
        List<String> cleanDemandDataExitIds = cleanDemandDataDetailExitVOS.stream().map(CleanDemandDataDetailExitVO::getCleanDemandDataExitId)
                .distinct().collect(Collectors.toList());
        List<CleanDemandDataExitVO> cleanDemandDataExitVOS = cleanDemandDataExitService
                .selectByPrimaryKeys(cleanDemandDataExitIds);

        return cleanDemandDataExitVOS.stream().collect(Collectors.toMap(BaseVO::getId,
                CleanDemandDataExitVO::getDemandCategory));
    }

    private Map<String,String> getConsistenceDemandCategory(List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOS){
        List<String> consistenceDemandForecastDateIds = consistenceDemandForecastDataDetailVOS.stream()
                .map(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(consistenceDemandForecastDateIds)) {
            return new HashMap<>(2);
        }
        List<ConsistenceDemandForecastDataVO> consistenceDemandForecastDataVOS =
                consistenceDemandForecastDataService.selectByPrimaryKeys(consistenceDemandForecastDateIds);
        return consistenceDemandForecastDataVOS.stream()
                .collect(Collectors.toMap(ConsistenceDemandForecastDataVO::getId,
                        ConsistenceDemandForecastDataVO::getDemandCategory, (v1, v2) -> v2));
    }

    private Map<String, Map<String, List<Date>>> getResourceByOemByProduct(List<String> oemCodes,
                                                                                         List<String> months,
                                                                                         List<String> vehicles) {
        return dfpResourceCalendarService.getMonthMapByOemByVehicle(oemCodes, vehicles, months);
    }

    public List<String> getMonths(Date lastDay) {
        List<String> months = new ArrayList<>();
        months.add(DateUtils.dateToString(lastDay, DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 1), DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 2), DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 3), DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 4), DateUtils.YEAR_MONTH));
        return months;
    }

    public boolean isWeekend(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

    }
    
    /**
     * 获取国内/出口 MES/非MES 在途数据
     * @param deliveryPlanCalcSpace
     * @param oemCodes
     * @param productCodes
     * @param rangeData
     * @param deliveryPlanCalcFlag 是否是发货计划计算
     */
    public void queryOemReceive(DeliveryPlanCalcSpace deliveryPlanCalcSpace, List<String> oemCodes,
			List<String> productCodes, String rangeData, Boolean deliveryPlanCalcFlag) {
    	setInlandMesReceive(deliveryPlanCalcSpace, oemCodes, productCodes, rangeData, deliveryPlanCalcFlag);
        //处理出口+MES的中转库库存数据(在途数据)
        setExitMesReceive(deliveryPlanCalcSpace, oemCodes, productCodes, rangeData, deliveryPlanCalcFlag);
    }
    
    /**
     * 处理国内+MES/非MES的中转库库存数据(在途数据)
     * @param deliveryPlanCalcSpace
     * @param oemCodeScope
     * @param productScope
     * @param rangeData
     * @param deliveryPlanCalcFlag 是否是发货计划计算
     */
	private void setInlandMesReceive(DeliveryPlanCalcSpace deliveryPlanCalcSpace, List<String> oemCodeScope,
			List<String> productScope, String rangeData, Boolean deliveryPlanCalcFlag) {
		List<String> inlandOemCodes = deliveryPlanCalcSpace.getOemVOList().stream()
        		.filter(e -> oemCodeScope.contains(e.getOemCode()) && OemTradeTypeEnum.IN.getCode().equals(e.getMarketType()))
        		.map(OemVO::getOemCode)
        		.collect(Collectors.toList());
		deliveryPlanCalcSpace.setInlandMesOemCodes(new ArrayList<>());
		deliveryPlanCalcSpace.setInlandMesReceiveMap(new HashMap<>());
		deliveryPlanCalcSpace.setInlandNotMesOemCodes(new ArrayList<>());
		deliveryPlanCalcSpace.setInlandNotMesReceiveMap(new HashMap<>());
		if(CollectionUtils.isEmpty(inlandOemCodes)) {
			return;
		}
    	Map<String, String> oemStockPointMaps = oemStockPointMapService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"oemCodeList" , inlandOemCodes,
    			"stockPointCodesNotInList", Arrays.asList(rangeData)))
        		.stream().collect(Collectors.toMap(OemStockPointMapVO::getOemCode,
        				OemStockPointMapVO::getStockPointCode,(v1, v2) -> v1));
        List<String> stockPointCodes = new ArrayList<>(oemStockPointMaps.values());
        //获取数据来源是MES的库存点
        if(oemStockPointMaps.isEmpty()) {
        	return;
        }
        List<NewStockPointVO> stockPointList = newMdsFeign.selectNewStockPointVOByParams(SystemHolder.getScenario(),
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "stockPointCodes", stockPointCodes));

        //维护主机厂是国内，MES的在途数据
    	List<String> mesStockPointCodes = stockPointList.stream().filter(e -> "MES".equals(e.getInterfaceSource()))
        		.map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
    	setInlandMesReceiveMap(deliveryPlanCalcSpace, productScope, oemStockPointMaps,
				mesStockPointCodes, deliveryPlanCalcFlag);
    	//维护主机厂是国内，非MES的在途数据
    	List<String> notFesStockPointCodes = stockPointList.stream().filter(e -> !"MES".equals(e.getInterfaceSource()))
        		.map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
    	setInlandNotMesReceiveMap(deliveryPlanCalcSpace, productScope, oemStockPointMaps,
    			notFesStockPointCodes, deliveryPlanCalcFlag);
	}
	
	/**
	 * 维护主机厂是国内，MES的在途数据
	 * @param deliveryPlanCalcSpace
	 * @param productScope
	 * @param oemStockPointMaps
	 * @param mesStockPointCodes
	 * @param deliveryPlanCalcFlag 是否是发货计划计算
	 */
	private void setInlandMesReceiveMap(DeliveryPlanCalcSpace deliveryPlanCalcSpace, List<String> productScope,
			Map<String, String> oemStockPointMaps, List<String> mesStockPointCodes, Boolean deliveryPlanCalcFlag) {
		Map<String, Integer> oemTransportTimeMap = deliveryPlanCalcSpace.getOemTransportTimeMap();
		//获取对应的中转库发货数据
		List<String> inlandMesOemCodes = new ArrayList<>();
    	for (Entry<String, String> entry : oemStockPointMaps.entrySet()) {
			if(mesStockPointCodes.contains(entry.getValue()) && !inlandMesOemCodes.contains(entry.getKey())) {
				inlandMesOemCodes.add(entry.getKey());
			}
		}
    	if(CollectionUtils.isEmpty(inlandMesOemCodes)) {
    		return;
    	}
    	//维护国内，MES的主机厂
    	deliveryPlanCalcSpace.setInlandMesOemCodes(inlandMesOemCodes);

		//查询中转库发货记录
		List<WarehouseReleaseToWarehouseVO> notReceives = warehouseReleaseToWarehouseService
				.selectNotReceiveInland(inlandMesOemCodes, productScope);
		//推移对应的发货时间
		Date nowLastTime = DateUtils.getDayLastTime(new Date());
		Date endTime = DateUtils.moveDay(nowLastTime, 89);
		notReceives.forEach( e-> {
			Date creationDate = e.getCreationDate();
			creationDate = DateUtils.moveMinute(creationDate, oemTransportTimeMap.getOrDefault(e.getOemCode(), 0));
			if(creationDate.compareTo(nowLastTime) <= 0) {
				creationDate = nowLastTime;
			}
			if(!deliveryPlanCalcFlag && creationDate.compareTo(endTime) > 0) {
				e.setSumQty(BigDecimal.ZERO);
			}
			e.setCreationDate(creationDate);
		});
		if(deliveryPlanCalcFlag) {
			//按照主机厂，物料，发货时间统计对应的在途数量（小于等于当天的，算当天）
			Map<String, BigDecimal> inlandMesReceiveMap = notReceives.stream()
	                .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getItemCode(),
	                		DateUtils.dateToString(e.getCreationDate())) ,
	                Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseVO::getSumQty, BigDecimal::add)));
			deliveryPlanCalcSpace.setInlandMesReceiveMap(inlandMesReceiveMap);
		}else {
			//按照主机厂，物料，发货时间统计对应的在途数量（小于等于当天的，算当天）
			Map<String, BigDecimal> inlandMesReceiveMap = notReceives.stream()
	                .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getItemCode()) ,
	                Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseVO::getSumQty, BigDecimal::add)));
			deliveryPlanCalcSpace.setInlandMesReceiveMap(inlandMesReceiveMap);
		}
	}

	/**
	 * 维护主机厂是国内，非MES的在途数据
	 * @param deliveryPlanCalcSpace
	 * @param productScope
	 * @param oemStockPointMaps
	 * @param notFesStockPointCodes
	 * @param deliveryPlanCalcFlag 是否是发货计划计算
	 */
	private void setInlandNotMesReceiveMap(DeliveryPlanCalcSpace deliveryPlanCalcSpace, List<String> productScope,
			Map<String, String> oemStockPointMaps, List<String> notFesStockPointCodes, Boolean deliveryPlanCalcFlag) {
		Map<String, Integer> oemTransportTimeMap = deliveryPlanCalcSpace.getOemTransportTimeMap();
		//获取对应的中转库发货数据
		List<String> inlandNotMesOemCodes = new ArrayList<>();
    	for (Entry<String, String> entry : oemStockPointMaps.entrySet()) {
			if(notFesStockPointCodes.contains(entry.getValue()) && !inlandNotMesOemCodes.contains(entry.getKey())) {
				inlandNotMesOemCodes.add(entry.getKey());
			}
		}
    	if(CollectionUtils.isEmpty(inlandNotMesOemCodes)) {
    		return;
    	}
    	deliveryPlanCalcSpace.setInlandNotMesOemCodes(inlandNotMesOemCodes);
    	//2.查询中转库这些主机厂的数据
    	Map<String, Object> queryMap = new HashMap<>();
    	queryMap.put("oemCodeList", inlandNotMesOemCodes);
    	queryMap.put("productCodeList", productScope);
    	queryMap.put("beginDate", DateUtils.dateToString(DateUtils.getDayFirstTime(DateUtils.moveDay(new Date(), -14)),
    			DateUtils.COMMON_DATE_STR1));
    	queryMap.put("endDate", DateUtils.dateToString(DateUtils.getDayLastTime(new Date()), DateUtils.COMMON_DATE_STR1));
    	List<WarehouseReceiveCheckVO> notReceives = warehouseReleaseToWarehouseService
    			.selectNotReceiveInlandStatistics(queryMap);
    	if(CollectionUtils.isEmpty(notReceives)) {
    		return;
        }
		//获取发货清单号最大的到达时间
    	Map<String, Date> listNumMaxTimeMap = notReceives.stream()
    		    .collect(Collectors.toMap( WarehouseReceiveCheckVO::getListNum,
    		        WarehouseReceiveCheckVO::getCreationDate,
    		        (date1, date2) -> date1.after(date2) ? date1 : date2
    		    ));
		//处理入库数量数据
        List<String> listNums = notReceives.stream().map(WarehouseReceiveCheckVO::getListNum).distinct()
    			.collect(Collectors.toList());
        List<String> itemCodes = notReceives.stream().map(WarehouseReceiveCheckVO::getItemCode).distinct()
    			.collect(Collectors.toList());
        List<String> shipmentLocatorCodes = notReceives.stream().map(WarehouseReceiveCheckVO::getShipmentLocatorCode).distinct()
    			.collect(Collectors.toList());
        List<InventoryRecieveConfirmationVO> recieveConfirmationList = inventoryRecieveConfirmationService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"listNums" , listNums, "itemCodes" , itemCodes, "shipmentLocatorCodes", shipmentLocatorCodes));
        Map<String, InventoryRecieveConfirmationVO> recieveConfirmationMap = recieveConfirmationList.stream()
        		.collect(Collectors.toMap(e -> String.join("&", e.getListNum(), e.getItemCode(), e.getShipmentLocatorCode()),
        				e->e,(v1, v2) -> v1));
        Date nowLastTime = DateUtils.getDayLastTime(new Date());
        Date endTime = DateUtils.moveDay(nowLastTime, 89);
        notReceives.forEach( e -> {
			e.setStoreQty(BigDecimal.ZERO);
			InventoryRecieveConfirmationVO inventoryRecieveConfirmationVO = recieveConfirmationMap
					.get(String.join("&", e.getListNum(), e.getItemCode(), e.getShipmentLocatorCode()));
			if(inventoryRecieveConfirmationVO != null) {
				e.setStoreQty(inventoryRecieveConfirmationVO.getStoreQty());
				e.setInventoryRecieveConfirmationId(inventoryRecieveConfirmationVO.getId());
			}
			e.setReceiveQty(e.getSumQty().subtract(e.getStoreQty()));
			//处理预计到达时间
			Date estimatedArrivalTime = listNumMaxTimeMap.get(e.getListNum());
			estimatedArrivalTime = DateUtils.moveMinute(estimatedArrivalTime,
					oemTransportTimeMap.getOrDefault(e.getOemCode(), 0));
			if(estimatedArrivalTime.compareTo(nowLastTime) <= 0) {
				estimatedArrivalTime = nowLastTime;
			}
			if(!deliveryPlanCalcFlag && estimatedArrivalTime.compareTo(endTime) > 0) {
				e.setReceiveQty(BigDecimal.ZERO);
			}
			e.setEstimatedArrivalTime(estimatedArrivalTime);
		});
        if(deliveryPlanCalcFlag) {
        	Map<String, BigDecimal> inlandNotMesReceiveMap = notReceives.stream()
                    .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getItemCode(),
                    		DateUtils.dateToString(e.getEstimatedArrivalTime())) ,
                    Collectors.reducing(BigDecimal.ZERO, WarehouseReceiveCheckVO::getReceiveQty, BigDecimal::add)));
    		deliveryPlanCalcSpace.setInlandNotMesReceiveMap(inlandNotMesReceiveMap);
        }else {
        	Map<String, BigDecimal> inlandNotMesReceiveMap = notReceives.stream()
                    .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getItemCode()) ,
                    Collectors.reducing(BigDecimal.ZERO, WarehouseReceiveCheckVO::getReceiveQty, BigDecimal::add)));
    		deliveryPlanCalcSpace.setInlandNotMesReceiveMap(inlandNotMesReceiveMap);
        }
	}
	
	/**
     * 处理出口+MES/非MES的中转库库存数据(在途数据)
     * @param deliveryPlanCalcSpace
     * @param oemCodeScope
     * @param productScope
     * @param rangeData
     * @param deliveryPlanCalcFlag 是否是发货计划计算
     */
	private void setExitMesReceive(DeliveryPlanCalcSpace deliveryPlanCalcSpace, List<String> oemCodeScope,
			List<String> productScope, String rangeData, Boolean deliveryPlanCalcFlag) {
		List<String> exitOemCodes = deliveryPlanCalcSpace.getOemVOList().stream()
        		.filter(e -> oemCodeScope.contains(e.getOemCode()) && OemTradeTypeEnum.OUT.getCode().equals(e.getMarketType()))
        		.map(OemVO::getOemCode)
        		.collect(Collectors.toList());
		deliveryPlanCalcSpace.setExitNotMesOemCodes(new ArrayList<>());
		deliveryPlanCalcSpace.setExitNotMesReceiveMap(new HashMap<>());
		deliveryPlanCalcSpace.setExitMesOemCodes(new ArrayList<>());
		deliveryPlanCalcSpace.setExitMesReceiveMap(new HashMap<>());
		if(CollectionUtils.isEmpty(exitOemCodes)) {
			return;
		}
    	Map<String, String> oemStockPointMaps = oemStockPointMapService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"oemCodeList" , exitOemCodes,
    			"stockPointCodesNotInList", Arrays.asList(rangeData)))
        		.stream().collect(Collectors.toMap(OemStockPointMapVO::getOemCode,
        				OemStockPointMapVO::getStockPointCode,(v1, v2) -> v1));
        List<String> stockPointCodes = new ArrayList<>(oemStockPointMaps.values());
        //获取数据来源是MES的库存点
        if(oemStockPointMaps.isEmpty()) {
        	return;
        }
        List<NewStockPointVO> stockPointList = newMdsFeign.selectNewStockPointVOByParams(SystemHolder.getScenario(),
        		ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(), "stockPointCodes", stockPointCodes));
    	//维护主机厂是出口，非MES的在途数据
    	List<String> notMesStockPointCodes = stockPointList.stream().filter(e -> !"MES".equals(e.getInterfaceSource()))
        		.map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
    	setExitNotMesReceiveMap(deliveryPlanCalcSpace, productScope, exitOemCodes, oemStockPointMaps,
				notMesStockPointCodes, deliveryPlanCalcFlag);
    	//维护主机厂是出口，MES的在途数据
    	List<String> mesStockPointCodes = stockPointList.stream().filter(e -> "MES".equals(e.getInterfaceSource()))
        		.map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
    	setExitMesReceiveMap(deliveryPlanCalcSpace, productScope, exitOemCodes, oemStockPointMaps,
    			mesStockPointCodes, deliveryPlanCalcFlag);
	}

	/**
	 * 维护主机厂是出口，非MES的在途数据
	 * @param deliveryPlanCalcSpace
	 * @param productScope
	 * @param exitOemCodes
	 * @param oemStockPointMaps
	 * @param notMesStockPointCodes
	 * @param deliveryPlanCalcFlag 是否是发货计划计算
	 */
	private void setExitNotMesReceiveMap(DeliveryPlanCalcSpace deliveryPlanCalcSpace, List<String> productScope,
			List<String> exitOemCodes, Map<String, String> oemStockPointMaps, List<String> notMesStockPointCodes,
			Boolean deliveryPlanCalcFlag) {
		//获取对应的中转库发货数据
		List<String> exitNotMesOemCodes = new ArrayList<>();
    	for (Entry<String, String> entry : oemStockPointMaps.entrySet()) {
			if(notMesStockPointCodes.contains(entry.getValue()) && !exitNotMesOemCodes.contains(entry.getKey())) {
				exitNotMesOemCodes.add(entry.getKey());
			}
		}
    	if(CollectionUtils.isEmpty(exitNotMesOemCodes)) {
    		return;
    	}
    	deliveryPlanCalcSpace.setExitNotMesOemCodes(exitNotMesOemCodes);
    	//查询中转库与主机厂库存提报(出口)数据
    	List<OemInventorySubmissionExitVO> submissionExit = oemInventorySubmissionExitService.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
    			"oemCodes", exitOemCodes, "productCodes", productScope,
    			"startSubmissionDateStr", DateUtils.dateToString(DateUtils.getDayFirstTime(new Date()),
    					DateUtils.COMMON_DATE_STR1))).stream().filter(e -> e.getTransitWaitQuantity() != null)
    			.collect(Collectors.toList());
    	if(CollectionUtils.isEmpty(submissionExit)) {
    		return;
    	}
    	if(deliveryPlanCalcFlag) {
    		Map<String, BigDecimal> exitNotMesReceiveMap = submissionExit.stream()
                    .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getProductCode(),
                    		DateUtils.dateToString(e.getSubmissionDate())) ,
                    Collectors.reducing(BigDecimal.ZERO, OemInventorySubmissionExitVO::getTransitWaitQuantity, BigDecimal::add)));
        	deliveryPlanCalcSpace.setExitNotMesReceiveMap(exitNotMesReceiveMap);
    	}else {
    		Date nowLastTime = DateUtils.getDayLastTime(new Date());
            Date endTime = DateUtils.moveDay(nowLastTime, 89);
            Map<String, BigDecimal> exitNotMesReceiveMap = submissionExit.stream()
            		.filter( e-> e.getSubmissionDate().compareTo(endTime) <= 0)
                    .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getProductCode()) ,
                    Collectors.reducing(BigDecimal.ZERO, OemInventorySubmissionExitVO::getTransitWaitQuantity, BigDecimal::add)));
        	deliveryPlanCalcSpace.setExitNotMesReceiveMap(exitNotMesReceiveMap);
    	}
	}
	
	/**
	 * 维护主机厂是出口，MES的在途数据
	 * @param deliveryPlanCalcSpace
	 * @param productScope
	 * @param exitOemCodes
	 * @param oemStockPointMaps
	 * @param mesStockPointCodes
	 * @param deliveryPlanCalcFlag 是否是发货计划计算
	 */
	private void setExitMesReceiveMap(DeliveryPlanCalcSpace deliveryPlanCalcSpace, List<String> productScope,
			List<String> exitOemCodes, Map<String, String> oemStockPointMaps, List<String> mesStockPointCodes,
			Boolean deliveryPlanCalcFlag) {
		Map<String, Integer> oemTransportTimeMap = deliveryPlanCalcSpace.getOemTransportTimeMap();
		//维护主机厂是出口，MES数据集合
		List<String> exitMesOemCodes = new ArrayList<>();
    	for (Entry<String, String> entry : oemStockPointMaps.entrySet()) {
			if(mesStockPointCodes.contains(entry.getValue()) && !exitMesOemCodes.contains(entry.getKey())) {
				exitMesOemCodes.add(entry.getKey());
			}
		}
    	if(CollectionUtils.isEmpty(exitMesOemCodes)) {
    		return;
    	}
    	deliveryPlanCalcSpace.setExitMesOemCodes(exitMesOemCodes);
    	//维护主机厂是出口，MES在途Map
    	Map<String, Object> queryMap = new HashMap<>();
    	queryMap.put("oemCodeList", exitMesOemCodes);
    	queryMap.put("productCodeList", productScope);
    	List<WarehouseReceiveCheckExitVO> notReceives = abroadWarehouseReleaseRecordService
    			.selectNotReceiveEixtStatistics(queryMap);
    	if(CollectionUtils.isEmpty(notReceives)) {
    		return;
        }
    	notReceives.forEach( e-> {
    		if(StringUtils.isEmpty(e.getContainerNum())) {
    			e.setContainerNum("");
    		}
    	});
		//获取发货清单号最大的到达时间
    	Map<String, Date> listNumMaxTimeMap = notReceives.stream()
    		    .collect(Collectors.toMap( WarehouseReceiveCheckExitVO::getListNum,
    		    		WarehouseReceiveCheckExitVO::getCreationDate,
    		        (date1, date2) -> date1.after(date2) ? date1 : date2
    		    ));
		//处理入库数量数据
        List<String> listNums = notReceives.stream().map(WarehouseReceiveCheckExitVO::getListNum).distinct()
    			.collect(Collectors.toList());
        List<String> itemCodes = notReceives.stream().map(WarehouseReceiveCheckExitVO::getItemCode).distinct()
    			.collect(Collectors.toList());
        List<String> shipmentLocatorCodes = notReceives.stream().map(WarehouseReceiveCheckExitVO::getShipmentLocatorCode).distinct()
    			.collect(Collectors.toList());
        List<InventoryRecieveConfirmationExitVO> recieveConfirmationExitList = inventoryRecieveConfirmationExitService
        		.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
    			"listNums" , listNums, "itemCodes" , itemCodes, "shipmentLocatorCodes", shipmentLocatorCodes));
        Map<String, InventoryRecieveConfirmationExitVO> recieveConfirmationExitMap = recieveConfirmationExitList.stream()
        		.collect(Collectors.toMap(e -> String.join("&", e.getListNum(), e.getItemCode(), 
        				e.getShipmentLocatorCode(), e.getContainerNum()),
        				e->e,(v1, v2) -> v1));
        Date nowLastTime = DateUtils.getDayLastTime(new Date());
        Date endTime = DateUtils.moveDay(nowLastTime, 89);
        notReceives.forEach( e -> {
			e.setStoreQty(BigDecimal.ZERO);
			InventoryRecieveConfirmationExitVO inventoryRecieveConfirmationVO = recieveConfirmationExitMap
					.get(String.join("&", e.getListNum(), e.getItemCode(), e.getShipmentLocatorCode(), e.getContainerNum()));
			if(inventoryRecieveConfirmationVO != null) {
				e.setStoreQty(inventoryRecieveConfirmationVO.getStoreQty());
			}
			e.setReceiveQty(e.getSumQty().subtract(e.getStoreQty()));
			//处理预计到达时间
			Date estimatedArrivalTime = listNumMaxTimeMap.get(e.getListNum());
			estimatedArrivalTime = DateUtils.moveMinute(estimatedArrivalTime,
					oemTransportTimeMap.getOrDefault(e.getOemCode(), 0));
			if(estimatedArrivalTime.compareTo(nowLastTime) <= 0) {
				estimatedArrivalTime = nowLastTime;
			}
			if(!deliveryPlanCalcFlag && estimatedArrivalTime.compareTo(endTime) > 0) {
				e.setReceiveQty(BigDecimal.ZERO);
			}
			e.setEstimatedArrivalTime(estimatedArrivalTime);
		});
        if(deliveryPlanCalcFlag) {
        	Map<String, BigDecimal> exitMesReceiveMap = notReceives.stream()
                    .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getItemCode(),
                    		DateUtils.dateToString(e.getEstimatedArrivalTime())) ,
                    Collectors.reducing(BigDecimal.ZERO, WarehouseReceiveCheckExitVO::getReceiveQty, BigDecimal::add)));
    		deliveryPlanCalcSpace.setExitMesReceiveMap(exitMesReceiveMap);
        }else {
        	Map<String, BigDecimal> exitMesReceiveMap = notReceives.stream()
                    .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getItemCode()) ,
                    Collectors.reducing(BigDecimal.ZERO, WarehouseReceiveCheckExitVO::getReceiveQty, BigDecimal::add)));
    		deliveryPlanCalcSpace.setExitMesReceiveMap(exitMesReceiveMap);
        }
	}


}
