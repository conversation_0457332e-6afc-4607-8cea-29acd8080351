package com.yhl.scp.dfp.stock.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <code>InventoryBatchDetailDO</code>
 * <p>
 * 库存批次明细DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 09:53:39
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBatchDetailDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 802429630536367300L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 子库存
     */
    private String subinventory;
    /**
     * 子库存描述
     */
    private String subinventoryDescription;
    /**
     * 货位
     */
    private String freightSpace;
    /**
     * 货位描述
     */
    private String freightSpaceDescription;
    /**
     * 批次
     */
    private String batch;
    /**
     * 条码号
     */
    private String barCode;
    /**
     * 现有量
     */
    private String currentQuantity;
    /**
     * 客户号
     */
    private String customerNum;
    /**
     * 零件号
     */
    private String partNum;
    /**
     * 入库时间
     */
    private String assignedTime;
    /**
     * 最后更新时间
     */
    private String lastUpdateDate;
    /**
     * 库龄
     */
    private String stockAge;
    /**
     * 库龄天数
     */
    private String stockAgeDay;
    /**
     * 保质期
     */
    private String warrantyDate;
    /**
     * 距离失效时间
     */
    private String distanceEnableDate;
    /**
     * 来源类型
     */
    private String sourceType;
    /**
     * 原始报文组织ID
     */
    private String originalOrgId;
    /**
     * 版本号
     */
    private Integer versionValue;
    /**
     * 分配状态
     */
    private String allocationStatus;
    /**
     * 原始物料编码
     */
    private String originalProductCode;
    /**
     * 工序
     */
    private String operationCode;
    /**
     * 质检状态
     */
    private String qcStatus;
}
