package com.yhl.scp.dfp.delivery.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.config.DeliveryPlanChangeWebSocketHandler;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanPublishedCompareDayConvertor;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanPublishedCompareDayDO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanPublishedCompareDayDomainService;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedCompareDayDTO;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedDTO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedCompareDayDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedCompareDayPO;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedCompareDayService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.MessageTypeEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.untils.UserMessageUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.resource.vo.StandardResourceBasicVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanPublishedCompareDayServiceImpl</code>
 * <p>
 * 发货计划按天对比表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-13 14:14:16
 */
@Slf4j
@Service
public class DeliveryPlanPublishedCompareDayServiceImpl extends AbstractService implements DeliveryPlanPublishedCompareDayService {

    @Resource
    private DeliveryPlanPublishedCompareDayDao deliveryPlanPublishedCompareDayDao;

    @Resource
    private DeliveryPlanPublishedCompareDayDomainService deliveryPlanPublishedCompareDayDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private DeliveryPlanChangeWebSocketHandler deliveryPlanChangeWebSocketHandler;

    @Resource
    private UserMessageUtils userMessageUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(DeliveryPlanPublishedCompareDayDTO deliveryPlanPublishedCompareDayDTO) {
        // 0.数据转换
        DeliveryPlanPublishedCompareDayDO deliveryPlanPublishedCompareDayDO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Do(deliveryPlanPublishedCompareDayDTO);
        DeliveryPlanPublishedCompareDayPO deliveryPlanPublishedCompareDayPO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Po(deliveryPlanPublishedCompareDayDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedCompareDayDomainService.validation(deliveryPlanPublishedCompareDayDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(deliveryPlanPublishedCompareDayPO);
        deliveryPlanPublishedCompareDayDao.insert(deliveryPlanPublishedCompareDayPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(DeliveryPlanPublishedCompareDayDTO deliveryPlanPublishedCompareDayDTO) {
        // 0.数据转换
        DeliveryPlanPublishedCompareDayDO deliveryPlanPublishedCompareDayDO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Do(deliveryPlanPublishedCompareDayDTO);
        DeliveryPlanPublishedCompareDayPO deliveryPlanPublishedCompareDayPO = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Po(deliveryPlanPublishedCompareDayDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        deliveryPlanPublishedCompareDayDomainService.validation(deliveryPlanPublishedCompareDayDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(deliveryPlanPublishedCompareDayPO);
        deliveryPlanPublishedCompareDayDao.update(deliveryPlanPublishedCompareDayPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DeliveryPlanPublishedCompareDayDTO> list) {
        List<DeliveryPlanPublishedCompareDayPO> newList = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        deliveryPlanPublishedCompareDayDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DeliveryPlanPublishedCompareDayDTO> list) {
        List<DeliveryPlanPublishedCompareDayPO> newList = DeliveryPlanPublishedCompareDayConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        deliveryPlanPublishedCompareDayDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return deliveryPlanPublishedCompareDayDao.deleteBatch(idList);
        }
        return deliveryPlanPublishedCompareDayDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DeliveryPlanPublishedCompareDayVO selectByPrimaryKey(String id) {
        DeliveryPlanPublishedCompareDayPO po = deliveryPlanPublishedCompareDayDao.selectByPrimaryKey(id);
        return DeliveryPlanPublishedCompareDayConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_COMPARE_DAY")
    public List<DeliveryPlanPublishedCompareDayVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        queryCriteriaParam = assemblyPermission(queryCriteriaParam);
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DELIVERY_PLAN_PUBLISHED_COMPARE_DAY")
    public List<DeliveryPlanPublishedCompareDayVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DeliveryPlanPublishedCompareDayVO> dataList = deliveryPlanPublishedCompareDayDao.selectByCondition(sortParam, queryCriteriaParam);
        DeliveryPlanPublishedCompareDayServiceImpl target = springBeanUtils.getBean(DeliveryPlanPublishedCompareDayServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO> selectByParams(Map<String, Object> params) {
        List<DeliveryPlanPublishedCompareDayPO> list = deliveryPlanPublishedCompareDayDao.selectByParams(params);
        return DeliveryPlanPublishedCompareDayConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DELIVERY_PLAN_PUBLISHED_COMPARE_DAY.getCode();
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO> invocation(List<DeliveryPlanPublishedCompareDayVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    public String assemblyPermission(String queryCriteriaParam) {
        // 只查询有物料权限的变更通知
        List<String> plannerProduct = newMdsFeign.getPlannerProduct(SystemHolder.getScenario(),
                SystemHolder.getUserId());
        String inClause = plannerProduct.stream()
                .map(code -> "'" + code + "'")
                .collect(Collectors.joining(", "));
        // 拼接 queryCriteriaParam
        if (com.yhl.platform.common.utils.StringUtils.isEmpty(queryCriteriaParam)) {
            queryCriteriaParam = "and enabled = 'YES'";
            if (CollectionUtils.isNotEmpty(plannerProduct)) {
                queryCriteriaParam += " and product_code in (" + inClause + ")";
            } else {
                queryCriteriaParam += " and 1=2";
            }
        } else {
            queryCriteriaParam += " and enabled = 'YES'";
            if (CollectionUtils.isNotEmpty(plannerProduct)) {
                queryCriteriaParam += " and product_code in (" + inClause + ")";
            } else {
                queryCriteriaParam += " and 1=2";
            }
        }
        return queryCriteriaParam;
    }


    @Async
    @Override
    public void doCompare(List<DeliveryPlanPublishedDTO> oldDataList, String userId, Date startDate, String scenario) {
        if (CollectionUtils.isEmpty(oldDataList) || StringUtils.isEmpty(userId)) {
            List<String> productCodeList
                    = oldDataList.isEmpty() ? new ArrayList<>() : oldDataList.stream().map(DeliveryPlanPublishedDTO::getProductCode)
                    .collect(Collectors.toList());
            log.warn("oldDataList is null or userId is null, productCodeList is {}", String.join("#", productCodeList));
            return;
        }
        String startDateStr = DateUtils.dateToString(startDate);
        log.info("开始执行发货计划发布数据对比，userId is {}, startDateStr is {}", userId, startDateStr);
        // 去掉时分秒
        Date startDateTruncateTime = DateUtils.stringToDate(startDateStr);
        // 查询最新的发货计划数据
        List<String> oemCodes = oldDataList.stream().map(DeliveryPlanPublishedDTO::getOemCode)
                .distinct().collect(Collectors.toList());
        List<String> productCodes = oldDataList.stream().map(DeliveryPlanPublishedDTO::getProductCode)
                .distinct().collect(Collectors.toList());
        List<DeliveryPlanPublishedVO> newDeliveryPlanPublishedVOS = deliveryPlanPublishedService.selectByParams(
                ImmutableMap.of("oemCodes", oemCodes, "productCodes", productCodes, "demandTimeStart", startDateStr));
        List<CollectionValueVO> collectionValueList = ipsFeign.getByCollectionCode("DELIVERY_PLAN_PUBLISHED_COMPARE_DAY");
        if (CollectionUtils.isEmpty(collectionValueList)) {
            log.info("未维护字典表DELIVERY_PLAN_PUBLISHED_COMPARE_DAY");
            return;
        }
        List<String> dayList = collectionValueList.stream().map(CollectionValueVO::getCollectionValue)
                .sorted().collect(Collectors.toList());

        List<DeliveryPlanPublishedCompareDayPO> insertList = new ArrayList<>();
        for (String day : dayList) {
            Date endDate = DateUtils.moveDay(startDateTruncateTime, Integer.parseInt(day) - 1);
            List<String> durList = getDurDayList(startDateTruncateTime, endDate);

            List<DeliveryPlanPublishedVO> newList = newDeliveryPlanPublishedVOS.stream()
                    .filter(newDeliveryPlanPublishedVO -> {
                        String date = DateUtils.dateToString(newDeliveryPlanPublishedVO.getDemandTime());
                        return durList.contains(date);
                    }).collect(Collectors.toList());
            Map<String, Integer> newMap = newList.stream().collect(Collectors.groupingBy(t ->
                            String.join("-",
                                    Optional.ofNullable(t.getOemCode()).orElse(""),
                                    Optional.ofNullable(t.getProductCode()).orElse("")),
                    Collectors.summingInt(DeliveryPlanPublishedVO::getDemandQuantity)));

            List<DeliveryPlanPublishedDTO> oldList = oldDataList.stream()
                    .filter(DeliveryPlanPublishedDTO -> {
                        String date = DateUtils.dateToString(DeliveryPlanPublishedDTO.getDemandTime());
                        return durList.contains(date);
                    }).collect(Collectors.toList());
            /**
             * 因为是按新发布数据维度进行统计的，老数据的时间范围可能是 2025-06-01~2025-06-30, 新数据时间范围是2025-06-02~2025-07-01,
             * 此时按新数据的开始时间算30天的时候，老数据其实只能取到29天的，在算变化率的时候时间范围要一致，所以用老数据的时间范围
             * （2025-06-02~2025-06-30）对新数据进行截断，确保时间范围一致再算变化率
             */
            List<String> oldDayList = oldList.stream().map(t -> DateUtils.dateToString(t.getDemandTime()))
                    .collect(Collectors.toList());
            List<DeliveryPlanPublishedVO> oldAndNewList = newDeliveryPlanPublishedVOS.stream()
                    .filter(newDeliveryPlanPublishedVO -> {
                        String date = DateUtils.dateToString(newDeliveryPlanPublishedVO.getDemandTime());
                        return oldDayList.contains(date);
                    }).collect(Collectors.toList());
            Map<String, Integer> oldAndNewMap = oldAndNewList.stream().collect(Collectors.groupingBy(t ->
                            String.join("-",
                                    Optional.ofNullable(t.getOemCode()).orElse(""),
                                    Optional.ofNullable(t.getProductCode()).orElse("")),
                    Collectors.summingInt(DeliveryPlanPublishedVO::getDemandQuantity)));

            Map<String, Integer> oldMap = oldList.stream().collect(Collectors.groupingBy(t ->
                            String.join("-",
                                    Optional.ofNullable(t.getOemCode()).orElse(""),
                                    Optional.ofNullable(t.getProductCode()).orElse("")),
                    Collectors.summingInt(DeliveryPlanPublishedDTO::getDemandQuantity)));
            for (Map.Entry<String, Integer> entry : newMap.entrySet()) {
                String[] split = entry.getKey().split("-");
                String oemCode = split[0];
                String productCode = split[1];
                Integer newValue = entry.getValue();
                Integer oldValue = oldMap.getOrDefault(entry.getKey(), 0);
                DeliveryPlanPublishedCompareDayPO po = new DeliveryPlanPublishedCompareDayPO();
                po.setId(UUIDUtil.getUUID());
                po.setOemCode(oemCode);
                po.setProductCode(productCode);
                po.setDayStr(day);
                po.setNewDemandQuantity(newValue);
                po.setOldDemandQuantity(oldValue);
                po.setVariableQuantity(newValue - oldValue);
                if (oldValue == 0) {
                    if (newValue == 0) {
                        po.setRateOfChange(BigDecimal.ZERO);
                    } else {
                        po.setRateOfChange(BigDecimal.ONE);
                    }
                } else {
                    Integer oldAndNewQty = oldAndNewMap.getOrDefault(entry.getKey(), 0);
                    int differenceValue = oldAndNewQty - oldValue;
                    po.setRateOfChange(new BigDecimal(differenceValue)
                            .divide(new BigDecimal(oldValue), 4, RoundingMode.HALF_UP));
                }
                insertList.add(po);
            }
        }
        if (CollectionUtils.isNotEmpty(insertList)) {
            BasePOUtils.insertBatchFiller(insertList);
            insertList.forEach(t -> {
                t.setPublisher(userId);
                t.setPublishTime(startDate);
                t.setCreator(userId);
                t.setModifier(userId);
            });
            deliveryPlanPublishedCompareDayDao.insertBatch(insertList);

            // 发送告警消息
            doSendMessage(insertList, scenario);
        }
    }

    private void doSendMessage(List<DeliveryPlanPublishedCompareDayPO> insertList, String scenario) {
        if (CollectionUtils.isEmpty(insertList)) {
            log.info("变更记录为空，跳过发送变更通知");
            return;
        }
        List<CollectionValueVO> changeNoticeRatioList = ipsFeign.getByCollectionCode("CHANGE_NOTICE_RATIO");
        if (CollectionUtils.isEmpty(changeNoticeRatioList)) {
            log.warn("未配置CHANGE_NOTICE_RATIO阈值，跳过变更通知");
            return;
        }
        // value为报警阈值，字典表维护的小数 0.1（超10%），mean为时间范围，即几天内超百分之几则进行报警
        String ratio = changeNoticeRatioList.get(0).getCollectionValue();
        String dayStr = changeNoticeRatioList.get(0).getValueMeaning();
        Map<String, BigDecimal> dayMap = getDayMap(insertList, dayStr);
        PlanningHorizonVO planningHorizonVO = newMdsFeign.selectPlanningHorizon(scenario);
        if (null == planningHorizonVO) {
            log.error("获取计划期间失败");
            return;
        }
        // 锁定期内1天变化率
        Date planStartTime = planningHorizonVO.getPlanStartTime();
        Date planLockEndTime = planningHorizonVO.getPlanLockEndTime();
        String startTimeStr = DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1);
        String endTimeStr = DateUtils.dateToString(planLockEndTime, DateUtils.COMMON_DATE_STR1);

        // 获取需要在锁定期内排产的物料以及有对应权限的人
        List<StandardResourceVO> standardResourceVOList =
                deliveryPlanPublishedCompareDayDao.selectUserIdByDate(startTimeStr,
                        endTimeStr);
        List<UserMessageDTO> userMessageList = new ArrayList<>();
        Map<String, List<String>> userMessageListMap = new HashMap<>();
        if (CollectionUtils.isEmpty(standardResourceVOList)) {
            log.info("锁定期内无需要排产的物料，跳过变更通知");
            return;
        }
        // standardResourceCode里存的是其实是productCode
        Map<String, String> productCodeOfUserIdMap = standardResourceVOList.stream()
                .collect(Collectors.toMap(StandardResourceBasicVO::getStandardResourceCode,
                        StandardResourceBasicVO::getProductionPlanner));
        for (Map.Entry<String, String> entry : productCodeOfUserIdMap.entrySet()) {
            if (!dayMap.containsKey(entry.getKey())) {
                continue;
            }
            BigDecimal decimal = dayMap.get(entry.getKey());
            BigDecimal max = new BigDecimal(ratio);
            BigDecimal min = max.negate();
            // 在 -ratio ~ ratio 范围内，不告警
            if (decimal.compareTo(min) > 0 && decimal.compareTo(max) < 0) {
                continue;
            }
            // 这里是该物料所有有权限的userId,数据用,分割
            String value = entry.getValue();
            if (StringUtils.isEmpty(value)) {
                continue;
            }
            String productCode = entry.getKey();
            String content = productCode + "产品需求发生变更，请注意！";
            for (String userId : value.split(",")) {
                String trimmedUserId = userId.trim();
                userMessageListMap.computeIfAbsent(trimmedUserId, k -> new ArrayList<>()).add(content);
            }
            UserMessageDTO result = UserMessageDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .userId(value)
                    .roleId(null)
                    .messageSource("业务消息")
                    .messageType(MessageTypeEnum.DELIVERY_PLAN_CHANGE_MESSAGE.getCode())
                    .messageTitle("生产反馈联动发货计划变更")
                    .messageContent(content)
                    .messageLink(null)
                    .messageEmergency("3")
                    .readStatus(YesOrNoEnum.NO.getCode())
                    .extraInfo(null)
                    .build();
            userMessageList.add(result);
        }
        if (CollectionUtils.isNotEmpty(userMessageList)) {
            userMessageUtils.sendMessage(userMessageList);
        }

        // 每个人要发的消息
        Map<String, String> userMessage = new HashMap<>();
        userMessage = userMessageListMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        e -> String.join("\n", e.getValue())
                ));
        doSendWebSocketMessage(userMessage);
    }

    private static Map<String, BigDecimal> getDayMap(List<DeliveryPlanPublishedCompareDayPO> insertList, String dayStr) {
        Map<String, BigDecimal> dayMap = new HashMap<>();
        Map<String, List<DeliveryPlanPublishedCompareDayPO>> map = insertList.stream()
                .filter(t -> dayStr.equals(t.getDayStr()))
                .collect(Collectors.groupingBy(DeliveryPlanPublishedCompareDayPO::getProductCode));
        for (Map.Entry<String, List<DeliveryPlanPublishedCompareDayPO>> entry : map.entrySet()) {
            // 对各个主机厂的数据进行求和
            List<DeliveryPlanPublishedCompareDayPO> value = entry.getValue();
            int totalOldQty = value.stream().mapToInt(DeliveryPlanPublishedCompareDayPO::getOldDemandQuantity).sum();
            int totalNewQty = value.stream().mapToInt(DeliveryPlanPublishedCompareDayPO::getNewDemandQuantity).sum();
            BigDecimal rateOfChange;
            if (totalOldQty == 0) {
                if (totalNewQty == 0) {
                    rateOfChange = BigDecimal.ZERO;
                }else {
                    rateOfChange = BigDecimal.ONE;
                }
            }else {
                rateOfChange = new BigDecimal(totalNewQty)
                        .divide(new BigDecimal(totalOldQty), 4, RoundingMode.HALF_UP);
            }
            dayMap.put(entry.getKey(), rateOfChange);
        }
        return dayMap;
    }

    /**
     * 发送webSocket消息
     * @param userMessage
     */
    private void doSendWebSocketMessage(Map<String, String> userMessage) {
        if (userMessage == null || userMessage.isEmpty()) {
            return;
        }
        for (Map.Entry<String, String> entry : userMessage.entrySet()) {
            deliveryPlanChangeWebSocketHandler.sendMessageToUser(entry.getKey(), entry.getValue());
        }
    }

    @Override
    public void testSendMessage(String userId, String message) {
        if (userId == null || message == null) {
            return;
        }
        doSendWebSocketMessage(ImmutableMap.of(userId, message));
    }

    public static List<String> getDurDayList(Date startDate, Date endDate) {
        List<Date> intervalDates = DateUtils.getIntervalDates(startDate, endDate);
        return intervalDates.stream().map(DateUtils::dateToString).collect(Collectors.toList());
    }

    @Override
    public void testSendMessage2(String message) {
        UserMessageDTO result = UserMessageDTO.builder()
                .id(UUIDUtil.getUUID())
                .userId("8d62ab0f-018d-72b118fe-8a87cdd8-0029")
                .roleId(null)
                .messageSource("业务消息")
                .messageType(MessageTypeEnum.DELIVERY_PLAN_CHANGE_MESSAGE.getCode())
                .messageTitle("生产反馈联动发货计划变更")
                .messageContent(message)
                .messageLink(null)
                .messageEmergency("3")
                .readStatus(YesOrNoEnum.NO.getCode())
                .extraInfo(null)
                .build();

        userMessageUtils.sendMessage(Collections.singletonList(result));
    }

    @Override
    public List<DeliveryPlanPublishedCompareDayVO2> deliveryPlanPublishedCompareDayView(List<String> queryProductCodeList,
                                                                                        String scenario,
                                                                                        String userId) {

        List<DeliveryPlanPublishedCompareDayVO2> result = new ArrayList<>();
        List<CollectionValueVO> collectionValueList = ipsFeign.getByCollectionCode("NUMBER_OF_DAYS_FOR_CHANGE_NOTICE_DISPLAY");
        if (CollectionUtils.isEmpty(collectionValueList)) {
            log.info("未维护字典表NUMBER_OF_DAYS_FOR_CHANGE_NOTICE_DISPLAY");
            return result;
        }
        List<String> plannerProduct = newMdsFeign.getPlannerProduct(scenario, userId);
        PlanningHorizonVO planningHorizonVO = newMdsFeign.selectPlanningHorizon(scenario);
        Date startDate = planningHorizonVO.getPlanStartTime();
        List<String> dayStrList = collectionValueList.stream().map(CollectionValueVO::getCollectionValue).sorted().collect(Collectors.toList());
        List<DeliveryPlanPublishedCompareDayPO> deliveryPlanPublishedCompareDayPOList =
                deliveryPlanPublishedCompareDayDao.selectViewByDayStrAndProductCodeList(dayStrList, queryProductCodeList);
        deliveryPlanPublishedCompareDayPOList = deliveryPlanPublishedCompareDayPOList.stream()
                .filter(t->plannerProduct.contains(t.getProductCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(deliveryPlanPublishedCompareDayPOList)) {
            List<String> productCodeList = deliveryPlanPublishedCompareDayPOList.stream()
                    .map(DeliveryPlanPublishedCompareDayPO::getProductCode)
                    .distinct().collect(Collectors.toList());
            List<NewProductStockPointVO> deliveryProductStockPoints = newMdsFeign.selectByProductCode(scenario, productCodeList);
            Map<String, String> product2VehicleMap = deliveryProductStockPoints.stream()
                    .filter(t -> StringUtils.isNotEmpty(t.getVehicleModelCode()))
                    .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, NewProductStockPointVO::getVehicleModelCode, (v1, v2) -> v1));

            Map<String, List<DeliveryPlanPublishedCompareDayPO>> map = deliveryPlanPublishedCompareDayPOList
                    .stream().collect(Collectors.groupingBy(DeliveryPlanPublishedCompareDayPO::getProductCode));
            for (Map.Entry<String, List<DeliveryPlanPublishedCompareDayPO>> entry : map.entrySet()) {
                String productCode = entry.getKey();
                String vehicleModelCode = product2VehicleMap.get(productCode);
                DeliveryPlanPublishedCompareDayVO2 vo2 = new DeliveryPlanPublishedCompareDayVO2();
                vo2.setVehicleModelCode(vehicleModelCode);
                vo2.setProductCode(productCode);
                vo2.setPublishTime(entry.getValue().get(0).getPublishTime());
                vo2.setDynamicHeader(dayStrList);
                Map<String, List<DeliveryPlanPublishedCompareDayPO>> dayMap = entry.getValue()
                        .stream().collect(Collectors.groupingBy(DeliveryPlanPublishedCompareDayPO::getDayStr));
                for (String dayStr : dayStrList) {
                    List<DeliveryPlanPublishedCompareDayPO> deliveryPlanPublishedCompareDayPOS = dayMap.get(dayStr);
                    if (CollectionUtils.isNotEmpty(deliveryPlanPublishedCompareDayPOS)) {
                        int variableQuantity = deliveryPlanPublishedCompareDayPOS.stream().mapToInt(DeliveryPlanPublishedCompareDayPO::getVariableQuantity).sum();
                        vo2.getVariableQtyMap().put(dayStr, variableQuantity);
                        BigDecimal rateOfChange = BigDecimal.ZERO;
                        int oldDemandQuantity = deliveryPlanPublishedCompareDayPOS.stream().mapToInt(DeliveryPlanPublishedCompareDayPO::getOldDemandQuantity).sum();
                        if (oldDemandQuantity == 0) {
                            if (variableQuantity != 0) {
                                rateOfChange = BigDecimal.ONE;
                            }
                        } else {
                            rateOfChange = new BigDecimal(variableQuantity)
                                    .divide(new BigDecimal(oldDemandQuantity), 4, RoundingMode.HALF_UP);
                        }
                        vo2.getRateOfChangeMap().put(dayStr, rateOfChange);
                    } else {
                        vo2.getVariableQtyMap().put(dayStr, 0);
                        vo2.getRateOfChangeMap().put(dayStr, BigDecimal.ZERO);
                    }
                }
                boolean allMatch = vo2.getRateOfChangeMap().entrySet().stream().allMatch(t -> t.getValue()
                        .compareTo(BigDecimal.ZERO) == 0);
                if (allMatch) {
                    continue;
                }
                result.add(vo2);
            }
        }
        List<DeliveryPlanPublishedCompareDayVO2> collect = result.stream().filter(t -> {
                    Date publishTime = t.getPublishTime();
                    Date date = DateUtils.moveDay(publishTime, 3);
                    return date.getTime() >= startDate.getTime();
                }).sorted((o1, o2) -> o2.getPublishTime().compareTo(o1.getPublishTime()))
                .collect(Collectors.toList());
        return collect;
    }
}
