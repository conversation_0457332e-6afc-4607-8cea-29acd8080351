<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.loading.infrastructure.dao.LoadingDemandSubmissionDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO">
        <!--@Table fdp_loading_demand_submission_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="submission_id" jdbcType="VARCHAR" property="submissionId"/>
        <result column="submission_type" jdbcType="VARCHAR" property="submissionType"/>
        <result column="demand_time" jdbcType="VARCHAR" property="demandTime"/>
        <result column="origin_demand_time" jdbcType="VARCHAR" property="originDemandTime"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,submission_id,submission_type,demand_time,origin_demand_time,demand_quantity,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.submissionId != null and params.submissionId != ''">
                and submission_id = #{params.submissionId,jdbcType=VARCHAR}
            </if>
            <if test="params.submissionType != null and params.submissionType != ''">
                and submission_type = #{params.submissionType,jdbcType=VARCHAR}
            </if>
            <if test="params.demandTime != null and params.demandTime != ''">
                and demand_time = #{params.demandTime,jdbcType=VARCHAR}
            </if>
            <if test="params.originDemandTime != null and params.originDemandTime != ''">
                and origin_demand_time = #{params.originDemandTime,jdbcType=VARCHAR}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.submissionIds != null and params.submissionIds.size() > 0">
                and submission_id in
                <foreach collection="params.submissionIds" item="itemSubmissionId" open="(" separator="," close=")">
                    #{itemSubmissionId}
                </foreach>
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.startDemandTime != null and params.startDemandTime != ''">
                and demand_time <![CDATA[>=]]> #{params.startDemandTime,jdbcType=VARCHAR}
            </if>
            <if test="params.endDemandTime != null and params.endDemandTime != ''">
                and demand_time <![CDATA[<=]]> #{params.endDemandTime,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_loading_demand_submission_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_loading_demand_submission_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_loading_demand_submission_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_loading_demand_submission_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectBySubmissionIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_loading_demand_submission_detail
        where submission_id in
        <foreach collection="submissionIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByOriginIdAndOemCode" resultType="java.lang.String">
        select id
        from fdp_loading_demand_submission_detail
        where submission_id in (select id
                                from fdp_loading_demand_submission
                                where version_id = #{originVersionId,jdbcType=VARCHAR}
                                )

    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_loading_demand_submission_detail(
        id,
        submission_id,
        submission_type,
        demand_time,
        origin_demand_time,
        demand_quantity,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{submissionId,jdbcType=VARCHAR},
        #{submissionType,jdbcType=VARCHAR},
        #{demandTime,jdbcType=VARCHAR},
        #{originDemandTime,jdbcType=VARCHAR},
        #{demandQuantity,jdbcType=VARCHAR},
        #{oweQuantity,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO">
        insert into fdp_loading_demand_submission_detail(id,
                                                         submission_id,
                                                         submission_type,
                                                         demand_time,
                                                         origin_demand_time,
                                                         demand_quantity,
                                                         remark,
                                                         enabled,
                                                         creator,
                                                         create_time,
                                                         modifier,
                                                         modify_time,
                                                         version_value)
        values (#{id,jdbcType=VARCHAR},
                #{submissionId,jdbcType=VARCHAR},
                #{submissionType,jdbcType=VARCHAR},
                #{demandTime,jdbcType=VARCHAR},
                #{originDemandTime,jdbcType=VARCHAR},
                #{demandQuantity,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_loading_demand_submission_detail(
        id,
        submission_id,
        submission_type,
        demand_time,
        origin_demand_time,
        demand_quantity,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.submissionId,jdbcType=VARCHAR},
            #{entity.submissionType,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=VARCHAR},
            #{entity.originDemandTime,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_loading_demand_submission_detail(
        id,
        submission_id,
        submission_type,
        demand_time,
        origin_demand_time,
        demand_quantity,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.submissionId,jdbcType=VARCHAR},
            #{entity.submissionType,jdbcType=VARCHAR},
            #{entity.demandTime,jdbcType=VARCHAR},
            #{entity.originDemandTime,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO">
        update fdp_loading_demand_submission_detail
        set submission_id      = #{submissionId,jdbcType=VARCHAR},
            submission_type    = #{submissionType,jdbcType=VARCHAR},
            demand_time        = #{demandTime,jdbcType=VARCHAR},
            origin_demand_time = #{originDemandTime,jdbcType=VARCHAR},
            demand_quantity    = #{demandQuantity,jdbcType=VARCHAR},
            remark             = #{remark,jdbcType=VARCHAR},
            enabled            = #{enabled,jdbcType=VARCHAR},
            modifier           = #{modifier,jdbcType=VARCHAR},
            modify_time        = #{modifyTime,jdbcType=TIMESTAMP},
            version_value      = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update fdp_loading_demand_submission_detail
        set version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionDetailPO">
        update fdp_loading_demand_submission_detail
        <set>
            <if test="item.submissionId != null and item.submissionId != ''">
                submission_id = #{item.submissionId,jdbcType=VARCHAR},
            </if>
            <if test="item.submissionType != null and item.submissionType != ''">
                submission_type = #{item.submissionType,jdbcType=VARCHAR},
            </if>
            <if test="item.demandTime != null and item.demandTime != ''">
                demand_time = #{item.demandTime,jdbcType=VARCHAR},
            </if>
            <if test="item.originDemandTime != null and item.originDemandTime != ''">
                origin_demand_time = #{item.originDemandTime,jdbcType=VARCHAR},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_loading_demand_submission_detail set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_loading_demand_submission_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="submission_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.submissionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="submission_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.submissionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="origin_demand_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originDemandTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_loading_demand_submission_detail set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_loading_demand_submission_detail
            <set>
                <if test="item.submissionId != null and item.submissionId != ''">
                    submission_id = #{item.submissionId,jdbcType=VARCHAR},
                </if>
                <if test="item.submissionType != null and item.submissionType != ''">
                    submission_type = #{item.submissionType,jdbcType=VARCHAR},
                </if>
                <if test="item.demandTime != null and item.demandTime != ''">
                    demand_time = #{item.demandTime,jdbcType=VARCHAR},
                </if>
                <if test="item.originDemandTime != null and item.originDemandTime != ''">
                    origin_demand_time = #{item.originDemandTime,jdbcType=VARCHAR},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_loading_demand_submission_detail set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateQuantityById">
        update fdp_loading_demand_submission_detail
        set demand_quantity = #{demandQuantity,jdbcType=DECIMAL}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_loading_demand_submission_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_loading_demand_submission_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 批量id+版本删除 -->
    <delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_loading_demand_submission_detail where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>
    <delete id="deleteByVersionIdAndSubmissionType">
        delete
        from fdp_loading_demand_submission_detail
        where submission_id = #{submissionId,jdbcType=VARCHAR}
          and submission_type = #{contentType,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteBySubmissionIds">
        delete
        from fdp_loading_demand_submission_detail
        where submission_id in
        <foreach collection="submissionIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <!-- 条件查询 -->
    <select id="selectVOByParams" resultType="com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO">
        SELECT
			a.id,
			a.submission_id submissionId,
			a.submission_type submissionType,
			a.demand_time demandTime,
			a.origin_demand_time originDemandTime,
			a.demand_quantity demandQuantity,
			b.demand_category demandCategory,
			b.product_code productCode,
			b.version_id versionId
		FROM
			fdp_loading_demand_submission_detail a
			LEFT JOIN fdp_loading_demand_submission b ON a.submission_id = b.id
		where 1=1
		<if test="params.submissionType != null and params.submissionType != ''">
            and a.submission_type = #{params.submissionType,jdbcType=VARCHAR}
        </if>
        <if test="params.demandTime != null and params.demandTime != ''">
            and a.demand_time = #{params.demandTime,jdbcType=VARCHAR}
        </if>
        <if test="params.oemCode != null and params.oemCode != ''">
            and b.oem_code = #{params.oemCode,jdbcType=VARCHAR}
        </if>
        <if test="params.versionId != null and params.versionId != ''">
            and b.version_id = #{params.versionId,jdbcType=VARCHAR}
        </if>
        <if test="params.startTimeStr != null and params.startTimeStr != '' and params.endTimeStr != null and params.endTimeStr != ''">
            and date_format(demand_time,'%Y-%m-%d %H:%i:%s') BETWEEN #{params.startTimeStr,jdbcType=VARCHAR} AND
            #{params.endTimeStr,jdbcType=VARCHAR}
        </if>
    </select>
    
    <delete id="deleteBySubmissionIdsAndSubmissionType">
        delete
        from fdp_loading_demand_submission_detail
        where 
	        submission_id in
	        <foreach collection="submissionIds" item="itemSubmissionId" open="(" separator="," close=")">
	            #{itemSubmissionId}
	        </foreach>
          	and submission_type = #{submissionType,jdbcType=VARCHAR}
    </delete>
</mapper>
