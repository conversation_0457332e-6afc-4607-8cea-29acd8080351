package com.yhl.scp.dfp.calendar.domain.service;

import com.google.common.collect.Maps;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dfp.calendar.convertor.ResourceCalendarConvertor;
import com.yhl.scp.dfp.calendar.domain.entity.CalendarRuleDO;
import com.yhl.scp.dfp.calendar.domain.entity.ResourceCalendarDO;
import com.yhl.scp.dfp.calendar.domain.entity.ResourceHourStatisticDO;
import com.yhl.scp.dfp.calendar.domain.entity.TimeRangeDO;
import com.yhl.scp.dfp.calendar.domain.factory.DfpResourceCalendarFactory;
import com.yhl.scp.dfp.calendar.enums.ShiftTypeEnum;
import com.yhl.scp.dfp.calendar.infrastructure.dao.DfpResourceCalendarDao;
import com.yhl.scp.dfp.calendar.infrastructure.po.ResourceCalendarPO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.HumanResourceVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>ResourceCalendarDomainService</code>
 * <p>
 * 资源日历领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:06:22
 */
@Service
@Slf4j
public class DfpResourceCalendarDomainService {

    @Resource
    private DfpResourceCalendarDao resourceCalendarDao;

    @Resource
    private DfpCalendarRuleDomainService calendarRuleDomainService;

    @Resource
    private DfpResourceCalendarFactory resourceCalendarFactory;

    /**
     * 数据校验
     *
     * @param resourceCalendarDO 领域对象
     */
    public void validation(ResourceCalendarDO resourceCalendarDO) {
        // 修正时间
        resourceCalendarDO.truncateTimeOfDate();
        checkNotNull(resourceCalendarDO);

    }

    public void validation(List<ResourceCalendarDO> resourceCalendarDOList) {
        // 修正时间
        resourceCalendarDOList.forEach(resourceCalendarDO -> resourceCalendarDO.truncateTimeOfDate());
        // 非空校验
        resourceCalendarDOList.forEach(resourceCalendarDO -> checkNotNull(resourceCalendarDO));
        // 资源日历时间段校验
        checkTimeIntervalConflict(resourceCalendarDOList);
    }

    /**
     * 异常日历合并
     * @param normalResourceCalendarList
     * @param abnormalResourceCalendarList
     */
    public void mergeAbnormalResourceClendar(List<ResourceCalendarDO> normalResourceCalendarList, List<ResourceCalendarDO> abnormalResourceCalendarList) {

        List<ResourceCalendarDO> createResourceCalendarList = new ArrayList<>();
        List<ResourceCalendarDO> deleteResourceCalendarList = new ArrayList<>();

        for (ResourceCalendarDO normalResourceCalendarD0 : normalResourceCalendarList) {

            List<ResourceCalendarDO> overLapresourceCalendarDOList = new ArrayList<>();
            for (ResourceCalendarDO abnormalResourceCalendarDO : abnormalResourceCalendarList) {
                if (!StringUtils.equals(normalResourceCalendarD0.getStandardResourceId(), abnormalResourceCalendarDO.getStandardResourceId()) || !StringUtils.equals(normalResourceCalendarD0.getPhysicalResourceId(), abnormalResourceCalendarDO.getPhysicalResourceId())) {
                    continue;
                }
                if (normalResourceCalendarD0.getStartTime().after(abnormalResourceCalendarDO.getEndTime())) {
                    continue;
                }
                if (normalResourceCalendarD0.getEndTime().before(abnormalResourceCalendarDO.getStartTime())) {
                    continue;
                }
                overLapresourceCalendarDOList.add(abnormalResourceCalendarDO);
            }
            if (CollectionUtils.isEmpty(overLapresourceCalendarDOList)) {
                if (StringUtils.isEmpty(normalResourceCalendarD0.getId())) {
                    createResourceCalendarList.add(normalResourceCalendarD0);
                }
            } else {
                overLapresourceCalendarDOList.sort(Comparator.comparing(ResourceCalendarDO::getStartTime));
                if (overLapresourceCalendarDOList.size() == 1) {
                    mergerOneNormalAndOneAbnormal(normalResourceCalendarD0, overLapresourceCalendarDOList.get(0),
                            createResourceCalendarList, deleteResourceCalendarList);

                }
                if (overLapresourceCalendarDOList.size() > 1) {
                    for (int i=0; i < overLapresourceCalendarDOList.size(); i++) {
                        ResourceCalendarDO abnormalResourceCalendarDO = overLapresourceCalendarDOList.get(i);
                        if (i==0) {
                            if (abnormalResourceCalendarDO.getStartTime().after(normalResourceCalendarD0.getStartTime())) {
                                mergerOneNormalAndOneAbnormal(normalResourceCalendarD0, overLapresourceCalendarDOList.get(i),
                                        createResourceCalendarList, deleteResourceCalendarList);
                            }
                        }
                        if (i == overLapresourceCalendarDOList.size() - 1) {
                            if (abnormalResourceCalendarDO.getEndTime().before(normalResourceCalendarD0.getEndTime())) {
                                mergerOneNormalAndOneAbnormal(normalResourceCalendarD0, overLapresourceCalendarDOList.get(i),
                                        createResourceCalendarList, deleteResourceCalendarList);
                            }
                        }
                        if (i>0 && i < overLapresourceCalendarDOList.size() - 1) {
                            ResourceCalendarDO abnormalResourceCalendarDO2 = overLapresourceCalendarDOList.get(i+1);
                            if (abnormalResourceCalendarDO.getEndTime().before(abnormalResourceCalendarDO2.getStartTime())) {
                                ResourceCalendarDO resourceCalendarDO1 = new ResourceCalendarDO();
                                BeanUtils.copyProperties(normalResourceCalendarD0, resourceCalendarDO1,"id");
                                resourceCalendarDO1.setStartTime(abnormalResourceCalendarDO.getEndTime());
                                resourceCalendarDO1.setEndTime(abnormalResourceCalendarDO2.getStartTime());
                                setNormalResourceCalendarWorkHoursOrOvertimeHours(resourceCalendarDO1);
                                createResourceCalendarList.add(resourceCalendarDO1);
                            }
                        }
                    }

                }
            }
        }
        Date operationTime = new Date();
        if (CollectionUtils.isNotEmpty(createResourceCalendarList)) {
            List<ResourceCalendarPO> resourceCalendarPOS = ResourceCalendarConvertor.INSTANCE.dos2Pos(createResourceCalendarList);
            BasePOUtils.insertBatchFiller(resourceCalendarPOS, operationTime);
            resourceCalendarDao.insertBatch(resourceCalendarPOS);
        }
        if (CollectionUtils.isNotEmpty(deleteResourceCalendarList)) {
            resourceCalendarDao.deleteBatch(deleteResourceCalendarList.stream().map(ResourceCalendarDO::getId).collect(Collectors.toList()));
        }

    }



    /**
     *
     * @return
     */
    public void mergerOneNormalAndOneAbnormal(ResourceCalendarDO normalResourceCalendarD0,
                                              ResourceCalendarDO abnormalResourceCalendarD0,
                                              List<ResourceCalendarDO> createResourceCalendarList,
                                              List<ResourceCalendarDO> deleteResourceCalendarList){
        // 异常日历时间段包含正常日历,删除该正常日历
        if (!abnormalResourceCalendarD0.getStartTime().after(normalResourceCalendarD0.getStartTime())
                && !abnormalResourceCalendarD0.getEndTime().before(normalResourceCalendarD0.getStartTime())) {
            if (StringUtils.isNotEmpty(normalResourceCalendarD0.getId())) {
                deleteResourceCalendarList.add(normalResourceCalendarD0);
            }
        }
        if (abnormalResourceCalendarD0.getStartTime().after(normalResourceCalendarD0.getStartTime())) {
            ResourceCalendarDO resourceCalendarDO1 = new ResourceCalendarDO();
            BeanUtils.copyProperties(normalResourceCalendarD0, resourceCalendarDO1,"id");
            resourceCalendarDO1.setEndTime(abnormalResourceCalendarD0.getStartTime());
            setNormalResourceCalendarWorkHoursOrOvertimeHours(resourceCalendarDO1);
            createResourceCalendarList.add(resourceCalendarDO1);
        }

        if (abnormalResourceCalendarD0.getEndTime().before(normalResourceCalendarD0.getEndTime())) {
            ResourceCalendarDO resourceCalendarDO1 = new ResourceCalendarDO();
            BeanUtils.copyProperties(normalResourceCalendarD0, resourceCalendarDO1,"id");
            resourceCalendarDO1.setStartTime(abnormalResourceCalendarD0.getEndTime());
            setNormalResourceCalendarWorkHoursOrOvertimeHours(resourceCalendarDO1);
            createResourceCalendarList.add(resourceCalendarDO1);
        }

        if (StringUtils.isNotEmpty(normalResourceCalendarD0.getId())) {
            deleteResourceCalendarList.add(normalResourceCalendarD0);
        }
    }

    private void setNormalResourceCalendarWorkHoursOrOvertimeHours(ResourceCalendarDO resourceCalendarDO) {
        Long minuteInterval = DateUtils.getMinuteInterval(resourceCalendarDO.getStartTime(), resourceCalendarDO.getEndTime());
        if (resourceCalendarDO.getOvertimeHours() != null && resourceCalendarDO.getOvertimeHours().doubleValue() > 0) {
            resourceCalendarDO.setOvertimeHours(BigDecimal.valueOf(minuteInterval).divide(BigDecimal.valueOf(60),2, BigDecimal.ROUND_HALF_UP));
        }
        if (resourceCalendarDO.getWorkHours() != null && resourceCalendarDO.getWorkHours().doubleValue() > 0) {
            resourceCalendarDO.setWorkHours(BigDecimal.valueOf(minuteInterval).divide(BigDecimal.valueOf(60),2, BigDecimal.ROUND_HALF_UP));
        }

    }

    private  Map<String, List<ResourceCalendarDO>> groupResourceCalendar(List<ResourceCalendarDO> resourceCalendarDOList) {
        List<ResourceCalendarDO> normalResourceCalendarList = new ArrayList<>();
        List<ResourceCalendarDO> abnormalResourceCalendarList = new ArrayList<>();
        List<String> normalShiftList = EnumUtils.getCodeList(ShiftTypeEnum.class);
        resourceCalendarDOList.forEach(resourceCalendarDO -> {
            if (normalShiftList.contains(resourceCalendarDO.getCalendarType())) {
                normalResourceCalendarList.add(resourceCalendarDO);
            } else {
                abnormalResourceCalendarList.add(resourceCalendarDO);
            }
        });

        Map<String, List<ResourceCalendarDO>> map  = Maps.newHashMap();
        map.put("normal", normalResourceCalendarList);
        map.put("abnormal", abnormalResourceCalendarList);
        return map;
    }

    /**
     * 校验时间是否冲突
     * @param resourceCalendarDOList
     */
    private void checkTimeIntervalConflict(List<ResourceCalendarDO> resourceCalendarDOList) {
        for (int i = 0; i < resourceCalendarDOList.size(); i++) {
            ResourceCalendarDO resourceCalendarDO1 = resourceCalendarDOList.get(i);
            for (int j = i + 1; j < resourceCalendarDOList.size(); j++) {
                ResourceCalendarDO resourceCalendarDO2 = resourceCalendarDOList.get(j);

                if (resourceCalendarDO1.getStartTime().after(resourceCalendarDO2.getEndTime())) {
                    continue;
                }
                if (resourceCalendarDO1.getEndTime().before(resourceCalendarDO2.getStartTime())) {
                    continue;
                }
                throw new BusinessException("时间冲突");


            }
        }
    }

    /**
     * 非空检验
     *
     * @param resourceCalendarDO 领域对象
     */
    private void checkNotNull(ResourceCalendarDO resourceCalendarDO) {
        if (StringUtils.isBlank(resourceCalendarDO.getPhysicalResourceId())) {
            throw new BusinessException("资源，不能为空");
        }
        if (StringUtils.isBlank(resourceCalendarDO.getShiftPattern())) {
            throw new BusinessException("出勤模式，不能为空");
        }
    }




    /**
     * 刷新给定资源的开始时间到结束时间段的资源日历
     *
     * @param organizationId      生产组织ID
     * @param standardResourceIds 标准资源列表  具体的标准资源id
     * @param physicalResourceIds 物理资源列表  具体的物理资源id
     * @param planStartTime       开始时间
     * @param planEndTime         结束时间
     * @param calendarRuleDOList  日历规则
     */
    public List<ResourceCalendarDO> refreshCalendar(
            String organizationId,
            List<String> standardResourceIds,
            List<String> physicalResourceIds,
            Map<String, String> physicalIdToStandardIdMap,
            Date planStartTime,
            Date planEndTime,
            List<CalendarRuleDO> calendarRuleDOList) {

        // 1、删除对应资源的所有日历
        List<ResourceCalendarPO> resourceCalendarPOS = resourceCalendarDao.selectByResourceIdsAndDate(standardResourceIds, physicalResourceIds, planStartTime, planEndTime);
        List<String> idsToDelete = resourceCalendarPOS.stream().map(ResourceCalendarPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idsToDelete)) {
            BulkOperationUtils.bulkUpdateOrCreate(idsToDelete, ids -> resourceCalendarDao.deleteBatch(ids),null);
        }
        // 2、然后开始进行刷新日历
        List<Date> planDates = DateUtils.getIntervalDates(planStartTime, planEndTime);

        // 对每个资源和计划周期的每天设置日历
        List<ResourceCalendarDO> resourceCalendarList = new ArrayList<>();
        for (String physicalResourceId : physicalResourceIds) {
            // 获取该资源在[planStartTime,planEndTime]期间可以用的所有资源日历
            List<CalendarRuleDO> hitRules = calendarRuleDomainService.getCalendarRuleDOs(
                    organizationId,
                    Collections.singletonList(physicalIdToStandardIdMap.get(physicalResourceId)),
                    Collections.singletonList(physicalResourceId),
                    planStartTime, planEndTime, calendarRuleDOList);
            for (Date planDate : planDates) {
                // 获取当日该planDate可以用的日历规则
//                List<CalendarRuleDO> currentDayRules = calendarRuleDomainService.getPlanDateRule(hitRules, planDate);
//                if (CollectionUtils.isEmpty(currentDayRules)) {
//                    continue;
//                }
                List<ResourceCalendarDO> list = resourceCalendarFactory.createByCalendarRule(planDate,
                        organizationId, physicalResourceId, physicalIdToStandardIdMap, hitRules);
                resourceCalendarList.addAll(list);
            }
        }
        List<ResourceCalendarDO> resourceCalendarDOS = this.dealWhetherCrossDays(resourceCalendarList, planDates);
        // 持久化
        if (CollectionUtils.isNotEmpty(resourceCalendarDOS)) {
            List<ResourceCalendarPO> pos = ResourceCalendarConvertor.INSTANCE.dos2Pos(resourceCalendarDOS);
            BasePOUtils.insertBatchFiller(pos);
            BulkOperationUtils.bulkUpdateOrCreate(pos, list -> resourceCalendarDao.insertBatch(list),null);
            log.info("日历刷新完成,共{}条记录", resourceCalendarDOS.size());
        }
        return resourceCalendarList;
    }


    /**
     * 计算这一批资源日历的总工时(仅正班工时)
     *
     * @param calendarDOS 资源日历集合
     * @return 总工时
     */
    public BigDecimal calTotalWorkHours(List<ResourceCalendarDO> calendarDOS) {
        return calendarDOS.stream().map(ResourceCalendarDO::getWorkHours).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * @param calendarDOS 资源日历集合
     * @return 加班工时合计
     */
    public BigDecimal calTotalOverTimes(List<ResourceCalendarDO> calendarDOS) {
        return calendarDOS.stream().map(ResourceCalendarDO::getOvertimeHours).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取资源日历集合
     *
     * @param standardResourceIds 标准资源ids集合
     * @param physicalResourceIds 物理资源ids集合
     * @param startDate           开始时间
     * @param endDate             结束时间
     * @return 资源日历集合
     */
    public List<ResourceCalendarDO> getResourceCalendar(List<String> standardResourceIds,
                                                        List<String> physicalResourceIds,
                                                        Date startDate,
                                                        Date endDate) {

        List<ResourceCalendarPO> pos = resourceCalendarDao.selectByResourceIdsAndDate(
                standardResourceIds,
                physicalResourceIds,
                DateUtils.truncateTimeOfDate(startDate),
                endDate);
        pos.sort(Comparator.comparing(ResourceCalendarPO::getWorkDay).thenComparing(ResourceCalendarPO::getShiftPattern));
        return ResourceCalendarConvertor.INSTANCE.pos2Dos(pos);
    }

    /**
     * 处理跨天的情况
     *
     * @param resourceCalendarDOS 资源日历DO列表
     * @param planDates           计划日期列表
     * @return java.util.List<com.yhl.scp.mds.calendar.domain.entity.ResourceCalendarDO>
     */
    private List<ResourceCalendarDO> dealWhetherCrossDays(List<ResourceCalendarDO> resourceCalendarDOS, List<Date> planDates) {
        List<ResourceCalendarDO> resourceCalendarDOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(resourceCalendarDOS)) {
            return resourceCalendarDOList;
        }
        // 根据资源分组
        Map<String, List<ResourceCalendarDO>> resultMap = resourceCalendarDOS.stream()
                .collect(Collectors.groupingBy(ResourceCalendarDO::getPhysicalResourceId));
        for (Map.Entry<String, List<ResourceCalendarDO>> entry : resultMap.entrySet()) {
            // 根据工作时间分组
            Map<String, List<ResourceCalendarDO>> value = entry.getValue().stream()
                    .collect(Collectors.groupingBy(p -> DateUtils.dateToString(p.getWorkDay(), DateUtils.COMMON_DATE_STR3)));
            for (int i = 0; i < planDates.size(); i++) {
                // 当前计划日
                String nowDate = DateUtils.dateToString(planDates.get(i), DateUtils.COMMON_DATE_STR3);
                List<ResourceCalendarDO> resourceCalendarNewForNow = value.get(nowDate);
                if (CollectionUtils.isEmpty(resourceCalendarNewForNow)) {
                    // 当前计划日没有资源日历，说明不工作
                    continue;
                }
                if (i != planDates.size() - 1) {
                    // 下一个计划日
                    String nextDate = DateUtils.dateToString(planDates.get(i + 1), DateUtils.COMMON_DATE_STR3);
                    // 下一个计划日的资源日历
                    List<ResourceCalendarDO> resourceCalendarNewForNext = value.get(nextDate);
                    if (CollectionUtils.isNotEmpty(resourceCalendarNewForNext)) {
                        // 如果下一个计划日的资源日历不为空，那么将当前资源日历无需判断是否跨天
                        resourceCalendarDOList.addAll(resourceCalendarNewForNow);
                        continue;
                    }
                    // 下一个计划日的资源日历不为null，校验当前计划日资源日历是否存在跨天，存在跨天将当前资源日历重置为不跨天
                    for (ResourceCalendarDO resourceCalendarNew : resourceCalendarNewForNow) {
                        String[] dayShiftTimeSplits = resourceCalendarNew.getShiftPattern().split(";");
                        for (int j = 0; j < dayShiftTimeSplits.length; j++) {
                            // 资源日历工作时间段字符串
                            String[] attendancePattern = dayShiftTimeSplits[j].split("-");
                            // 判断是否跨天，根据开始时间和结束时间转换为分钟数进行比较，
                            // 如果工作的结束时间小于开始时间，那么认为存在跨天，将工作时间设置为当天的结束时间‘23:59’
                            int startTimeNum = DateUtils.hourMinuteStr2Minute(attendancePattern[0]);
                            int endTimeNum = DateUtils.hourMinuteStr2Minute(attendancePattern[1]);
                            if (endTimeNum < startTimeNum) {
                                // 说明当前日历的结束时间存在跨天,重置为当天结束时间
                                String attendancePatternStr = attendancePattern[0] + "-23:59";
                                resourceCalendarNew.setShiftPattern(attendancePatternStr);
                            }
                        }
                    }
                }
                // 表示最后一天，不需要校验，直接添加进资源日历
                resourceCalendarDOList.addAll(resourceCalendarNewForNow);
            }
        }

        // 设置开始结束工作时间
        this.setWorkStartEndDate(resourceCalendarDOList);

        return resourceCalendarDOList;
    }

    private void setWorkStartEndDate(List<ResourceCalendarDO> resourceCalendarNewResult) {
        if (CollectionUtils.isNotEmpty(resourceCalendarNewResult)) {
            // 为入库的资源日历设置开始工作时间和结束工作时间
            for (ResourceCalendarDO resourceCalendarDO : resourceCalendarNewResult) {
                String[] timeStr = resourceCalendarDO.getShiftPattern().split("-");
                String startTimePoint = timeStr[0];
                resourceCalendarDO.setStartTime(DateUtils.dateJoinTime(resourceCalendarDO.getWorkDay(), startTimePoint, DateUtils.COMMON_TIME_STR1));
                Date workEndTime;
                String endTimePoint = timeStr[1];
                int i = DateUtils.hourMinuteStr2Minute(startTimePoint) >= DateUtils.hourMinuteStr2Minute(endTimePoint) ?
                        1 : -1;
                if (i > 0) {
                    // 结束时间是当天工作日期 + 一天 + 结束时间点
                    workEndTime = DateUtils.dateJoinTime(DateUtils.moveCalendar(resourceCalendarDO.getWorkDay(), Calendar.DAY_OF_YEAR, 1), endTimePoint, DateUtils.COMMON_TIME_STR1);
                } else {
                    // 不跨天，开始和结束时间都在同一天
                    workEndTime = DateUtils.dateJoinTime(resourceCalendarDO.getWorkDay(), endTimePoint, DateUtils.COMMON_TIME_STR1);
                }
                resourceCalendarDO.setEndTime(workEndTime);
            }
        }
    }


    /**
     * 计算工时
     *
     * @param timeRangeDOList     时段范围
     * @param standardResourceIds  标准资源
     * @param physicalResourceIds 物理资源
     * @return List<ResourceHourStatisticDO>
     */

    public List<ResourceHourStatisticDO> calWorkHour(List<TimeRangeDO> timeRangeDOList,
                                                     List<String> standardResourceIds,
                                                     List<String> physicalResourceIds) {

        Map<Date, String> dateMap = timeRangeDOList.stream().collect(
                Collectors.toMap(TimeRangeDO::getStartDate, TimeRangeDO::dateString, (a, b) -> a)
        );
        List<ResourceHourStatisticDO> list = new ArrayList<>();

        // 获取这段时间内的资源日历明细
        List<ResourceCalendarDO> totalResourceCalendarDOS = this.getResourceCalendar(
                standardResourceIds,
                physicalResourceIds,
                timeRangeDOList.get(0).getStartDate(),
                timeRangeDOList.get(timeRangeDOList.size() - 1).getEndDate());

        Map<String, List<ResourceCalendarDO>> resourceCalendarDOMap = totalResourceCalendarDOS.stream().collect(Collectors.groupingBy(it -> it.getStandardResourceId() + "&" + it.getPhysicalResourceId()));

        for (String standardResourceId : standardResourceIds) {
            for (String physicalResourceId : physicalResourceIds) {
                List<ResourceCalendarDO> resourceCalendarDOS = resourceCalendarDOMap.get(standardResourceId + "&" + physicalResourceId);
                if(CollectionUtils.isEmpty(resourceCalendarDOS)) continue;
                Map<String, BigDecimal> dataMapForWorkHour = new HashMap<>();
                Map<String, BigDecimal> dataMapForOvertimeHour = new HashMap<>();
                Map<String, BigDecimal> totalHourMap = new HashMap<>();
                Integer resourceNum = 1;
                for (TimeRangeDO rangeDO : timeRangeDOList) {
                    List<ResourceCalendarDO> filtered = resourceCalendarDOS.stream()
                            .filter(it -> rangeDO.getStartDate().compareTo(it.getWorkDay()) <= 0
                                    && rangeDO.getEndDate().compareTo(it.getWorkDay()) >= 0
                            ).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(resourceCalendarDOS) && resourceCalendarDOS.get(0).getResourceQuantity() != null) {
                        resourceNum = resourceCalendarDOS.get(0).getResourceQuantity();
                    }
                    BigDecimal workHour = this.calTotalWorkHours(filtered);
                    BigDecimal overtimeHour = this.calTotalOverTimes(filtered);
                    dataMapForWorkHour.put(dateMap.get(rangeDO.getStartDate()), workHour.multiply(BigDecimal.valueOf(resourceNum)));
                    dataMapForOvertimeHour.put(dateMap.get(rangeDO.getStartDate()), overtimeHour.multiply(BigDecimal.valueOf(resourceNum)));
                    totalHourMap.put(dateMap.get(rangeDO.getStartDate()),
                            dataMapForWorkHour.get(dateMap.get(rangeDO.getStartDate()))
                                    .add(dataMapForOvertimeHour.get(dateMap.get(rangeDO.getStartDate()))));
                }
                BigDecimal totalWorkHour = dataMapForWorkHour.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal totalOverTimeHour = dataMapForOvertimeHour.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                ResourceHourStatisticDO statisticDO = ResourceHourStatisticDO.builder()
                        .standardResourceId(standardResourceId)
                        .physicalResourceId(physicalResourceId)
                        .workHour(totalWorkHour)
                        .overtimeHour(totalOverTimeHour)
                        .workHourByTimeRange(dataMapForWorkHour)
                        .overtimeHourByTimeRange(dataMapForOvertimeHour)
                        .totalHourByTimeRange(totalHourMap)
                        .resourceNum(resourceNum)
                        .build();

                list.add(statisticDO);
            }
        }




        return list;

    }

    /**
     * 生成日历
     *
     * @param calendarRuleList
     * @param physicalStandardMap
     * @param standardOrgMap
     * @return
     */
    public List<ResourceCalendarDO> generate(List<CalendarRuleDO> calendarRuleList,
                                             Map<String, String> physicalStandardMap, Map<String, String> standardOrgMap,
                                             Map<String, List<HumanResourceVO>> positionSkillCollect,
                                             Map<String, String> humanResourceGroupOrgMap) {

        List<ResourceCalendarDO> resourceCalendarDOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(calendarRuleList)) {

            Map<Integer, List<CalendarRuleDO>> collect = calendarRuleList.stream().collect(Collectors.groupingBy(CalendarRuleDO::getPriority));
            int n =0;
            for (List<CalendarRuleDO> subCalendarRuleList : collect.values()) {
                if (n++ == 0) {
                    for (int i = 0; i < subCalendarRuleList.size(); i++) {
                        resourceCalendarDOList.addAll(generate(subCalendarRuleList.get(i), physicalStandardMap, standardOrgMap, positionSkillCollect,humanResourceGroupOrgMap));
                    }
                } else {
                    List<ResourceCalendarDO> resourceCalendarDOListTemp = new ArrayList<>();
                    for (int i = 0; i < subCalendarRuleList.size(); i++) {
                        resourceCalendarDOListTemp.addAll(generate(subCalendarRuleList.get(i), physicalStandardMap, standardOrgMap, positionSkillCollect, humanResourceGroupOrgMap));
                    }
                    if (CollectionUtils.isNotEmpty(resourceCalendarDOListTemp)) {
                        resourceCalendarDOListTemp = resourceCalendarDOListTemp.stream().filter(resourceCalendarDO -> {
                            for (ResourceCalendarDO calendarDO : resourceCalendarDOList) {
                                if (!StringUtils.equals(resourceCalendarDO.getPhysicalResourceId(),calendarDO.getPhysicalResourceId())) {
                                    continue;
                                }
                                if (!resourceCalendarDO.getEndTime().after(calendarDO.getStartTime())) {
                                    continue;
                                }
                                if (!resourceCalendarDO.getStartTime().before(calendarDO.getEndTime())) {
                                    continue;
                                }
                                return false;

                            }
                            return true;
                        }).collect(Collectors.toList());
                    }
                    if (CollectionUtils.isNotEmpty(resourceCalendarDOListTemp)) {
                        resourceCalendarDOList.addAll(resourceCalendarDOListTemp);
                    }
                }
            }
        }
        return resourceCalendarDOList;
    }

    public List<ResourceCalendarDO> generate(CalendarRuleDO calendarRuleDO, Map<String, String> physicalStandardMap, Map<String, String> standardOrgMap, Map<String, List<HumanResourceVO>> positionSkillCollect, Map<String, String> humanResourceGroupOrgMap) {
        List<ResourceCalendarDO> resourceCalendarDOList = new ArrayList<>();
        List<Pair<Date, Date>> pairDateList = calendarRuleDomainService.getCalendarRuleWorkDay(calendarRuleDO.getRepeatFrequency(),
                calendarRuleDO.getFrequencyPattern(),
                calendarRuleDO.getStartDate(),
                calendarRuleDO.getEndDate(),
                calendarRuleDO.getShiftDOS().get(0).getShiftPattern());
        for (String physicalResourceId : calendarRuleDO.getPhysicalResourceIds()) {
            List<String> standardResourceIdList = new ArrayList<>();
            standardResourceIdList.add(physicalStandardMap.get(calendarRuleDO.getOemCode()+calendarRuleDO.getStandardResourceIds().get(0)+physicalResourceId));
            if (CollectionUtils.isEmpty(standardResourceIdList)){
                continue;
            }
            for (String standardResourceId : standardResourceIdList) {
                for (Pair<Date, Date> datePair : pairDateList) {
                    ResourceCalendarDO resourceCalendarDO = new ResourceCalendarDO();
                    resourceCalendarDO.setId(UUIDUtil.getUUID());
                    resourceCalendarDO.setPhysicalResourceId(physicalResourceId);
                    resourceCalendarDO.setStandardResourceId(standardResourceId);
//                    resourceCalendarDO.setOrganizationId(standardOrgMap.get(resourceCalendarDO.getStandardResourceId()));
                    resourceCalendarDO.setStartTime(datePair.getFirst());
                    resourceCalendarDO.setEndTime(datePair.getSecond());
                    resourceCalendarDO.setResourceQuantity(calendarRuleDO.getResourceQuantity());
                    resourceCalendarDO.setShiftId(calendarRuleDO.getShiftDOS().get(0).getId());
                    resourceCalendarDO.setShiftPattern(calendarRuleDO.getShiftDOS().get(0).getShiftPattern());
                    resourceCalendarDO.setWorkDay(DateUtils.truncateTimeOfDate(datePair.getFirst()));
                    resourceCalendarDO.setCalendarType(calendarRuleDO.getShiftDOS().get(0).getShiftType());
                    resourceCalendarDO.setEfficiency(calendarRuleDO.getEfficiency());
                    resourceCalendarDO.setWorkHours(calendarRuleDO.getWorkHours());
                    resourceCalendarDO.setRuleId(calendarRuleDO.getId());
                    if("正常".equals(calendarRuleDO.getDescription())){
                        Long minuteInterval = DateUtils.getMinuteInterval(resourceCalendarDO.getStartTime(), resourceCalendarDO.getEndTime());
                        if ("OVERTIME".equals(calendarRuleDO.getCalendarType())) {
                            resourceCalendarDO.setOvertimeHours(BigDecimal.valueOf(minuteInterval).divide(BigDecimal.valueOf(60),2, BigDecimal.ROUND_HALF_UP));
                        } else {
                            resourceCalendarDO.setWorkHours(BigDecimal.valueOf(minuteInterval).divide(BigDecimal.valueOf(60),2, BigDecimal.ROUND_HALF_UP));
                        }
                    }
                    resourceCalendarDOList.add(resourceCalendarDO);
                }
            }
        }
        return resourceCalendarDOList;
    }





}