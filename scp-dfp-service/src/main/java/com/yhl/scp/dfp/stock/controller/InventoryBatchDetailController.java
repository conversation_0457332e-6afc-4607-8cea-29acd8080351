package com.yhl.scp.dfp.stock.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.stock.dto.InventoryBatchDetailDTO;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>InventoryBatchDetailController</code>
 * <p>
 * 库存批次明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 09:53:39
 */
@Slf4j
@Api(tags = "库存批次明细控制器")
@RestController
@RequestMapping("inventoryBatchDetail")
public class InventoryBatchDetailController extends BaseController {

    @Resource
    private InventoryBatchDetailService inventoryBatchDetailService;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<InventoryBatchDetailVO>> page() {
        List<InventoryBatchDetailVO> inventoryBatchDetailList = inventoryBatchDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<InventoryBatchDetailVO> pageInfo = new PageInfo<>(inventoryBatchDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody InventoryBatchDetailDTO inventoryBatchDetailDTO) {
        return inventoryBatchDetailService.doCreate(inventoryBatchDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody InventoryBatchDetailDTO inventoryBatchDetailDTO) {
        return inventoryBatchDetailService.doUpdate(inventoryBatchDetailDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        inventoryBatchDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<InventoryBatchDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryBatchDetailService.selectByPrimaryKey(id));
    }

     @ApiOperation(value = "手动同步")
     @GetMapping(value = "sync")
     public BaseResponse<Void> syncStockBatchDetail(@RequestParam("stockPoint") String stockPoint) {
        log.info("Controller 开始手动同步库存批次详情");
         String databaseName = SystemHolder.getScenario();
         String tenantId = SystemHolder.getTenantId();
         Scenario scenario=new Scenario();
         scenario.setDataBaseName(databaseName);
         scenario.setTenantId(tenantId);
         return inventoryBatchDetailService.syncStockBatchDetail(scenario,stockPoint);
     }

}
