package com.yhl.scp.ips.conf.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.conf.dto.UrlTableDTO;
import com.yhl.scp.ips.conf.service.UrlTableService;
import com.yhl.scp.ips.conf.vo.UrlTableVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "控制器")
@RestController
@RequestMapping("urlTable")
public class UrlTableController extends BaseController {

    @Resource
    private UrlTableService urlTableService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<UrlTableVO>> page() {
        List<UrlTableVO> urlTableList = urlTableService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<UrlTableVO> pageInfo = new PageInfo<>(urlTableList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody UrlTableDTO urlTableDTO) {
        return urlTableService.doCreate(urlTableDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody UrlTableDTO urlTableDTO) {
        return urlTableService.doUpdate(urlTableDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        urlTableService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<UrlTableVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, urlTableService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "获取所有数据表")
    @GetMapping(value = "listTables")
    public BaseResponse<List<String>> listTables() {
        return urlTableService.selectAllTables();
    }
}
