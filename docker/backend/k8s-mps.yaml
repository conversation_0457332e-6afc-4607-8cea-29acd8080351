---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bpim-xxx-service
  namespace: bpim-xxx-namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bpim-xxx-service
  template:
    metadata:
      name: bpim-xxx-service
      labels:
        app: bpim-xxx-service
        tier: backend
      namespace: bpim-xxx-namespace
    spec:
      terminationGracePeriodSeconds: 240
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values: [ "bpim-xxx-service" ]
                  - key: tier
                    operator: In
                    values: [ "backend" ]
              topologyKey: "kubernetes.io/hostname"
      containers:
        - name: bpim-xxx-service
          image: bpim-xxx-image
          lifecycle:
            preStop:
              exec:
                command:
                  - "/bin/sh"
                  - "-c"
                  - |
                    # 检查应用是否还能响应健康接口（本地）
                    if curl -f http://localhost:8760/actuator/health --max-time 5 >/dev/null 2>&1; then
                      echo "健康检查通过，跳过dump!"
                    else
                      echo "健康检查失败或超时，搜集系统诊断报告..."
                    
                      # 服务名称
                      SERVICE_NAME="bpim-xxx-service"
                    
                      # 获取容器名：优先使用环境变量，否则从 hostname 截取最后一段
                      CONTAINER_NAME="${CONTAINER_NAME:-${HOSTNAME##*-}}"
                      # 示例：若 hostname 为 my-pod-bpim-xxx-service-7d8f9c6b5d-abc12，则 CONTAINER_NAME = abc12
                    
                      # 当前时间戳
                      TIMESTAMP=$(date +"%Y%m%d-%H%M%S")
                    
                      # 获取 Java 进程 PID
                      PID=$(ps -eo pid,ppid,args | grep 'java .*-jar' | grep -v 'sh -c' | grep -v 'grep' | awk '{print $1}' | head -n1)
                    
                      # 检查是否找到 PID
                      if [ -z "$PID" ]; then
                        echo "未找到 Java 应用进程（未匹配到 'java -jar' 启动命令）。"
                        exit 0
                      fi
                    
                      # 生成带 服务名、容器名、PID、时间 的文件名
                      BASE_NAME="dump_${SERVICE_NAME}_${CONTAINER_NAME}_${PID}_${TIMESTAMP}"
                      JSTACK_FILE="/opt/file/${BASE_NAME}.jstack"
                      HEAPDUMP_FILE="/opt/file/${BASE_NAME}.hprof"
                    
                      # 清理该服务 3 天前的旧 dump 文件（按服务名匹配）
                      echo "清理 3 天前的旧诊断文件..."
                      find /opt/file -type f \( \
                        -name "dump_${SERVICE_NAME}_*.jstack" \
                        -o -name "dump_${SERVICE_NAME}_*.hprof" \
                        \) -mtime +2 -delete 2>/dev/null || true
                    
                      # 生成线程栈 dump（jstack -l 包含锁信息）
                      echo "生成线程栈信息: $JSTACK_FILE"
                      jstack -l "$PID" > "$JSTACK_FILE" 2>&1
                      if [ $? -eq 0 ]; then
                        echo "jstack 成功生成。"
                      else
                        echo "jstack 执行失败或无权限。" >> "$JSTACK_FILE"
                      fi
                    
                      # 生成堆内存 dump（仅 live 对象，减少体积）
                      echo "生成堆内存转储: $HEAPDUMP_FILE"
                      jmap -dump:live,format=b,file="$HEAPDUMP_FILE" "$PID" 2>&1
                      if [ $? -eq 0 ]; then
                        echo "heap dump 成功生成。"
                      else
                        echo "jmap 执行失败或内存过大。" > "${HEAPDUMP_FILE}.error"
                      fi
                    
                      # 留出时间让大文件写入磁盘
                      sleep 15
                    fi
          resources:
            requests:
              memory: "k8s-request-memory"
              cpu: "k8s-request-cpu"
            limits:
              memory: "k8s-limits-memory"
              cpu: "k8s-limits-cpu"
          imagePullPolicy: Always
          ports:
            - name: server-port
              containerPort: 8760
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: 8760
            initialDelaySeconds: 600
            periodSeconds: 30
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /actuator/health
              port: 8760
            initialDelaySeconds: 90
            periodSeconds: 10
            timeoutSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          volumeMounts:
            - mountPath: /usr/local/aps/workspace
              name: bpim-data-volume
              subPath: aps/workspace
            - mountPath: /opt/file
              name: bpim-data-volume
              subPath: aps/file
      volumes:
        - name: sw-agent
          emptyDir: { }
        - name: bpim-data-volume
          persistentVolumeClaim:
            claimName: bpim-xxx-pvc
      imagePullSecrets:
        - name: harbor-registry
---
apiVersion: v1
kind: Service
metadata:
  namespace: bpim-xxx-namespace
  name: bpim-xxx-service
  labels:
    app: bpim-xxx-service
spec:
  ports:
    - name: server
      protocol: TCP
      port: 8760
      targetPort: 8760
  selector:
    app: bpim-xxx-service