package com.yhl.scp.mrp.material.plan.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDetailDTO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDetailVO;

import java.util.Date;
import java.util.List;

/**
 * <code>GlassInventoryShiftDetailService</code>
 * <p>
 * 物料库存推移应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:08:22
 */
public interface GlassInventoryShiftDetailService extends BaseService<GlassInventoryShiftDetailDTO, GlassInventoryShiftDetailVO> {

    void doUpdateBatchSelective(List<GlassInventoryShiftDetailDTO> list);

    /**
     * 查询所有
     *
     * @return list {@link GlassInventoryShiftDetailVO}
     */
    List<GlassInventoryShiftDetailVO> selectAll();

}
