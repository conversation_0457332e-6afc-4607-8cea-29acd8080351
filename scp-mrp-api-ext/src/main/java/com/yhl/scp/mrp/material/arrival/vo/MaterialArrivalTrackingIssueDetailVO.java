package com.yhl.scp.mrp.material.arrival.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>MaterialArrivalTrackingIssueDetailVO</code>
 * <p>
 * 材料到货跟踪下发明细VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-13 11:15:58
 */
@ApiModel(value = "材料到货跟踪下发明细VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialArrivalTrackingIssueDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 306818778624112908L;

    /**
     * 到货跟踪下发版本ID
     */
    @ApiModelProperty(value = "到货跟踪下发版本ID")
    @FieldInterpretation(value = "到货跟踪下发版本ID")
    private String materialArrivalTrackingIssueVersionId;
    /**
     * 到货跟踪ID
     */
    @ApiModelProperty(value = "到货跟踪ID")
    @FieldInterpretation(value = "到货跟踪ID")
    private String materialArrivalTrackingId;
    /**
     * 到货跟踪单号
     */
    @ApiModelProperty(value = "到货跟踪单号")
    @FieldInterpretation(value = "到货跟踪单号")
    private String materialArrivalTrackingNo;
    /**
     * 下发版本
     */
    @ApiModelProperty(value = "下发版本")
    @FieldInterpretation(value = "下发版本")
    private Integer issueVersion;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @FieldInterpretation(value = "状态")
    private String status;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    /**
     * 下发人
     */
    @ApiModelProperty(value = "下发人")
    @FieldInterpretation(value = "下发人")
    private String issueCreator;
    /**
     * 下发时间
     */
    @ApiModelProperty(value = "下发时间")
    @FieldInterpretation(value = "下发时间")
    private Date issueCreateTime;
    /**
     * 版本编码
     */
    @ApiModelProperty(value = "版本编码")
    @FieldInterpretation(value = "版本编码")
    private String versionCode;
    /**
     * 版本名称
     */
    @ApiModelProperty(value = "版本名称")
    @FieldInterpretation(value = "版本名称")
    private String versionName;

    /**
     * 源数据ID
     */
    @ApiModelProperty(value = "源数据ID")
    @FieldInterpretation(value = "源数据ID")
    private String sourceId;

    /**
     * 数据来源，材料采购，要货计划，人工新增
     */
    @ApiModelProperty(value = "数据来源")
    @FieldInterpretation(value = "数据来源")
    private String dataSource;

    /**
     * 采购订单号
     */
    @ApiModelProperty(value = "采购订单号")
    @FieldInterpretation(value = "采购订单号")
    private String purchaseOrderCode;

    /**
     * 采购订单行号
     */
    @ApiModelProperty(value = "采购订单行号")
    @FieldInterpretation(value = "采购订单行号")
    private String purchaseOrderLineCode;

    /**
     * 物料ID
     */
    @ApiModelProperty(value = "物料ID")
    @FieldInterpretation(value = "物料ID")
    private String productId;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;

    /**
     * 材料代码
     */
    @ApiModelProperty(value = "材料代码")
    @FieldInterpretation(value = "材料代码")
    private String materialCode;

    /**
     * 材料名称
     */
    @ApiModelProperty(value = "材料名称")
    @FieldInterpretation(value = "材料名称")
    private String materialName;

    /**
     * 要货日期
     */
    @ApiModelProperty(value = "要货日期")
    @FieldInterpretation(value = "要货日期")
    private Date requireDate;

    /**
     * 推荐要货日期
     */
    @ApiModelProperty(value = "推荐要货日期")
    @FieldInterpretation(value = "推荐要货日期")
    private Date recommendRequireDate;

    /**
     * 要货数量
     */
    @ApiModelProperty(value = "要货数量")
    @FieldInterpretation(value = "要货数量")
    private BigDecimal requireQuantity;

    /**
     * 待发货数量
     */
    @ApiModelProperty(value = "待发货数量")
    private BigDecimal waitDeliveryQuantity;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @FieldInterpretation(value = "单位")
    private String productUnit;

    /**
     * 预计到货日期
     */
    @ApiModelProperty(value = "预计到货日期")
    @FieldInterpretation(value = "预计到货日期")
    private Date predictArrivalDate;

    /**
     * 预计到货数量（在途数量）
     */
    @ApiModelProperty(value = "预计到货数量（在途数量）")
    @FieldInterpretation(value = "预计到货数量（在途数量）")
    private BigDecimal predictArrivalQuantity;

    /**
     * 到货状态，
     */
    @ApiModelProperty(value = "到货状态，")
    @FieldInterpretation(value = "到货状态，")
    private String arrivalStatus;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态")
    @FieldInterpretation(value = "审批状态")
    private String approvalStatus;

    /**
     * 取消标识
     */
    @ApiModelProperty(value = "取消标识")
    @FieldInterpretation(value = "取消标识")
    private String cancelFlag;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @FieldInterpretation(value = "状态")
    private String closedCode;

    /**
     * 物料属性
     */
    @ApiModelProperty(value = "物料属性")
    @FieldInterpretation(value = "物料属性")
    private String supplyType;

    /**
     * 物料大类
     */
    @ApiModelProperty(value = "物料大类")
    @FieldInterpretation(value = "物料大类")
    private String productCategory;

    /**
     * 送货单号
     */
    @ApiModelProperty(value = "送货单号")
    @FieldInterpretation(value = "送货单号")
    private String deliveryNoteCode;

    /**
     * 送货单创建时间
     */
    @ApiModelProperty(value = "送货单创建时间")
    @FieldInterpretation(value = "送货单创建时间")
    private Date shippingDate;

    /**
     * 供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    @FieldInterpretation(value = "供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    @FieldInterpretation(value = "供应商名称")
    private String supplierName;

    /**
     * 要货计划单号
     */
    @ApiModelProperty(value = "要货计划单号")
    @FieldInterpretation(value = "要货计划单号")
    private String materialPlanNeedNo;

    /**
     * PR号
     */
    @ApiModelProperty(value = "PR号")
    @FieldInterpretation(value = "PR号")
    private String purchaseRequestCode;

    /**
     * PR行号
     */
    @ApiModelProperty(value = "PR行号")
    @FieldInterpretation(value = "PR行号")
    private String purchaseRequestLineCode;

    /**
     * 入库数量
     */
    @ApiModelProperty(value = "入库数量")
    @FieldInterpretation(value = "入库数量")
    private BigDecimal inventoryQuantity;

    /**
     * 父级id（拆单）
     */
    @ApiModelProperty(value = "父级id（拆单）")
    @FieldInterpretation(value = "父级id（拆单）")
    private String parentId;

    /**
     * 修改原因
     */
    @ApiModelProperty(value = "修改原因")
    @FieldInterpretation(value = "修改原因")
    private String updateReason;

    /**
     * 发布状态
     */
    @ApiModelProperty(value = "发布状态")
    @FieldInterpretation(value = "发布状态")
    private String publishStatus;

    /**
     * 开单量
     */
    @ApiModelProperty(value = "开单量")
    @FieldInterpretation(value = "开单量")
    private BigDecimal qtyBilled;

    /**
     * 手动备注
     */
    @ApiModelProperty(value = "手动备注")
    @FieldInterpretation(value = "手动备注")
    private String manualNote;

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    @FieldInterpretation(value = "组织ID")
    private String organizationId;

    /**
     * 送货明细号
     */
    @ApiModelProperty(value = "送货明细号")
    @FieldInterpretation(value = "送货明细号")
    private String ticketNum;

    /**
     * 退货数量
     */
    @ApiModelProperty(value = "退货数量")
    @FieldInterpretation(value = "退货数量")
    private BigDecimal returnQty;

    @Override
    public void clean() {

    }

}
