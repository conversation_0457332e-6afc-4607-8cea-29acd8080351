package com.yhl.scp.mrp.material.arrival.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.mrp.extension.material.vo.ConsistencyWarningVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.dto.SyncMaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialNeedDateRecommendVO;

/**
 * <code>MaterialArrivalTrackingService</code>
 * <p>
 * 材料到货跟踪应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 16:57:08
 */
public interface MaterialArrivalTrackingService extends BaseService<MaterialArrivalTrackingDTO, MaterialArrivalTrackingVO> {

	void doUpdateBatchNew(List<MaterialArrivalTrackingDTO> list);

	void doUpdateBatchSelective(List<MaterialArrivalTrackingDTO> list);

	List<MaterialArrivalTrackingVO> selectVOByParams(Map<String, Object> params);

	/**
     * 查询所有
     *
     * @return list {@link MaterialArrivalTrackingVO}
     */
    List<MaterialArrivalTrackingVO> selectAll();

//	List<LabelValue<String>> queryStockPointDown();
	
	/**
	 * 要货计划/材料采购 发布下发同步数据到采购跟踪
	 * @param syncList
	 */
	void doSyncMaterialArrivalTracking(List<SyncMaterialArrivalTrackingDTO> syncList);

	void doUpdateSyncMaterialArrivalTracking(List<SyncMaterialArrivalTrackingDTO> syncList);

	/**
	 * 通过数据来源ID跟新材料到货跟踪数据采购单号
	 * @param batchTrackingList
	 */
	void doUpdateBatchBySourceId(List<MaterialArrivalTrackingDTO> batchTrackingList);

    BaseResponse<Void> splitTheOrder(List<MaterialArrivalTrackingDTO> materialArrivalTrackingVOList);

	BaseResponse<Void> closeOrder(List<String> idList);

	BaseResponse<Void> releaseOrder(List<String> idList);

	void doSyncArrivalStatus();

	PageInfo<ConsistencyWarningVO> consistencyWarning(String startDate, String endDate, String source, Integer pageNum, Integer pageSize);

	BaseResponse<Void> handlePrCancel(ErpResponse response);

	BaseResponse<Void> prCancel(String tenantId, Map<String, Object> params);

	BaseResponse<Void> handlePoClosed(ErpResponse response);

	BaseResponse<Void> poClosed(String tenantId, Map<String, Object> params);

    void doUpdateNeedDate(List<MaterialArrivalTrackingDTO> list);

	void doIssuePlanNeed(List<String> idList);

    BaseResponse<Void> issue(List<String> idList);
}
