package com.yhl.scp.mrp.material.plan.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.inventory.dto.GlassInventoryShiftQueryParamDTO;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDataDTO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftPageVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftPageVO2;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>GlassInventoryShiftDataService</code>
 * <p>
 * 物料库存推移应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:29:08
 */
public interface GlassInventoryShiftDataService extends BaseService<GlassInventoryShiftDataDTO, GlassInventoryShiftDataVO> {

    /**
     * 查询所有
     *
     * @return list {@link GlassInventoryShiftDataVO}
     */
    List<GlassInventoryShiftDataVO> selectAll();

    PageInfo<GlassInventoryShiftPageVO> pageCustom(GlassInventoryShiftQueryParamDTO dto);

    PageInfo<GlassInventoryShiftPageVO2> pageCustomNew(GlassInventoryShiftQueryParamDTO dto);

    List<GlassInventoryShiftDataVO> selectForDemandCalculation(Map<String, Object> params);

    Date getLastCreateTime(String userId);
}
