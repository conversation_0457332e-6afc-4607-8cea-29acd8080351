package com.yhl.scp.mrp.material.arrival.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingIssueDetailDTO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialArrivalTrackingIssueDetailVO;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialArrivalTrackingIssueDetailService</code>
 * <p>
 * 材料到货跟踪下发明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-13 11:15:59
 */
public interface MaterialArrivalTrackingIssueDetailService extends BaseService<MaterialArrivalTrackingIssueDetailDTO, MaterialArrivalTrackingIssueDetailVO> {

    List<MaterialArrivalTrackingIssueDetailVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link MaterialArrivalTrackingIssueDetailVO}
     */
    List<MaterialArrivalTrackingIssueDetailVO> selectAll();

}
