package com.yhl.scp.mrp.material.plan.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.inventory.dto.MaterialPlanInventoryShiftParamDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDataDTO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftPageVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <code>NoGlassInventoryShiftDataService</code>
 * <p>
 * 非原片库存推移主表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:25:33
 */
public interface NoGlassInventoryShiftDataService extends BaseService<NoGlassInventoryShiftDataDTO, NoGlassInventoryShiftDataVO> {

    List<NoGlassInventoryShiftDataVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link NoGlassInventoryShiftDataVO}
     */
    List<NoGlassInventoryShiftDataVO> selectAll();

    List<MaterialPlanInventoryShiftPageVO> selectPage2(MaterialPlanInventoryShiftParamDTO materialPlanInventoryShiftParamDTO);

    Set<String> doPublish(List<String> productCodeList);

    List<NoGlassInventoryShiftDataVO> selectBySpecialParams(Map<String, Object> params);

    void doSaveNoGlassDataPublished(String uuid, String materialPlanPublishedVersionId, Date date, String userId, String productCodeList);

    Date getLastCreateTime(String userId);

    List<String> selectMrpProductCode(MaterialPlanInventoryShiftParamDTO materialPlanInventoryShiftParamDTO);

}
