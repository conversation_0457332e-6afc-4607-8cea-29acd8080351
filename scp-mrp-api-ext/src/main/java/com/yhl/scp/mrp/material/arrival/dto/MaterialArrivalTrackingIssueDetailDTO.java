package com.yhl.scp.mrp.material.arrival.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialArrivalTrackingIssueDetailDTO</code>
 * <p>
 * 材料到货跟踪下发明细DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-13 11:15:58
 */
@ApiModel(value = "材料到货跟踪下发明细DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialArrivalTrackingIssueDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 421705331907193271L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 到货跟踪下发版本ID
     */
    @ApiModelProperty(value = "到货跟踪下发版本ID")
    private String materialArrivalTrackingIssueVersionId;
    /**
     * 到货跟踪ID
     */
    @ApiModelProperty(value = "到货跟踪ID")
    private String materialArrivalTrackingId;
    /**
     * 到货跟踪单号
     */
    @ApiModelProperty(value = "到货跟踪单号")
    private String materialArrivalTrackingNo;
    /**
     * 下发版本
     */
    @ApiModelProperty(value = "下发版本")
    private Integer issueVersion;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;

}
