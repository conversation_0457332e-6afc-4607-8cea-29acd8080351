package com.yhl.scp.mrp.material.plan.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mrp.inventory.dto.GlassInventoryShiftQueryParamDTO;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDataPublishedDTO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataPublishedVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftPublishedPageVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftPublishedPageVO2;

import java.util.List;

/**
 * <code>GlassInventoryShiftDataPublishedService</code>
 * <p>
 * 物料库存推移应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 23:08:33
 */
public interface GlassInventoryShiftDataPublishedService extends BaseService<GlassInventoryShiftDataPublishedDTO, GlassInventoryShiftDataPublishedVO> {

    /**
     * 查询所有
     *
     * @return list {@link GlassInventoryShiftDataPublishedVO}
     */
    List<GlassInventoryShiftDataPublishedVO> selectAll();

    PageInfo<GlassInventoryShiftPublishedPageVO> pageCustom(GlassInventoryShiftQueryParamDTO dto);

    PageInfo<GlassInventoryShiftPublishedPageVO2> pageCustomNew(GlassInventoryShiftQueryParamDTO dto);
}
