package com.yhl.scp.mps.config;

import com.yhl.scp.biz.common.webSocket.CommonWebSocketNewHandler;
import com.yhl.scp.common.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

/**
 * <code>WorkReportWebSocketHandler</code>
 * <p>
 * WorkReportWebSocketHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20 11:06:33
 */
@Component
@Slf4j
public class WorkReportWebSocketHandler extends CommonWebSocketNewHandler {

    /**
     * 从session中数据范围（实际应用中可能从HTTP头或URL参数获取）
     *
     * @param session 会话
     * @return java.lang.String
     */
    protected String getDataRange(WebSocketSession session) {
        String scenario = (String) session.getAttributes().get("scenario");
        String physicalResourceId = (String) session.getAttributes().get("physicalResourceId");
        return String.join(Constants.DELIMITER, scenario, physicalResourceId);
    }

}