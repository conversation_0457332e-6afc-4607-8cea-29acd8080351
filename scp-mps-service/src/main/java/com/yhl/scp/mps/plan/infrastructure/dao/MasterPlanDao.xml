<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        <!--@Table mps_master_plan-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_no" jdbcType="VARCHAR" property="versionNo"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="plan_id" jdbcType="VARCHAR" property="planId"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="resource_code" jdbcType="VARCHAR" property="resourceCode"/>
        <result column="beat" jdbcType="NUMERIC" property="beat"/>
        <result column="plan_start_date" jdbcType="TIMESTAMP" property="planStartDate"/>
        <result column="plan_end_date" jdbcType="TIMESTAMP" property="planEndDate"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="plan_quantity" jdbcType="INTEGER" property="planQuantity"/>
        <result column="spend_time" jdbcType="NUMERIC" property="spendTime"/>
        <result column="kit_status" jdbcType="VARCHAR" property="kitStatus"/>
        <result column="kit_quantity" jdbcType="INTEGER" property="kitQuantity"/>
        <result column="task_status" jdbcType="VARCHAR" property="taskStatus"/>
        <result column="order_prod_status" jdbcType="VARCHAR" property="orderProdStatus"/>
        <result column="demand_quantity" jdbcType="INTEGER" property="demandQuantity"/>
        <result column="demand_date" jdbcType="TIMESTAMP" property="demandDate"/>
        <result column="push_mes" jdbcType="VARCHAR" property="pushMes"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="deferred_status" jdbcType="INTEGER" property="deferredStatus"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.plan.vo.MasterPlanVO">
        <collection property="demandList" column="{planId=plan_id}" javaType="java.util.ArrayList"
                    ofType="com.yhl.scp.mps.plan.vo.MasterPlanVO"
                    select="selectDemandByPlanId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        version_no,
        order_no,
        plan_id,
        operation_code,
        resource_code,
        beat,
        plan_start_date,
        plan_end_date,
        product_code,
        plan_quantity,
        spend_time,
        kit_status,
        kit_quantity,
        task_status,
        order_prod_status,
        demand_quantity,
        demand_date,
        push_mes,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        deferred_status
    </sql>
    <sql id="VO_Column_List">
        `plan_id`,
        `version_no`,
        `order_no`,
        `operation_code`,
        `resource_code`,
        `beat`,
        `plan_start_date`,
        `plan_end_date`,
        `product_code`,
        `plan_quantity`,
        `spend_time`,
        `kit_status`,
        `kit_quantity`,
        `task_status`,
        `order_prod_status`,
        `push_mes`,
        `remark`,
        `enabled`,
        `creator`,
        `create_time`,
        `modifier`,
        `modify_time`,
        `version_value`,
        `deferred_status`
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.versionNo != null and params.versionNo != ''">
                and version_no = #{params.versionNo,jdbcType=VARCHAR}
            </if>
            <if test="params.orderNo != null and params.orderNo != ''">
                and order_no = #{params.orderNo,jdbcType=VARCHAR}
            </if>
            <if test="params.planId != null and params.planId != ''">
                and plan_id = #{params.planId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceCode != null and params.resourceCode != ''">
                and resource_code = #{params.resourceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.beat != null">
                and beat = #{params.beat,jdbcType=NUMERIC}
            </if>
            <if test="params.planStartDate != null">
                and plan_start_date = #{params.planStartDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planEndDate != null">
                and plan_end_date = #{params.planEndDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.planQuantity != null">
                and plan_quantity = #{params.planQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.spendTime != null">
                and spend_time = #{params.spendTime,jdbcType=NUMERIC}
            </if>
            <if test="params.kitStatus != null and params.kitStatus != ''">
                and kit_status = #{params.kitStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.kitQuantity != null">
                and kit_quantity = #{params.kitQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.taskStatus != null and params.taskStatus != ''">
                and task_status = #{params.taskStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.orderProdStatus != null and params.orderProdStatus != ''">
                and order_prod_status = #{params.orderProdStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.demandDate != null">
                and demand_date = #{params.demandDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.pushMes != null and params.pushMes != ''">
                and push_mes = #{params.pushMes,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.deferredStatus != null and params.deferredStatus != ''">
                and deferred_status = #{params.deferredStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.resourceCodes != null and params.resourceCodes.size() > 0">
                and resource_code in
                <foreach collection="params.resourceCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_master_plan
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <select id="selectDemandByPlanId" resultMap="BaseResultMap">
        select demand_date,
               demand_quantity
        from mps_master_plan
        where plan_id = #{planId,jdbcType=VARCHAR}
        order by demand_date
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_master_plan
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mps_master_plan
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_master_plan
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_mps_master_plan
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectProductQuantity" resultType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        SELECT p.product_code         as productCode,
               p.demand_date          AS demandDate,
               sum(p.demand_quantity) AS demandQuantity
        FROM
        (SELECT plan_id,
                demand_date,
                product_code,
                max(demand_quantity) AS demand_quantity
         FROM mps_master_plan
        where product_code in
        <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        GROUP BY plan_id,
                 product_code, demand_date) p
        group by p.product_code, p.demand_date
    </select>
    <select id="selectByPlanId" resultType="com.yhl.scp.mps.plan.domain.entity.MasterPlanDO">
        select
        <include refid="Base_Column_List"/>
        from mps_master_plan
        where plan_id = #{planId,jdbcType=VARCHAR}
    </select>
    <select id="selectByPlanStartTime" resultType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        select
        <include refid="Base_Column_List"/>
        from mps_master_plan
        where plan_start_date > #{planStartDate,jdbcType=TIMESTAMP}
    </select>
    <select id="selectByProductCode" resultType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        select
        <include refid="Base_Column_List"/>
        from mps_master_plan
        where product_code = #{productCode,jdbcType=VARCHAR}
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid())
            from dual
        </selectKey>
        insert into mps_master_plan(id,
                                    version_no,
                                    order_no,
                                    plan_id,
                                    operation_code,
                                    resource_code,
                                    beat,
                                    plan_start_date,
                                    plan_end_date,
                                    product_code,
                                    plan_quantity,
                                    spend_time,
                                    kit_status,
                                    kit_quantity,
                                    task_status,
                                    order_prod_status,
                                    demand_quantity,
                                    demand_date,
                                    push_mes,
                                    remark,
                                    enabled,
                                    creator,
                                    create_time,
                                    modifier,
                                    modify_time,
                                    version_value,
                                    deferred_status)
        values (#{id,jdbcType=VARCHAR},
                #{versionNo,jdbcType=VARCHAR},
                #{orderNo,jdbcType=VARCHAR},
                #{planId,jdbcType=VARCHAR},
                #{operationCode,jdbcType=VARCHAR},
                #{resourceCode,jdbcType=VARCHAR},
                #{beat,jdbcType=NUMERIC},
                #{planStartDate,jdbcType=TIMESTAMP},
                #{planEndDate,jdbcType=TIMESTAMP},
                #{productCode,jdbcType=VARCHAR},
                #{planQuantity,jdbcType=INTEGER},
                #{spendTime,jdbcType=NUMERIC},
                #{kitStatus,jdbcType=VARCHAR},
                #{kitQuantity,jdbcType=INTEGER},
                #{taskStatus,jdbcType=VARCHAR},
                #{orderProdStatus,jdbcType=VARCHAR},
                #{demandQuantity,jdbcType=INTEGER},
                #{demandDate,jdbcType=TIMESTAMP},
                #{pushMes,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{deferredStatus,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        insert into mps_master_plan(id,
                                    version_no,
                                    order_no,
                                    plan_id,
                                    operation_code,
                                    resource_code,
                                    beat,
                                    plan_start_date,
                                    plan_end_date,
                                    product_code,
                                    plan_quantity,
                                    spend_time,
                                    kit_status,
                                    kit_quantity,
                                    task_status,
                                    order_prod_status,
                                    demand_quantity,
                                    demand_date,
                                    push_mes,
                                    remark,
                                    enabled,
                                    creator,
                                    create_time,
                                    modifier,
                                    modify_time,
                                    version_value,
                                    defferred_status)
        values (#{id,jdbcType=VARCHAR},
                #{versionNo,jdbcType=VARCHAR},
                #{orderNo,jdbcType=VARCHAR},
                #{planId,jdbcType=VARCHAR},
                #{operationCode,jdbcType=VARCHAR},
                #{resourceCode,jdbcType=VARCHAR},
                #{beat,jdbcType=NUMERIC},
                #{planStartDate,jdbcType=TIMESTAMP},
                #{planEndDate,jdbcType=TIMESTAMP},
                #{productCode,jdbcType=VARCHAR},
                #{planQuantity,jdbcType=INTEGER},
                #{spendTime,jdbcType=NUMERIC},
                #{kitStatus,jdbcType=VARCHAR},
                #{kitQuantity,jdbcType=INTEGER},
                #{taskStatus,jdbcType=VARCHAR},
                #{orderProdStatus,jdbcType=VARCHAR},
                #{demandQuantity,jdbcType=INTEGER},
                #{demandDate,jdbcType=TIMESTAMP},
                #{pushMes,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{deferredStatus,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_master_plan(id,
                                    version_no,
                                    order_no,
                                    plan_id,
                                    operation_code,
                                    resource_code,
                                    beat,
                                    plan_start_date,
                                    plan_end_date,
                                    product_code,
                                    plan_quantity,
                                    spend_time,
                                    kit_status,
                                    kit_quantity,
                                    task_status,
                                    order_prod_status,
                                    demand_quantity,
                                    demand_date,
                                    push_mes,
                                    remark,
                                    enabled,
                                    creator,
                                    create_time,
                                    modifier,
                                    modify_time,
                                    version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
             #{entity.versionNo,jdbcType=VARCHAR},
             #{entity.orderNo,jdbcType=VARCHAR},
             #{entity.planId,jdbcType=VARCHAR},
             #{entity.operationCode,jdbcType=VARCHAR},
             #{entity.resourceCode,jdbcType=VARCHAR},
             #{entity.beat,jdbcType=NUMERIC},
             #{entity.planStartDate,jdbcType=TIMESTAMP},
             #{entity.planEndDate,jdbcType=TIMESTAMP},
             #{entity.productCode,jdbcType=VARCHAR},
             #{entity.planQuantity,jdbcType=INTEGER},
             #{entity.spendTime,jdbcType=NUMERIC},
             #{entity.kitStatus,jdbcType=VARCHAR},
             #{entity.kitQuantity,jdbcType=INTEGER},
             #{entity.taskStatus,jdbcType=VARCHAR},
             #{entity.orderProdStatus,jdbcType=VARCHAR},
             #{entity.demandQuantity,jdbcType=INTEGER},
             #{entity.demandDate,jdbcType=TIMESTAMP},
             #{entity.pushMes,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP},
             #{entity.versionValue,jdbcType=INTEGER},
             #{entity.deferredStatus,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_master_plan(id,
                                    version_no,
                                    order_no,
                                    plan_id,
                                    operation_code,
                                    resource_code,
                                    beat,
                                    plan_start_date,
                                    plan_end_date,
                                    product_code,
                                    plan_quantity,
                                    spend_time,
                                    kit_status,
                                    kit_quantity,
                                    task_status,
                                    order_prod_status,
                                    demand_quantity,
                                    demand_date,
                                    push_mes,
                                    remark,
                                    enabled,
                                    creator,
                                    create_time,
                                    modifier,
                                    modify_time,
                                    version_value,
                                    defferred_status)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
             #{entity.versionNo,jdbcType=VARCHAR},
             #{entity.orderNo,jdbcType=VARCHAR},
             #{entity.planId,jdbcType=VARCHAR},
             #{entity.operationCode,jdbcType=VARCHAR},
             #{entity.resourceCode,jdbcType=VARCHAR},
             #{entity.beat,jdbcType=NUMERIC},
             #{entity.planStartDate,jdbcType=TIMESTAMP},
             #{entity.planEndDate,jdbcType=TIMESTAMP},
             #{entity.productCode,jdbcType=VARCHAR},
             #{entity.planQuantity,jdbcType=INTEGER},
             #{entity.spendTime,jdbcType=NUMERIC},
             #{entity.kitStatus,jdbcType=VARCHAR},
             #{entity.kitQuantity,jdbcType=INTEGER},
             #{entity.taskStatus,jdbcType=VARCHAR},
             #{entity.orderProdStatus,jdbcType=VARCHAR},
             #{entity.demandQuantity,jdbcType=INTEGER},
             #{entity.demandDate,jdbcType=TIMESTAMP},
             #{entity.pushMes,jdbcType=VARCHAR},
             #{entity.remark,jdbcType=VARCHAR},
             #{entity.enabled,jdbcType=VARCHAR},
             #{entity.creator,jdbcType=VARCHAR},
             #{entity.createTime,jdbcType=TIMESTAMP},
             #{entity.modifier,jdbcType=VARCHAR},
             #{entity.modifyTime,jdbcType=TIMESTAMP},
             #{entity.versionValue,jdbcType=INTEGER},
             #{entity.deferredStatus,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        update mps_master_plan
        set version_no        = #{versionNo,jdbcType=VARCHAR},
            order_no          = #{orderNo,jdbcType=VARCHAR},
            plan_id           = #{planId,jdbcType=VARCHAR},
            operation_code    = #{operationCode,jdbcType=VARCHAR},
            resource_code     = #{resourceCode,jdbcType=VARCHAR},
            beat              = #{beat,jdbcType=NUMERIC},
            plan_start_date   = #{planStartDate,jdbcType=TIMESTAMP},
            plan_end_date     = #{planEndDate,jdbcType=TIMESTAMP},
            product_code      = #{productCode,jdbcType=VARCHAR},
            plan_quantity     = #{planQuantity,jdbcType=INTEGER},
            spend_time        = #{spendTime,jdbcType=NUMERIC},
            kit_status        = #{kitStatus,jdbcType=VARCHAR},
            kit_quantity      = #{kitQuantity,jdbcType=INTEGER},
            task_status       = #{taskStatus,jdbcType=VARCHAR},
            order_prod_status = #{orderProdStatus,jdbcType=VARCHAR},
            demand_quantity   = #{demandQuantity,jdbcType=INTEGER},
            demand_date       = #{demandDate,jdbcType=TIMESTAMP},
            push_mes          = #{pushMes,jdbcType=VARCHAR},
            remark            = #{remark,jdbcType=VARCHAR},
            enabled           = #{enabled,jdbcType=VARCHAR},
            modifier          = #{modifier,jdbcType=VARCHAR},
            modify_time       = #{modifyTime,jdbcType=TIMESTAMP},
            version_value     = #{versionValue,jdbcType=INTEGER},
            deferred_status   = #{deferredStatus,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER};
        update mps_master_plan
        set version_value = version_value + 1
        where id = #{id,jdbcType=VARCHAR}
          and version_value = #{versionValue,jdbcType=INTEGER}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO">
        update mps_master_plan
        <set>
            <if test="item.versionNo != null and item.versionNo != ''">
                version_no = #{item.versionNo,jdbcType=VARCHAR},
            </if>
            <if test="item.orderNo != null and item.orderNo != ''">
                order_no = #{item.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="item.planId != null and item.planId != ''">
                plan_id = #{item.planId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCode != null and item.resourceCode != ''">
                resource_code = #{item.resourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.beat != null">
                beat = #{item.beat,jdbcType=NUMERIC},
            </if>
            <if test="item.planStartDate != null">
                plan_start_date = #{item.planStartDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planEndDate != null">
                plan_end_date = #{item.planEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.planQuantity != null">
                plan_quantity = #{item.planQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.spendTime != null">
                spend_time = #{item.spendTime,jdbcType=NUMERIC},
            </if>
            <if test="item.kitStatus != null and item.kitStatus != ''">
                kit_status = #{item.kitStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.kitQuantity != null">
                kit_quantity = #{item.kitQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.taskStatus != null and item.taskStatus != ''">
                task_status = #{item.taskStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.orderProdStatus != null and item.orderProdStatus != ''">
                order_prod_status = #{item.orderProdStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.demandDate != null">
                demand_date = #{item.demandDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.pushMes != null and item.pushMes != ''">
                push_mes = #{item.pushMes,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.deferredStatus != null and item.deferredStatus != ''">
                deferred_status = #{item.deferredStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update mps_master_plan
        set version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_master_plan
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="beat = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beat,jdbcType=NUMERIC}
                </foreach>
            </trim>
            <trim prefix="plan_start_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planStartDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plan_end_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planEndDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="spend_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.spendTime,jdbcType=NUMERIC}
                </foreach>
            </trim>
            <trim prefix="kit_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kitStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kit_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kitQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="task_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.taskStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_prod_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderProdStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="demand_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="push_mes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.pushMes,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="deferred_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deferredStatuse,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
    update mps_master_plan
    set version_value = version_value + 1
    where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
        and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mps_master_plan
            <set>
                <if test="item.versionNo != null and item.versionNo != ''">
                    version_no = #{item.versionNo,jdbcType=VARCHAR},
                </if>
                <if test="item.orderNo != null and item.orderNo != ''">
                    order_no = #{item.orderNo,jdbcType=VARCHAR},
                </if>
                <if test="item.planId != null and item.planId != ''">
                    plan_id = #{item.planId,jdbcType=VARCHAR},
                </if>
                <if test="item.operationCode != null and item.operationCode != ''">
                    operation_code = #{item.operationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.resourceCode != null and item.resourceCode != ''">
                    resource_code = #{item.resourceCode,jdbcType=VARCHAR},
                </if>
                <if test="item.beat != null">
                    beat = #{item.beat,jdbcType=NUMERIC},
                </if>
                <if test="item.planStartDate != null">
                    plan_start_date = #{item.planStartDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.planEndDate != null">
                    plan_end_date = #{item.planEndDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.planQuantity != null">
                    plan_quantity = #{item.planQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.spendTime != null">
                    spend_time = #{item.spendTime,jdbcType=NUMERIC},
                </if>
                <if test="item.kitStatus != null and item.kitStatus != ''">
                    kit_status = #{item.kitStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.kitQuantity != null">
                    kit_quantity = #{item.kitQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.taskStatus != null and item.taskStatus != ''">
                    task_status = #{item.taskStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.orderProdStatus != null and item.orderProdStatus != ''">
                    order_prod_status = #{item.orderProdStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.demandDate != null">
                    demand_date = #{item.demandDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.pushMes != null and item.pushMes != ''">
                    push_mes = #{item.pushMes,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.deferredStatus != null and item.deferredStatus != ''">
                    deferred_status = #{item.deferredStatus,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
              and version_value = #{item.versionValue,jdbcType=INTEGER};
            update mps_master_plan
            set version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
              and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateByPlanId">
        update mps_master_plan
        <set>
            <if test="item.planStartDate != null">
                plan_start_date = #{planStartDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planEndDate != null">
                plan_end_date = #{item.planEndDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planQuantity != null">
                plan_quantity = #{item.planQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.resourceCode != null and item.resourceCode != ''">
                resource_code = #{item.resourceCode,jdbcType=VARCHAR},
            </if>
        </set>
        where plan_id = #{item.planId,jdbcType=VARCHAR}
    </update>
    <update id="updateSpendTime">
        update mps_master_plan
        set spend_time=#{spendTime,jdbcType=NUMERIC}
        where plan_id = #{planId,jdbcType=VARCHAR}
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mps_master_plan
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete
        from mps_master_plan where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <select id="selectOperationDropdown" resultType="com.yhl.scp.mds.extension.routing.infrastructure.po.StandardStepPO"
            parameterType="java.util.Map">
        select distinct ss.id                 as id,
                        ss.stock_point_code   as stockPointCode,
                        ss.standard_step_name as standardStepName,
                        ss.standard_step_code as standardStepCode
        from mds_res_standard_resource sr
                 left join mds_res_physical_resource pr on sr.id = pr.standard_resource_id
                 left join mds_org_production_organization po on sr.organization_id = po.id
                 left join mds_rou_standard_step ss on po.organization_code = ss.stock_point_code
            and ss.standard_step_code = pr.sequence_code
        <where>
            1 = 1
              and standard_step_type != 'NORMEAL_PROCESS'
              and stock_point_code is not null
              and standard_step_name is not null
              and standard_step_code is not null
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceCode != null and params.physicalResourceCode != ''">
                and physical_resource_code = #{params.physicalResourceCode,jdbcType=VARCHAR}
            </if>
        </where>
        order by stock_point_code, standard_step_code, standard_step_name
    </select>

    <delete id="deleteOldDemand">
        delete
        from sds_peg_demand
        where customer_id is not null
        and demand_order_id in
        <foreach collection="orderIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteDemandByIds">
        delete
        from sds_peg_demand
        where id in
        <foreach collection="demandIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="getUnPlanDeliveryPlan" resultType="String">

        WITH a AS (SELECT DISTINCT mpsp.product_code
                   FROM `sds_ord_work_order` t
                            JOIN mds_product_stock_point mpsp ON t.product_id = mpsp.id
                   WHERE t.end_time &gt;= #{startTime,jdbcType=VARCHAR}),
             b AS (SELECT DISTINCT fdpp.product_code
                   FROM fdp_delivery_plan_published fdpp
                   WHERE fdpp.demand_time &gt;= #{startTime,jdbcType=VARCHAR}
                     AND fdpp.demand_quantity > 0)
        SELECT b.product_code
        FROM b
        WHERE NOT EXISTS (SELECT 1 FROM a WHERE b.product_code = a.product_code)
    </select>

    <select id="getCompareDayInfoByProductCode" resultType="com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedCompareDayVO">
        SELECT t.oem_code as oemCode,
               t.product_code as productCode,
               t.demand_category as demandCategory,
               t.publisher as publisher,
               t.publish_time as publishTime,
               t.day_str as dayStr,
               t.new_demand_quantity as newDemandQuantity,
               t.old_demand_quantity as oldDemandQuantity,
               t.variable_quantity as variableQuantity,
               t.rate_of_change as rateOfChange
        FROM fdp_delivery_plan_published_compare_day t
        INNER JOIN (
        SELECT product_code, MAX(publish_time) AS publish_time
        FROM fdp_delivery_plan_published_compare_day
        WHERE product_code IN
        <foreach item="item" index="index" collection="productCodeList" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        GROUP BY product_code
        ) latest ON t.product_code = latest.product_code AND t.publish_time = latest.publish_time
        and t.day_str in
        <foreach item="item" index="index" collection="dayStrList" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getKeyStandardStepByProductCode" resultType="com.yhl.scp.mps.plan.vo.MasterPlanAlertingVO">
        WITH a AS (
            SELECT DISTINCT
                all_values.merged_field,
                all_values.product_code
            FROM
                (
                    SELECT
                        mrr.product_id AS merged_field,
                        mrr.product_code
                    FROM
                        mds_rou_routing mrr
                    WHERE
                        mrr.product_code IN
                        <foreach item="item" index="index" collection="productCodeList" open="(" separator="," close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                      AND mrr.enabled = 'YES' UNION ALL
                    SELECT
                        mmp.id AS merged_field,
                        mrr.product_code
                    FROM
                        mds_rou_routing mrr
                            LEFT JOIN mds_rou_routing_step_input mrrs ON mrr.id = mrrs.routing_id
                            LEFT JOIN mds_product_stock_point mmp ON mrrs.input_product_id = mmp.id
                    WHERE
                        mrr.product_code IN
                        <foreach item="item" index="index" collection="productCodeList" open="(" separator="," close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                      AND mrr.enabled = 'YES'
                      AND mrrs.enabled = 'YES'
                      AND mmp.enabled = 'YES'
                      AND mmp.product_type = 'SA'
                ) AS all_values
        ) SELECT
              mrr.product_code as productCode,
              mrr.product_id as productId,
              mrrs.sequence_no as sequenceNo,
              mrss.standard_step_name as standardStepName,
              a.product_code AS sourceProductCode
        FROM
            mds_rou_routing mrr
                LEFT JOIN mds_rou_routing_step mrrs ON mrr.id = mrrs.routing_id
                LEFT JOIN mds_rou_standard_step mrss ON mrrs.standard_step_id = mrss.id
                INNER JOIN a ON a.merged_field = mrr.product_id
        WHERE
            mrss.key_step = 'YES'
    </select>

    <select id="selectDeliveryByCondition" parameterType="java.util.Map"
            resultType="com.yhl.scp.mps.plan.vo.DeliveryPlanOverviewVO">
        select
        t1.product_code         as productCode,
        sum(
        case
        when t1.coordination_quantity > 0 then t1.coordination_quantity
        else t1.demand_quantity
        end
        ) as demandQuantity
        from fdp_delivery_plan_published t1
        where t1.demand_time between #{params.startDateTime,jdbcType=TIMESTAMP} and
        #{params.endDateTime,jdbcType=TIMESTAMP}
        <if test="params.productCodes != null and params.productCodes.size > 0">
            and t1.product_code in
            <foreach collection="params.productCodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and (t1.demand_quantity > 0 or t1.coordination_quantity > 0)
        group by t1.product_code
    </select>
</mapper>
