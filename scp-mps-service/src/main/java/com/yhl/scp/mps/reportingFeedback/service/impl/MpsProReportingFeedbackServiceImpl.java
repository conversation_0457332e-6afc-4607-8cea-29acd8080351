package com.yhl.scp.mps.reportingFeedback.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReportFeedback;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.ReportingStatusEnum;
import com.yhl.scp.mps.operationPublished.service.WorkOrderPublishedService;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.service.MasterPlanRelationLogService;
import com.yhl.scp.mps.plan.service.MasterPlanRelationService;
import com.yhl.scp.mps.plan.vo.MasterPlanRelationLogVO;
import com.yhl.scp.mps.plan.vo.MasterPlanRelationVO;
import com.yhl.scp.mps.reportingFeedback.convertor.MpsProReportingFeedbackConvertor;
import com.yhl.scp.mps.reportingFeedback.domain.entity.MpsProReportingFeedbackDO;
import com.yhl.scp.mps.reportingFeedback.domain.service.MpsProReportingFeedbackDomainService;
import com.yhl.scp.mps.reportingFeedback.dto.MpsProReportingFeedbackDTO;
import com.yhl.scp.mps.reportingFeedback.infrastructure.dao.MpsProReportingFeedbackDao;
import com.yhl.scp.mps.reportingFeedback.infrastructure.po.MpsProReportingFeedbackPO;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.mps.reportingFeedback.vo.MpsProReportingFeedbackVO;
import com.yhl.scp.sds.basic.enums.ReportingTypeEnum;
import com.yhl.scp.sds.basic.enums.TaskTypeEnum;
import com.yhl.scp.sds.basic.order.enums.PlannedStatusEnum;
import com.yhl.scp.sds.extension.feedback.infrastructure.po.FeedbackProductionPO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.dto.OperationDTO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.feedback.infrastructure.dao.FeedbackProductionDao;
import com.yhl.scp.sds.feedback.service.FeedbackProductionService;
import com.yhl.scp.sds.order.convertor.OperationConvertor;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import com.yhl.scp.sds.pegging.service.DemandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <code>MpsProReportingFeedbackServiceImpl</code>
 * <p>
 * 生产报工反馈应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-03 10:32:00
 */
@Slf4j
@Service
public class MpsProReportingFeedbackServiceImpl extends AbstractService implements MpsProReportingFeedbackService {

    @Resource
    private MpsProReportingFeedbackDao mpsProReportingFeedbackDao;

    @Resource
    private MpsProReportingFeedbackDomainService mpsProReportingFeedbackDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private MasterPlanRelationService masterPlanRelationService;

    @Resource
    private FeedbackProductionDao feedbackProductionDao;
    @Resource
    private FeedbackProductionService feedbackProductionService;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;

    @Resource
    private OperationService operationService;

    @Resource
    private DemandService demandService;

    @Resource
    private WorkOrderPublishedService workflowPublishedService;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private MasterPlanRelationLogService masterPlanRelationLogService;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;

    @Override
    public BaseResponse<Void> doCreate(MpsProReportingFeedbackDTO mpsProReportingFeedbackDTO) {
        // 0.数据转换
        MpsProReportingFeedbackDO mpsProReportingFeedbackDO = MpsProReportingFeedbackConvertor.INSTANCE.dto2Do(mpsProReportingFeedbackDTO);
        MpsProReportingFeedbackPO mpsProReportingFeedbackPO = MpsProReportingFeedbackConvertor.INSTANCE.dto2Po(mpsProReportingFeedbackDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mpsProReportingFeedbackDomainService.validation(mpsProReportingFeedbackDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mpsProReportingFeedbackPO);
        mpsProReportingFeedbackDao.insert(mpsProReportingFeedbackPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MpsProReportingFeedbackDTO mpsProReportingFeedbackDTO) {
        // 0.数据转换
        MpsProReportingFeedbackDO mpsProReportingFeedbackDO = MpsProReportingFeedbackConvertor.INSTANCE.dto2Do(mpsProReportingFeedbackDTO);
        MpsProReportingFeedbackPO mpsProReportingFeedbackPO = MpsProReportingFeedbackConvertor.INSTANCE.dto2Po(mpsProReportingFeedbackDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mpsProReportingFeedbackDomainService.validation(mpsProReportingFeedbackDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mpsProReportingFeedbackPO);
        mpsProReportingFeedbackDao.update(mpsProReportingFeedbackPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MpsProReportingFeedbackDTO> list) {
        List<MpsProReportingFeedbackPO> newList = MpsProReportingFeedbackConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mpsProReportingFeedbackDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MpsProReportingFeedbackDTO> list) {
        List<MpsProReportingFeedbackPO> newList = MpsProReportingFeedbackConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mpsProReportingFeedbackDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mpsProReportingFeedbackDao.deleteBatch(idList);
        }
        return mpsProReportingFeedbackDao.deleteByPrimaryKey(idList.get(0));
    }

    /**
     * mes数据接入
     *
     * @param mesProFeedback 包含多个生产反馈信息的列表每个 MpsProReportingFeedbackDTO 对象代表一条生产反馈记录，列表中的这些记录将被聚合成一批进行数据库插入操作
     */
    @Override
    public void doInsertBatch(List<MpsProReportingFeedbackDTO> mesProFeedback) {

        this.doCreateBatch(mesProFeedback);
    }

    @Override
    public MpsProReportingFeedbackVO selectByPrimaryKey(String id) {
        MpsProReportingFeedbackPO po = mpsProReportingFeedbackDao.selectByPrimaryKey(id);
        return MpsProReportingFeedbackConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "REPORTING_FEEDBACK")
    public List<MpsProReportingFeedbackVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "REPORTING_FEEDBACK")
    public List<MpsProReportingFeedbackVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MpsProReportingFeedbackVO> dataList = mpsProReportingFeedbackDao.selectByCondition(sortParam, queryCriteriaParam);
        MpsProReportingFeedbackServiceImpl target = springBeanUtils.getBean(MpsProReportingFeedbackServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MpsProReportingFeedbackVO> selectByParams(Map<String, Object> params) {
        List<MpsProReportingFeedbackPO> list = mpsProReportingFeedbackDao.selectByParams(params);
        return MpsProReportingFeedbackConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MpsProReportingFeedbackVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }



    @Override
    public String getObjectType() {
        return ObjectTypeEnum.REPORTING_FEEDBACK.getCode();
    }

    @Override
    public List<MpsProReportingFeedbackVO> invocation(List<MpsProReportingFeedbackVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public List<String> selectProductOrderCodes(String productOrderCode) {
        return mpsProReportingFeedbackDao.selectProductOrderCodes(productOrderCode);
    }

    @Override
    public BaseResponse<Void> syncProReportingFeedback(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap();
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.REPORT_FEEDBACK.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public void doConvert(String scenario, List<MpsProReportingFeedbackDTO> reportingFeedbackDTOS) {
        if (CollectionUtils.isEmpty(reportingFeedbackDTOS)){
            return;
        }
        Set<String> erpOrderNoList = reportingFeedbackDTOS.stream()
                .map(MpsProReportingFeedbackDTO::getOrderCode)
                .filter(orderCode -> orderCode != null && orderCode.length() >= 9)
                .map(orderCode -> orderCode.substring(1, 9))
                .collect(Collectors.toSet());
        Map<String,Object> masterLogParams =  new HashMap<>();
        masterLogParams.put("wipEntityNameList", erpOrderNoList);
        List<MasterPlanRelationLogVO> masterPlanRelationLogVOList = masterPlanRelationLogService.selectByParams(masterLogParams);
        Map<String, MasterPlanRelationLogVO> masterPlanRelationLogVOMap =  masterPlanRelationLogVOList.stream().collect(
                Collectors.toMap(MasterPlanRelationLogVO::getWipEntityName, Function.identity(), (v1, v2) -> v1));
        List<String> reqNumberList =
                masterPlanRelationLogVOList.stream().map(MasterPlanRelationLogVO::getReqNumber).distinct().collect(Collectors.toList());
        Map<String,Object> params = new HashMap<>();
        params.put("planNos", reqNumberList);
        List<MasterPlanRelationVO> masterPlanRelationVOList = masterPlanRelationService.selectByParams(params);
        Map<String, List<MasterPlanRelationVO>> masterPlanRelationVOMap = masterPlanRelationVOList.stream()
                .collect(Collectors.groupingBy(MasterPlanRelationVO::getPlanNo));
        List<String> orderNoList =
                masterPlanRelationVOList.stream().map(MasterPlanRelationVO::getOrderNo).distinct().collect(Collectors.toList());
        Map<String,Object> orderNoListParams = new HashMap<>();
        orderNoListParams.put("orderNos", orderNoList);
        List<WorkOrderPublishedVO> workOrderPublishedVOList = workflowPublishedService.selectByParams(orderNoListParams);
        List<String> workOrderIds =
                workOrderPublishedVOList.stream().map(WorkOrderPublishedVO::getId).distinct().collect(Collectors.toList());
        Map<String,Object> orderNoListParentIdsParams = new HashMap<>();
        orderNoListParentIdsParams.put("parentIds", workOrderIds);
        List<WorkOrderPublishedVO> workOrderPublishedParentIdsVOList =
                workflowPublishedService.selectByParams(orderNoListParentIdsParams);
        Map<String, List<WorkOrderPublishedVO>> workOrderPublishedParentIdsVOMap = workOrderPublishedParentIdsVOList.stream()
                .filter(vo -> vo.getParentId() != null)
                .collect(Collectors.groupingBy(WorkOrderPublishedVO::getParentId));
        if(CollectionUtils.isNotEmpty(workOrderPublishedParentIdsVOList)){
            workOrderPublishedVOList.addAll(workOrderPublishedParentIdsVOList);
        }
        Map<String, WorkOrderPublishedVO> workOrderPublishedIdsVOMap =  workOrderPublishedVOList.stream().collect(
                Collectors.toMap(WorkOrderPublishedVO::getOrderNo, Function.identity(), (v1, v2) -> v1));
        List<String> workOrderParentIds =
                workOrderPublishedVOList.stream().map(WorkOrderPublishedVO::getId).distinct().collect(Collectors.toList());
        Map<String,Object> operationParams = new HashMap<>();
        operationParams.put("orderIds", workOrderParentIds);
        List<OperationVO> operationVOList = operationService.selectByParams(operationParams);
        Map<String, List<OperationVO>> operationMap = operationVOList.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getParentId()))
                .collect(Collectors.groupingBy(operationVO -> operationVO.getOrderId() + "_" + operationVO.getRoutingStepSequenceNo() + "_" + operationVO.getProductId()));


        List<CollectionValueVO> hwLimitResult =  ipsFeign.getByCollectionCode("HW_LIMIT_STAND_RESOURCE_CODE");
        // if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(hwLimitResult)){
        //     log.info("HW_LIMIT_STAND_RESOURCE_CODE字典表未获取到");
        //     return;
        // }
        List<String> productIds =
                operationVOList.stream().map(OperationVO::getProductId).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByIds(scenario, productIds);
        Map<String, NewProductStockPointVO> newProductStockPointVoMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(
                        vo -> vo.getProductCode() + "_" + vo.getStockPointCode(),
                        Function.identity(),
                        (v1, v2) -> v1 // 保留第一个出现的元素
                ));
        List<PhysicalResourceVO> physicalResourceVOList = newMdsFeign.selectAllPhysicalResource(scenario);
        Map<String, PhysicalResourceVO> physicalResourceVOMap =  physicalResourceVOList.stream().collect(
                Collectors.toMap(PhysicalResourceVO::getPhysicalResourceCode, Function.identity(), (v1, v2) -> v1));
        List<StandardResourceVO> selectStandardResourceVOS =
                newMdsFeign.selectStandardResourceVOS(scenario);
        List<String> hwLimitResultList = hwLimitResult.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
        Map<String, StandardResourceVO> stringStandardResourceVOMap =  selectStandardResourceVOS.stream().collect(
                Collectors.toMap(StandardResourceVO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String,Object> feedbackProductionParams = new HashMap<>();
        feedbackProductionParams.put("workOrderIds", workOrderParentIds);
        List<FeedbackProductionVO> feedbackProductionVOS = feedbackProductionService.selectByParams(feedbackProductionParams);
        Map<String, FeedbackProductionVO> feedbackProductionVOMap =  feedbackProductionVOS.stream().collect(
                Collectors.toMap(FeedbackProductionVO::getOperationId, Function.identity(), (v1, v2) -> v1));
        List<FeedbackProductionPO> updateList = new ArrayList<>();
        List<FeedbackProductionPO> insertList = new ArrayList<>();

        for (MpsProReportingFeedbackDTO exist : reportingFeedbackDTOS) {
            Integer finishQuantity = exist.getFinishQuantity();
            if(finishQuantity == null || finishQuantity == 0){
                log.info("报工反馈==0，不进生产反馈：{}",exist.getId());
                continue;
            }
            String orderCode = exist.getOrderCode();
            //该报工数据对应资源不存在
            if (!physicalResourceVOMap.containsKey(exist.getProductionLine())){
                log.info("生产反馈数据转换异常{}资源不存在:"+exist.getProductionLine());
                continue;
            }
            PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(exist.getProductionLine());
            String physicalResourceId = physicalResourceVO.getId();
            if(orderCode==null||orderCode.length() <= 9){
                log.info("生产订单编号格式有问题:"+orderCode);
                continue;
            }
            String substringOrderCode = orderCode.substring(1, 9);
            if (!masterPlanRelationLogVOMap.containsKey(orderCode.substring(1, 9))){
                log.info("未找到wipEntityName:"+substringOrderCode);
                continue;
            }
            MasterPlanRelationLogVO masterPlanRelationLogVO = masterPlanRelationLogVOMap.get(substringOrderCode);
            String headerId =  masterPlanRelationLogVO.getReqNumber();
            if (!masterPlanRelationVOMap.containsKey(headerId)){
                log.info("未找到headerId:"+headerId);
                continue;
            }
            List<MasterPlanRelationVO> masterPlanRelationList =  masterPlanRelationVOMap.get(headerId);
            for (MasterPlanRelationVO masterPlanRelationVO : masterPlanRelationList) {
                String orderNo = masterPlanRelationVO.getOrderNo();
                if (!workOrderPublishedIdsVOMap.containsKey(orderNo)) {
                    log.info("未找到WorkOrderPublished中的orderNo:" + orderNo);
                    continue;
                }
                List<WorkOrderPublishedVO> workOrderPublishedList= new ArrayList<>();
                WorkOrderPublishedVO workOrderPublishedVO = workOrderPublishedIdsVOMap.get(orderNo);
                List<WorkOrderPublishedVO> workOrderPublishedParentIdsList =
                        workOrderPublishedParentIdsVOMap.get(workOrderPublishedVO.getId());
                workOrderPublishedList.add(workOrderPublishedVO);
                if(CollectionUtils.isNotEmpty(workOrderPublishedParentIdsList)){
                    workOrderPublishedList.addAll(workOrderPublishedParentIdsList);
                }
                for(WorkOrderPublishedVO workOrderPublishedParentIdsVO : workOrderPublishedList) {
                    String productCode = exist.getProductCode();
                    String mainProductCode = getProductCode(productCode);
                    String productId = mainProductCode + "_" + exist.getOrganizationCode();
                    if (!newProductStockPointVoMap.containsKey(productId)) {
                        log.info("newProductStockPointVoMap:" + productId);
                        continue;
                    }
                    NewProductStockPointVO newProductStockPointVO = newProductStockPointVoMap.get(productId);
                    String orderPublishedId =
                            workOrderPublishedParentIdsVO.getId() + "_" + exist.getSequenceCode() + "_" + newProductStockPointVO.getId();
                    if (!operationMap.containsKey(orderPublishedId)) {
                        log.info("workOrderPublishedVOMap:" + orderPublishedId);
                        continue;
                    }
                    List<OperationVO> operationVOS = operationMap.get(orderPublishedId);
                    String standardResourceId = physicalResourceVO.getStandardResourceId();
                    if (!stringStandardResourceVOMap.containsKey(standardResourceId)) {
                        log.info("stringStandardResourceVOMap:" + standardResourceId);
                        continue;
                    }
                    StandardResourceVO standardResourceVO = stringStandardResourceVOMap.get(standardResourceId);
                    String standardResourceCode = standardResourceVO.getStandardResourceCode();
                    if (operationVOS.size() > 1) {
                        if (hwLimitResultList.contains(standardResourceCode)) {
                            BigDecimal quantity = new BigDecimal(exist.getFinishQuantity());
                            BigDecimal size = new BigDecimal(operationVOS.size());
                            // 整除，减少循环次数
                            BigDecimal quotient = quantity.divide(size, 0, RoundingMode.DOWN);
                            // 取余
                            BigDecimal remainder = quantity.remainder(size);
                            //均分
                            operationVOS.sort(Comparator.comparing(OperationVO::getStartTime));
                            for (int i = 0; i < operationVOS.size(); i++) {
                                OperationVO operationVO = operationVOS.get(i);
                                BigDecimal num;
                                // 尾差加到第一道工序
                                if (i == 0) {
                                    num = quotient.add(remainder);
                                } else {
                                    num = quotient;
                                }
                                if (num.compareTo(operationVO.getQuantity()) > 0) {
                                    num = operationVO.getQuantity();
                                }
                                feedBackProductionInsert(feedbackProductionVOMap, updateList, insertList, exist,
                                        physicalResourceId, workOrderPublishedParentIdsVO, operationVO, num);
                            }
                        } else {
                            //集合比例
                            Integer finishQuantityList = exist.getFinishQuantity();
                            // 按 startTime 从远到近排序
                            //  operationVOS.sort(Comparator.comparing(OperationVO::getStartTime).reversed());
                            operationVOS.sort(Comparator.comparing(OperationVO::getStartTime));
                            BigDecimal remainingFinishQuantity = new BigDecimal(finishQuantityList);

                            for (OperationVO operationVO : operationVOS) {
                                if (remainingFinishQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                                    break;
                                }

                                BigDecimal currentFinishQuantity = operationVO.getQuantity();

                                if (currentFinishQuantity.compareTo(remainingFinishQuantity) >= 0) {
                                    // 当前操作的 finishQuantity 大于或等于剩余的 finishQuantityList
                                    // 处理当前操作
                                    feedBackProductionInsert(feedbackProductionVOMap, updateList, insertList, exist,
                                            physicalResourceId, workOrderPublishedParentIdsVO, operationVO, remainingFinishQuantity);
                                    remainingFinishQuantity = BigDecimal.ZERO;
                                } else {
                                    // 当前操作的 finishQuantity 小于剩余的 finishQuantityList
                                    // 处理当前操作并减少剩余的 finishQuantityList
                                    feedBackProductionInsert(feedbackProductionVOMap, updateList, insertList, exist,
                                            physicalResourceId, workOrderPublishedParentIdsVO, operationVO, currentFinishQuantity);
                                    remainingFinishQuantity = remainingFinishQuantity.subtract(currentFinishQuantity);
                                }
                            }

                        }

                    } else {
                        OperationVO operationVO = operationVOS.get(0);
                        BigDecimal finishQuantityBig = new BigDecimal(exist.getFinishQuantity());
                        if (finishQuantityBig.compareTo(operationVO.getQuantity()) > 0) {
                            finishQuantityBig = operationVO.getQuantity();
                        }
                        feedBackProductionInsert(feedbackProductionVOMap, updateList, insertList, exist,
                                physicalResourceId, workOrderPublishedParentIdsVO, operationVO, finishQuantityBig);
                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)){
            log.info("修改feedBack数量：{}", updateList.size());
            BasePOUtils.updateBatchFiller(updateList);
            feedbackProductionDao.updateBatch(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)){
            log.info("新增feedBack数量：{}", insertList.size());
            BasePOUtils.insertBatchFiller(insertList);
            feedbackProductionDao.insertBatch(insertList);
        }

    }


    private void feedBackProductionInsert(Map<String, FeedbackProductionVO> feedbackProductionVOMap,
                                          List<FeedbackProductionPO> updateList,
                                          List<FeedbackProductionPO> insertList, MpsProReportingFeedbackDTO exist,
                                          String physicalResourceId, WorkOrderPublishedVO workOrderPublishedVO,
                                          OperationVO operationVO, BigDecimal finishQuantity) {
        String operationId = operationVO.getId();
        FeedbackProductionPO po = new FeedbackProductionPO();
        po.setWorkOrderId(workOrderPublishedVO.getId());
        po.setOperationId(operationId);
        // po.setRoutingStepId(operationVO.getRoutingStepId());
        po.setPhysicalResourceId(physicalResourceId);
        po.setReportingType(ReportingTypeEnum.SON.getCode());
        po.setTaskType(TaskTypeEnum.PRODUCTION.getCode());
        // 报工数量大于等于工序排产数量即为完工状态
        po.setReportingStatus(finishQuantity.compareTo(operationVO.getQuantity()) >= 0 ? ReportingStatusEnum.FINISHED.getCode() :
                ReportingStatusEnum.STARTED.getCode());
        po.setReportingQuantity(finishQuantity);
        po.setReportingScrap(new BigDecimal(exist.getScrapQuantity()));
        if (exist.getEndUpdateTime().before(operationVO.getEndTime())) {
            po.setReportingTime(exist.getEndUpdateTime());
        }else {
            po.setReportingTime(operationVO.getEndTime());
        }
        po.setStartTime(operationVO.getStartTime());
        po.setWhetherFeedBack(YesOrNoEnum.NO.getCode());
        if (feedbackProductionVOMap.containsKey(operationId)){
            FeedbackProductionVO feedbackProductionVO = feedbackProductionVOMap.get(operationId);
            po.setId(feedbackProductionVO.getId());
            po.setCreator(feedbackProductionVO.getCreator());
            po.setCreateTime(feedbackProductionVO.getCreateTime());
            po.setModifier(feedbackProductionVO.getModifier());
            po.setModifyTime(feedbackProductionVO.getModifyTime());
            po.setVersionValue(feedbackProductionVO.getVersionValue());
            po.setEnabled(feedbackProductionVO.getEnabled());
            updateList.add(po);
        }else {
            po.setId(UUIDUtil.getUUID());
            insertList.add(po);
        }
    }

    @Override
    public BaseResponse<Void> handleReportingFeedback(String scenario, List<MesReportFeedback> feedbacks) {
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(feedbacks)) {
            return BaseResponse.success();
        }
        List<String> orderCodes = feedbacks.stream().map(MesReportFeedback::getWorkOrderNum).collect(Collectors.toList());
        List<List<String>> splitOrderCodes = com.yhl.platform.common.utils.CollectionUtils.splitList(orderCodes, 1000);
        List<MpsProReportingFeedbackVO> cuuRfVOs = new ArrayList<>();
        for (List<String> subOrderCodes : splitOrderCodes) {
            Map<String, Object> paramMap = MapUtil.newHashMap();
            paramMap.put("orderCodes", subOrderCodes);
            List<MpsProReportingFeedbackVO> rfVOs = selectByParams(paramMap);
            cuuRfVOs.addAll(rfVOs);
        }
        Map<String, MpsProReportingFeedbackVO> feedbackMap = cuuRfVOs.stream().collect(Collectors.toMap(MpsProReportingFeedbackVO::getOrderCode, Function.identity()));
        List<MpsProReportingFeedbackDTO> insertBatchDTOs = Lists.newArrayList();
        List<MpsProReportingFeedbackDTO> updateBatchDTOs = Lists.newArrayList();
        for (MesReportFeedback syncFeedback : feedbacks) {
            MpsProReportingFeedbackDTO reportFeedbackDTO = MpsProReportingFeedbackDTO.builder()
                    .organizationCode(syncFeedback.getPlantCode())
                    .organizationName(syncFeedback.getPlantName())
                    .orderCode(syncFeedback.getWorkOrderNum())
                    .productCode(syncFeedback.getItemCode())
                    .productionLine(syncFeedback.getProdLineCode())
                    .statusCode(syncFeedback.getWorkOrderStatus())
                    .statusDesc(syncFeedback.getMeaning())
                    .planQuantity(syncFeedback.getWoQty())
                    .finishQuantity(syncFeedback.getCompletedQty())
                    .scrapQuantity(syncFeedback.getScrapedQty())
                    .sequenceCode(syncFeedback.getRoutingSequence()+"")
                    .endUpdateTime(syncFeedback.getLastUpdateDate())
                    .build();
            if(feedbackMap.containsKey(syncFeedback.getWorkOrderNum())) {
                MpsProReportingFeedbackVO sourceDTO = feedbackMap.get(syncFeedback.getWorkOrderNum());
                //修改
                reportFeedbackDTO.setVersionValue(sourceDTO.getVersionValue());
                reportFeedbackDTO.setId(sourceDTO.getId());
                updateBatchDTOs.add(reportFeedbackDTO);
            }else {
                //新增
                insertBatchDTOs.add(reportFeedbackDTO);
            }
        }//todo 需要把数据转换成FeedbackProductionVO
        List<MpsProReportingFeedbackDTO> result  = new ArrayList<>();
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(insertBatchDTOs)) {
            doCreateBatch(insertBatchDTOs);
            result.addAll(insertBatchDTOs);
        }
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(updateBatchDTOs)) {
            doUpdateBatch(updateBatchDTOs);
            result.addAll(updateBatchDTOs);
        }
        doConvert(scenario, result);
        return BaseResponse.success("同步成功");
    }

    public static void main(String[] args) {
        String test = "SZ01-S0400LFW00029-45";

        System.out.println(getProductCode(test));
    }

    private static String getProductCode(String code) {
        Pattern pattern = Pattern.compile(".*-(\\d{2})$");
        Matcher matcher = pattern.matcher(code);
        while (matcher.find()) {
            int lastIndexOf = code.lastIndexOf("-");
            code = code.substring(0, lastIndexOf);
        }
        return code;
    }

    @Override
    public void doProcessStartOperation() {
        List<OperationVO> operationVOS = operationService.selectByParams(ImmutableMap.of("planStatus", PlannedStatusEnum.STARTED.getCode()));
        log.info("已开始工序数量：{}", operationVOS.size());
        if (CollectionUtils.isEmpty(operationVOS)) {
            return;
        }
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(null);
        Map<String, StandardStepVO> standardStepVOMap = StreamUtils.mapByColumn(standardStepVOS, StandardStepVO::getId);
        Date date = new Date();
        Date reportTime = DateUtil.offsetHour(date, -12);
        List<String> operationIds = StreamUtils.columnToList(operationVOS, OperationVO::getId);
        List<FeedbackProductionVO> feedbackProductionVOS = masterPlanExtDao.selectFeedBackList(reportTime, operationIds);
        Map<String, List<FeedbackProductionVO>> feedBackMap = StreamUtils.mapListByColumn(feedbackProductionVOS, FeedbackProductionVO::getOperationId);
        List<OperationDTO> finishedOperation = new ArrayList<>();
        List<String> deleteDemandIds = new ArrayList<>();
        for (OperationVO operationVO : operationVOS) {
            String id = operationVO.getId();
            String standardStepId = operationVO.getStandardStepId();
            if (StrUtil.isNotEmpty(standardStepId) && standardStepVOMap.containsKey(standardStepId)) {
                StandardStepVO standardStepVO = standardStepVOMap.get(standardStepId);
                String standardStepName = standardStepVO.getStandardStepName();
                if ("包装".equals(standardStepName)) {
                    continue;
                }
            }
            if (feedBackMap.containsKey(id)) {
                // 已开始的工序12小时内存在报工数据
                continue;
            }
            deleteDemandIds.add(id);
            if (StrUtil.isNotEmpty(operationVO.getParentId())) {
                deleteDemandIds.add(operationVO.getParentId());
            }
            operationVO.setPlanStatus(PlannedStatusEnum.FINISHED.getCode());
            OperationDTO operationDTO = OperationConvertor.INSTANCE.vo2Dto(operationVO);
            finishedOperation.add(operationDTO);
        }
        log.info("生产反馈更新工序完工行：{}", finishedOperation.size());
        if (CollectionUtils.isNotEmpty(finishedOperation)) {
            finishedOperation.forEach(t->t.setRemark("生产反馈更新历史开始未完工工序工序状态"));
            operationService.doUpdateBatch(finishedOperation);
        }
        log.info("删除demand对应工序id：{}", JSON.toJSONString(deleteDemandIds));
        if (CollectionUtils.isNotEmpty(deleteDemandIds)) {
            masterPlanExtDao.deleteDemandByOperationIds(deleteDemandIds);
        }
    }

}
