package com.yhl.scp.mps.plan.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.extension.routing.infrastructure.po.StandardStepPO;
import com.yhl.scp.mps.plan.domain.entity.MasterPlanDO;
import com.yhl.scp.mps.plan.dto.MasterPlanDTO;
import com.yhl.scp.mps.plan.infrastructure.po.MasterPlanPO;
import com.yhl.scp.mps.plan.vo.DeliveryPlanOverviewVO;
import com.yhl.scp.mps.plan.vo.MasterPlanAlertingVO;
import com.yhl.scp.mps.plan.vo.MasterPlanVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>MasterPlanDao</code>
 * <p>
 * 主生产计划表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 13:52:50
 */
public interface MasterPlanDao extends BaseDao<MasterPlanPO, MasterPlanVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MasterPlanVO}
     */
    List<MasterPlanVO> selectVOByParams(@Param("params") Map<String, Object> params);


    List<MasterPlanPO> selectProductQuantity(@Param("productCodes") List<String> productCodes);

    void updateByPlanId(@Param("item") MasterPlanPO masterPlanPO);


    MasterPlanDO selectByPlanId(String planId);

    List<MasterPlanPO> selectByPlanStartTime(Date planStartDate);

    void updateSpendTime(double spendTime, MasterPlanDTO masterPlanDTO);

    MasterPlanPO selectByProductCode(String productCode);

    List<StandardStepPO> selectOperationDropdown(@Param("params") Map<String, Object> params);

    void deleteOldDemand(@Param("orderIds") List<String> orderIds);

    void deleteDemandByIds(@Param("demandIds") List<String> demandIds);

    /**
     * 获取未计划的物料
     * @return
     */
    List<String> getUnPlanDeliveryPlan(@Param("startTime") String startTime);


    List<MasterPlanAlertingVO> getKeyStandardStepByProductCode(@Param("productCodeList") List<String> productCodeList);


    List<DeliveryPlanOverviewVO> selectDeliveryByCondition(@Param("params") Map<String, Object> params);

}
