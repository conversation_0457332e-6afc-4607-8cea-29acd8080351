package com.yhl.scp.mps.demand.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.demand.convertor.OutsourceTransferDemandDetailConvertor;
import com.yhl.scp.mps.demand.domain.entity.OutsourceTransferDemandDetailDO;
import com.yhl.scp.mps.demand.domain.service.OutsourceTransferDemandDetailDomainService;
import com.yhl.scp.mps.demand.dto.OutsourceTransferDemandDetailDTO;
import com.yhl.scp.mps.demand.infrastructure.dao.OutsourceTransferDemandDetailDao;
import com.yhl.scp.mps.demand.infrastructure.po.OutsourceTransferDemandDetailPO;
import com.yhl.scp.mps.demand.service.OutsourceTransferDemandDetailService;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>OutsourceTransferDemandDetailServiceImpl</code>
 * <p>
 * 委外转产材料需求明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-24 18:47:46
 */
@Slf4j
@Service
public class OutsourceTransferDemandDetailServiceImpl extends AbstractService implements OutsourceTransferDemandDetailService {

    @Resource
    private OutsourceTransferDemandDetailDao outsourceTransferDemandDetailDao;

    @Resource
    private OutsourceTransferDemandDetailDomainService outsourceTransferDemandDetailDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(OutsourceTransferDemandDetailDTO outsourceTransferDemandDetailDTO) {
        // 0.数据转换
        OutsourceTransferDemandDetailDO outsourceTransferDemandDetailDO = OutsourceTransferDemandDetailConvertor.INSTANCE.dto2Do(outsourceTransferDemandDetailDTO);
        OutsourceTransferDemandDetailPO outsourceTransferDemandDetailPO = OutsourceTransferDemandDetailConvertor.INSTANCE.dto2Po(outsourceTransferDemandDetailDTO);
        // 1.数据校验
        outsourceTransferDemandDetailDomainService.validation(outsourceTransferDemandDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(outsourceTransferDemandDetailPO);
        outsourceTransferDemandDetailDao.insertWithPrimaryKey(outsourceTransferDemandDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OutsourceTransferDemandDetailDTO outsourceTransferDemandDetailDTO) {
        // 0.数据转换
        OutsourceTransferDemandDetailDO outsourceTransferDemandDetailDO = OutsourceTransferDemandDetailConvertor.INSTANCE.dto2Do(outsourceTransferDemandDetailDTO);
        OutsourceTransferDemandDetailPO outsourceTransferDemandDetailPO = OutsourceTransferDemandDetailConvertor.INSTANCE.dto2Po(outsourceTransferDemandDetailDTO);
        // 1.数据校验
        outsourceTransferDemandDetailDomainService.validation(outsourceTransferDemandDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(outsourceTransferDemandDetailPO);
        outsourceTransferDemandDetailDao.updateSelective(outsourceTransferDemandDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OutsourceTransferDemandDetailDTO> list) {
        List<OutsourceTransferDemandDetailPO> newList = OutsourceTransferDemandDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        outsourceTransferDemandDetailDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OutsourceTransferDemandDetailDTO> list) {
        List<OutsourceTransferDemandDetailPO> newList = OutsourceTransferDemandDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        outsourceTransferDemandDetailDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return outsourceTransferDemandDetailDao.deleteBatch(idList);
        }
        return outsourceTransferDemandDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OutsourceTransferDemandDetailVO selectByPrimaryKey(String id) {
        OutsourceTransferDemandDetailPO po = outsourceTransferDemandDetailDao.selectByPrimaryKey(id);
        return OutsourceTransferDemandDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mps_outsource_transfer_demand_detail")
    public List<OutsourceTransferDemandDetailVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mps_outsource_transfer_demand_detail")
    public List<OutsourceTransferDemandDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OutsourceTransferDemandDetailVO> dataList = outsourceTransferDemandDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        OutsourceTransferDemandDetailServiceImpl target = springBeanUtils.getBean(OutsourceTransferDemandDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OutsourceTransferDemandDetailVO> selectByParams(Map<String, Object> params) {
        List<OutsourceTransferDemandDetailPO> list = outsourceTransferDemandDetailDao.selectByParams(params);
        return OutsourceTransferDemandDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OutsourceTransferDemandDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OUTSOURCE_TRANSFER_DEMAND_DETAIL.getCode();
    }

    @Override
    public List<OutsourceTransferDemandDetailVO> invocation(List<OutsourceTransferDemandDetailVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

	@Override
	public void deleteBySummaryIds(List<String> summaryIds) {
		outsourceTransferDemandDetailDao.deleteBySummaryIds(summaryIds);
	}

}
