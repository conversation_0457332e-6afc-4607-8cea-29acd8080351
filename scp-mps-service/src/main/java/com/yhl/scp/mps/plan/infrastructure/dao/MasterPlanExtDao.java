package com.yhl.scp.mps.plan.infrastructure.dao;

import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mps.domain.dispatch.model.dto.SplitRoutingStepResourceDTO;
import com.yhl.scp.mps.fixtureRelation.infrastructure.po.ProductFixtureRelationPO;
import com.yhl.scp.mps.plan.vo.WorkOrderYieldVO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MasterPlanExtDao extends OperationDao {
    List<OperationPO> selectByIds(@Param("operationIds") List<String> operationIds);

    /**
     * 查询关键工序对应的operation数据
     *
     * @return
     */
    List<OperationPO> selectByKeyStep(@Param("orderIds") List<String> orderIds);

    void deletePlanUnitByOrderId(@Param("orderIds") List<String> orderIds);

    void deleteByOperationIds(@Param("operationIds") List<String> operationIds);

    void doClearFulfillment();

    /**
     * 通过工序id查询供应关系
     *
     * @param params
     * @return
     */
    List<SupplyVO> selectSupplyVOByOperationIds(@Param("params") Map<String, Object> params);

    /**
     * 查询调整建议可用的制造订单
     *
     * @return
     */
    List<WorkOrderVO> selectDelayAdjustWorkOrder();

    /**
     * 查询锁定期内的制造订单
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<FeedbackProductionVO> selectLockFeedbackList(@Param("startTime") Date startTime,
                                                      @Param("endTime") Date endTime,
                                                      @Param("permissionResourceIds") List<String> permissionResourceIds);

    List<OperationPO> selectByIds2(@Param("operationIds") List<String> operationIds);

    List<WorkOrderPO> selectPublishWorkOrder();

    List<String> selectParentWorkOrder(@Param("parentIds") List<String> parentIds);

    /**
     * 通过工序id查询需求关系
     *
     * @param params
     * @return
     */
    List<DemandVO> selectDemandVOByOperationIds(@Param("params") Map<String, Object> params);

    List<WorkOrderYieldVO> selectWorkOrderYield(@Param("orderNoList") List<String> orderNoList);

    List<RoutingStepVO> selectSemiStep(@Param("productCode") String productCode, @Param("productId") String productId);

    List<FeedbackProductionVO> selectFeedBackList(@Param("reportTime") Date reportTime, @Param("operationIds") List<String> operationIds);

    void deleteDemandByOperationIds(@Param("deleteDemandIds") List<String> deleteDemandIds);

    List<WorkOrderVO> selectWorkOrderByLineGroupAndProductLine(@Param("lineGroupList") List<String> lineGroupList,
                                                               @Param("productLineList") List<String> productLineList);

    List<PhysicalResourceVO> selectResourceByUserIdAndRoutingStepId(@Param("userId") String userId, @Param("routingStepId") String routingStepId);

    List<String> selectDeleteWorkOrder(@Param("ids") List<String> ids);

    List<SplitRoutingStepResourceDTO> getSplitRoutingStepResources(@Param("routingIds") List<String> routingIds);

    Integer selectWorkOrderMaxPriority();

    Integer clearBatchWorkOrderPriority(@Param("orderIds") List<String> orderIds);


    List<SafetyStockLevelVO> selectByProductCodeList(@Param("codeList") List<String> codeList,
                                                     @Param("planArea") String planArea,
                                                     @Param("organizeType") String organizeType);

    List<OperationVO> selectOrderOperation(@Param("operationIds") List<String> operationIds);

    List<OperationVO> selectOperationByParentWorkOrderIds(@Param("parentWorkOrderIds") List<String> parentWorkOrderIds);

    List<ProductFixtureRelationPO> selectWindFenceProductFixtureRelationsByProductCodes(@Param("productCodes") List<String> productCodes);


    List<String> selectWorkOrderIdsByResourceId(@Param("startTime") String startTime, @Param("resourceIds") List<String> resourceIds);
}
