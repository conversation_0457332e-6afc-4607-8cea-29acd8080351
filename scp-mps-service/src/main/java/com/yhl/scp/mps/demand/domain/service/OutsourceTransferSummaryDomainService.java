package com.yhl.scp.mps.demand.domain.service;

import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.demand.domain.entity.OutsourceTransferSummaryDO;
import com.yhl.scp.mps.demand.infrastructure.dao.OutsourceTransferSummaryDao;
import com.yhl.scp.mps.demand.infrastructure.po.OutsourceTransferSummaryPO;
import com.yhl.scp.mps.demand.vo.OutsourceTransferSummaryVO;
import com.yhl.scp.mps.enums.ChangeTypeEnum;
import com.yhl.scp.mps.enums.OutsourceStatusEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>OutsourceTransferSummaryDomainService</code>
 * <p>
 * 委外转产需求汇总领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 10:12:40
 */
@Service
public class OutsourceTransferSummaryDomainService {

    @Resource
    private OutsourceTransferSummaryDao outsourceTransferSummaryDao;
    
    @Resource
    private NewMdsFeign newMdsFeign;

    /**
     * 数据校验
     *
     * @param outsourceTransferSummaryDO 领域对象
     */
    public void validation(OutsourceTransferSummaryDO outsourceTransferSummaryDO) {
        checkNotNull(outsourceTransferSummaryDO);
        checkProductCode(outsourceTransferSummaryDO);
        checkUniqueCode(outsourceTransferSummaryDO);
    }
    
    /**
     * 本厂编码，转产后编码校验
     * @param outsourceTransferSummaryDO
     */
    private void checkProductCode(OutsourceTransferSummaryDO outsourceTransferSummaryDO) {
		List<String> productCodes = new ArrayList<>();
		productCodes.add(outsourceTransferSummaryDO.getProductCode());
		if(ChangeTypeEnum.PROCESS_OUTSOURCING.getCode().equals(outsourceTransferSummaryDO.getChangeType())){
			 productCodes.add(outsourceTransferSummaryDO.getChangeAfterCode());   
        }      
    	List<NewProductStockPointVO> productList = newMdsFeign.selectByProductCode(SystemHolder.getScenario(), productCodes);
    	Map<String, NewProductStockPointVO> productMap = new HashMap<>();
    	productList.forEach( e -> {
    		productMap.put(e.getProductCode(), e);
    	});
    	if(!productMap.containsKey(outsourceTransferSummaryDO.getProductCode())) {
    		throw new BusinessException("本厂编码:" + outsourceTransferSummaryDO.getProductCode() + "不存在!");
    	}
    	NewProductStockPointVO newProductStockPointVO = productMap.get(outsourceTransferSummaryDO.getProductCode());
    	outsourceTransferSummaryDO.setProductName(newProductStockPointVO.getProductName());
    	outsourceTransferSummaryDO.setVehicleModelCode(newProductStockPointVO.getVehicleModelCode());
    	/*if(ChangeTypeEnum.PROCESS_OUTSOURCING.getCode().equals(outsourceTransferSummaryDO.getChangeType())
    			&& !productMap.containsKey(outsourceTransferSummaryDO.getChangeAfterCode())){
			 throw new BusinessException("转产后编码:" + outsourceTransferSummaryDO.getChangeAfterCode() + "不存在!");
        }*/
	}

	/**
     * 非空检验
     *
     * @param outsourceTransferSummaryDO 领域对象
     */
    private void checkNotNull(OutsourceTransferSummaryDO outsourceTransferSummaryDO) {
        if(StringUtils.isEmpty(outsourceTransferSummaryDO.getProductCode())){
            throw new BusinessException("本厂编码不能为空");
        }
        if(StringUtils.isEmpty(outsourceTransferSummaryDO.getChangeType())){
            throw new BusinessException("转产类型不能为空");
        }
/*        if(ChangeTypeEnum.PROCESS_OUTSOURCING.getCode().equals(outsourceTransferSummaryDO.getChangeType())){
            if (StringUtils.isEmpty(outsourceTransferSummaryDO.getChangeAfterCode())){
                throw new BusinessException("工序委外时转产后编码不能为空！");
            }
        }  */
        if(outsourceTransferSummaryDO.getChangeStartDate()==null){
            throw new BusinessException("转产开始日期不能为空！");
        }   
        if(outsourceTransferSummaryDO.getChangeEndDate()==null){
            throw new BusinessException("转产结束日期不能为空！");
        }
    }

    /**
     * 唯一性校验
     *
     * @param outsourceTransferSummaryDO 领域对象
     */
    private void checkUniqueCode(OutsourceTransferSummaryDO outsourceTransferSummaryDO) {
    	List<String> productCodes = new ArrayList<>();
    	productCodes.add(outsourceTransferSummaryDO.getProductCode());
    	if(StringUtils.isNotEmpty(outsourceTransferSummaryDO.getChangeAfterCode())) {
    		productCodes.add(outsourceTransferSummaryDO.getChangeAfterCode());
    	}
        List<OutsourceTransferSummaryVO> outsourceTransferSummaryVOS = outsourceTransferSummaryDao.selectForCheckUnique(productCodes);
        outsourceTransferSummaryVOS=outsourceTransferSummaryVOS.stream().filter(e->!e.getId().equals(outsourceTransferSummaryDO.getId())).collect(Collectors.toList());
        Date changeStartDate = outsourceTransferSummaryDO.getChangeStartDate();
        Date changeEndDate = outsourceTransferSummaryDO.getChangeEndDate();
        for (OutsourceTransferSummaryVO outsourceTransferSummaryVO : outsourceTransferSummaryVOS) {
			if(isDateOverlap(changeStartDate, changeEndDate, outsourceTransferSummaryVO.getChangeStartDate(), outsourceTransferSummaryVO.getChangeEndDate())) {
				String dates = DateUtils.dateToString(outsourceTransferSummaryVO.getChangeStartDate(), DateUtils.COMMON_DATE_STR3) + "|"
	                    + DateUtils.dateToString(outsourceTransferSummaryVO.getChangeEndDate(), DateUtils.COMMON_DATE_STR3);
				throw new BusinessException("本厂编码:"+outsourceTransferSummaryDO.getProductCode()+"在已有数据"+dates+"范围内已存在");
			}
		}
    }
    
    /**
     * 校验是时间是否有重叠
     * @param date1Start
     * @param date1End
     * @param date2Start
     * @param date2End
     * @return
     */
    private boolean isDateOverlap(Date date1Start, Date date1End, Date date2Start, Date date2End) {
        return !(date1Start.after(date2End) || date1End.before(date2Start));
    }
    
    public void checkCanUpdate(OutsourceTransferSummaryDO outsourceTransferSummaryDO) {
    	OutsourceTransferSummaryPO po = outsourceTransferSummaryDao.selectByPrimaryKey(outsourceTransferSummaryDO.getId());
    	if(YesOrNoEnum.NO.getCode().equals(po.getEnabled())) {
    		throw new BusinessException("无效数据不支持编辑!");
    	}
        if(OutsourceStatusEnum.ISSUED.getCode().equals(po.getOutsourceStatus())) {
        	throw new BusinessException("已下发数据不支持编辑!");
        }
    }

}
