package com.yhl.scp.mps.demand.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceAlgorithmExecuteService;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceVersionService;
import com.yhl.scp.mps.demand.convertor.OutsourceTransferSummaryConvertor;
import com.yhl.scp.mps.demand.domain.entity.OutsourceTransferSummaryDO;
import com.yhl.scp.mps.demand.domain.service.OutsourceTransferSummaryDomainService;
import com.yhl.scp.mps.demand.dto.OutsourceTransferSummaryDTO;
import com.yhl.scp.mps.demand.infrastructure.dao.OutsourceTransferSummaryDao;
import com.yhl.scp.mps.demand.infrastructure.po.OutsourceTransferSummaryPO;
import com.yhl.scp.mps.demand.service.OutsourceTransferDemandDetailService;
import com.yhl.scp.mps.demand.service.OutsourceTransferSummaryService;
import com.yhl.scp.mps.demand.service.OutsourceTransferSupplyService;
import com.yhl.scp.mps.demand.vo.OutsourceTransferSummaryVO;
import com.yhl.scp.mps.demand.vo.OutsourceTransferSupplyVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.OutsourceStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <code>OutsourceTransferSummaryServiceImpl</code>
 * <p>
 * 委外转产需求汇总应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 10:12:39
 */
@Slf4j
@Service
public class OutsourceTransferSummaryServiceImpl extends AbstractService implements OutsourceTransferSummaryService {

    @Resource
    private OutsourceTransferSummaryDao outsourceTransferSummaryDao;

    @Resource
    private OutsourceTransferSummaryDomainService outsourceTransferSummaryDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private OutsourceTransferSupplyService outsourceTransferSupplyService;
    
    @Resource
    private OutsourceTransferDemandDetailService outsourceTransferDemandDetailService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private CapacityBalanceAlgorithmExecuteService algorithmExecuteService;

    @Resource
    private CapacityBalanceVersionService capacityBalanceVersionService;

    @Override
    public BaseResponse<Void> doCreate(OutsourceTransferSummaryDTO outsourceTransferSummaryDTO) {
        // 0.数据转换
        OutsourceTransferSummaryDO outsourceTransferSummaryDO = OutsourceTransferSummaryConvertor.INSTANCE.dto2Do(outsourceTransferSummaryDTO);
        OutsourceTransferSummaryPO outsourceTransferSummaryPO = OutsourceTransferSummaryConvertor.INSTANCE.dto2Po(outsourceTransferSummaryDTO);
        // 1.数据校验
        outsourceTransferSummaryDomainService.validation(outsourceTransferSummaryDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(outsourceTransferSummaryPO);
        outsourceTransferSummaryPO.setProductName(outsourceTransferSummaryDO.getProductName());
        outsourceTransferSummaryPO.setVehicleModelCode(outsourceTransferSummaryDO.getVehicleModelCode());
        //不传则默认当前月份
        if(StringUtils.isEmpty(outsourceTransferSummaryDTO.getDecisionMonth())){
            outsourceTransferSummaryDTO.setDecisionMonth(DateUtils.dateToString(new Date(),"yyyyMM"));
        }
        outsourceTransferSummaryPO.setOutsourceStatus(OutsourceStatusEnum.UNISSUED.getCode());
        outsourceTransferSummaryDao.insert(outsourceTransferSummaryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OutsourceTransferSummaryDTO outsourceTransferSummaryDTO) {
        // 0.数据转换
        OutsourceTransferSummaryDO outsourceTransferSummaryDO = OutsourceTransferSummaryConvertor.INSTANCE.dto2Do(outsourceTransferSummaryDTO);
        OutsourceTransferSummaryPO outsourceTransferSummaryPO = OutsourceTransferSummaryConvertor.INSTANCE.dto2Po(outsourceTransferSummaryDTO);
        // 1.数据校验
        outsourceTransferSummaryDomainService.validation(outsourceTransferSummaryDO);
        outsourceTransferSummaryDomainService.checkCanUpdate(outsourceTransferSummaryDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(outsourceTransferSummaryPO);
        outsourceTransferSummaryPO.setProductName(outsourceTransferSummaryDO.getProductName());
        outsourceTransferSummaryPO.setVehicleModelCode(outsourceTransferSummaryDO.getVehicleModelCode());
        outsourceTransferSummaryDao.updateSelective(outsourceTransferSummaryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OutsourceTransferSummaryDTO> list) {
        List<OutsourceTransferSummaryPO> newList = OutsourceTransferSummaryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        outsourceTransferSummaryDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OutsourceTransferSummaryDTO> list) {
        List<OutsourceTransferSummaryPO> newList = OutsourceTransferSummaryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        outsourceTransferSummaryDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        //查询当前数据是否都是未下发
        List<OutsourceTransferSummaryPO> poList = outsourceTransferSummaryDao.selectByPrimaryKeys(idList);
        poList = poList.stream().filter( e -> OutsourceStatusEnum.ISSUED.getCode().equals(e.getOutsourceStatus())).collect(Collectors.toList());
        if(CollUtil.isNotEmpty(poList)) {
        	throw new BusinessException("已下发的委外转产需求不允许删除!");
        }
        if (idList.size() > 1) {
            return outsourceTransferSummaryDao.deleteBatch(idList);
        }
        return outsourceTransferSummaryDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OutsourceTransferSummaryVO selectByPrimaryKey(String id) {
        OutsourceTransferSummaryPO po = outsourceTransferSummaryDao.selectByPrimaryKey(id);
        return OutsourceTransferSummaryConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mps_outsource_transfer_summary")
    public List<OutsourceTransferSummaryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mps_outsource_transfer_summary")
    public List<OutsourceTransferSummaryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OutsourceTransferSummaryVO> dataList = outsourceTransferSummaryDao.selectByCondition(sortParam, queryCriteriaParam);
        OutsourceTransferSummaryServiceImpl target = SpringBeanUtils.getBean(OutsourceTransferSummaryServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OutsourceTransferSummaryVO> selectByParams(Map<String, Object> params) {
        List<OutsourceTransferSummaryPO> list = outsourceTransferSummaryDao.selectByParams(params);
        return OutsourceTransferSummaryConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OutsourceTransferSummaryVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void doIssued(List<String> ids, String outsourceStatus) {
        if (CollectionUtils.isNotEmpty(ids)) {
        	//查询当前数据是否都是未下发
            List<OutsourceTransferSummaryPO> poList = outsourceTransferSummaryDao.selectByPrimaryKeys(ids);
            poList = poList.stream().filter( e -> YesOrNoEnum.YES.getCode().equals(e.getEnabled())).collect(Collectors.toList());
            List<OutsourceTransferSummaryPO> errorList = poList.stream().filter( e -> outsourceStatus.equals(e.getOutsourceStatus())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(errorList)) {
            	if(OutsourceStatusEnum.ISSUED.getCode().equals(outsourceStatus)) {
            		throw new BusinessException("已下发的委外转产需求请勿重复下发!");
            	}else {
            		throw new BusinessException("未下发的委外转产需求请勿重复取消下发!");
            	}
            }
            if(CollUtil.isEmpty(poList)) {
            	throw new BusinessException("请选择有效数据进行操作!");
            }
            List<OutsourceTransferSummaryPO> updateBatchPo = new ArrayList<>();
            for (OutsourceTransferSummaryPO po : poList) {
 /*           	if(OutsourceStatusEnum.ISSUED.getCode().equals(outsourceStatus) && StringUtils.isEmpty(po.getChangeAfterCode())) {
            		throw new BusinessException("请先维护转产后编码,再进行下发!");
            	}*/
				OutsourceTransferSummaryPO update = new OutsourceTransferSummaryPO();
				update.setId(po.getId());
				update.setVersionValue(po.getVersionValue());
				update.setOutsourceStatus(outsourceStatus);
				updateBatchPo.add(update);
			}
            BasePOUtils.updateBatchFiller(updateBatchPo);
            outsourceTransferSummaryDao.updateBatchSelective(updateBatchPo);
            //删除对应的委外转产材料需求计算数据
            if(OutsourceStatusEnum.ISSUED.getCode().equals(outsourceStatus)) {
            	List<String> summaryIds = poList.stream().map(OutsourceTransferSummaryPO::getId).collect(Collectors.toList());
            	outsourceTransferSupplyService.deleteBySummaryIds(summaryIds);
            	outsourceTransferDemandDetailService.deleteBySummaryIds(summaryIds);
            }

            doRefreshCapacityBalance();
        }
    }

    private void doRefreshCapacityBalance() {
        String scenario = SystemHolder.getScenario();
        String userId = SystemHolder.getUserId();
        ConsistenceDemandForecastVersionVO forecastVersionVO = dfpFeign.selectConsistenceDemandForecastVersionLatestPublished(scenario);
        if(forecastVersionVO != null) {
            Integer planHorizon = forecastVersionVO.getPlanHorizon();
            String capacityPeriod = planHorizon == null ? null : String.valueOf(planHorizon);
            CompletableFuture.runAsync(() -> {
                try {
                    DynamicDataSourceContextHolder.setDataSource(scenario);
                    //执行产能平衡计算
                    algorithmExecuteService.capacityBalanceExecuteLock(capacityPeriod, scenario, userId);
                    //发布版本
                    capacityBalanceVersionService.publishVersionLock();
                    DynamicDataSourceContextHolder.clearDataSource();
                } catch (Exception e) {
                    log.error("产能平衡计算失败", e);
                }
            });
        }
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OUTSOURCE_TRANSFER_SUMMARY.getCode();
    }

    @Override
    public List<OutsourceTransferSummaryVO> invocation(List<OutsourceTransferSummaryVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

	@Override
	public Boolean checkCancelissued(List<String> ids) {
		if (CollectionUtils.isEmpty(ids)) {
			throw new BusinessException("请选择需要取消下发的委外需求!");
        }
		HashMap<String, Object> params = new HashMap<>();
        params.put("outsourceTransferSummaryIds", ids);
        params.put("enabled", YesOrNoEnum.YES.getCode());
		List<OutsourceTransferSupplyVO> supplyVOList = outsourceTransferSupplyService.selectByParams(params);
		if(CollUtil.isEmpty(supplyVOList)) {
			return true;
		}else {
			return false;
		}
	}

	@Override
	public void updateEnabledForJob() {
		outsourceTransferSummaryDao.updateEnabledForJob();
	}

}
