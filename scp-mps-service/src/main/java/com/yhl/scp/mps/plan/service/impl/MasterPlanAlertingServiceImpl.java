package com.yhl.scp.mps.plan.service.impl;

import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.basic.routing.vo.BomRoutingStepInputBasicVO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanDao;
import com.yhl.scp.mps.plan.service.MasterPlanAlertingService;
import com.yhl.scp.mps.plan.support.MasterPlanInventorySupport;
import com.yhl.scp.mps.plan.vo.DeliveryPlanOverviewVO;
import com.yhl.scp.mps.plan.vo.MasterPlanAlertingVO;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MasterPlanAlertingServiceImpl</code>
 * <p>
 * 主计划告警服务实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-13 14:03:29
 */
@Slf4j
@Service
public class MasterPlanAlertingServiceImpl implements MasterPlanAlertingService {


    @Resource
    private MasterPlanDao masterPlanDao;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private MasterPlanInventorySupport masterPlanInventorySupport;

    private static final String limitWeekDay = "7";
    private static final String limitMonthDay = "30";
    private static final String BLACK = "#000000";
    private static final String RED = "#FF0000";

    @Override
    public List<Map<String, String>> getDeliveryPlanUnPlanAlertingList() {
        String scenario = SystemHolder.getScenario();
        PlanningHorizonVO planningHorizon = mdsFeign.selectPlanningHorizon(scenario);
        Date planStartTime = planningHorizon.getPlanStartTime();
        String planStartTimeStr = DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3);
        // 未排程物料
        List<String> unPlanProductCodes = masterPlanDao.getUnPlanDeliveryPlan(planStartTimeStr);
        if (CollectionUtils.isEmpty(unPlanProductCodes)) {
            return Collections.emptyList();
        }

        // 查询成品编码下挂的关键工序，没有关键工序则不提醒
        List<MasterPlanAlertingVO> keyStandardStepByProductCode = masterPlanDao.getKeyStandardStepByProductCode(unPlanProductCodes);
        List<String> sourceProductCodes = keyStandardStepByProductCode.stream().map(MasterPlanAlertingVO::getSourceProductCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        List<String> collect = unPlanProductCodes.stream()
                .filter(t -> !sourceProductCodes.contains(t)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            log.info("{}没有关键工序，跳过通知", collect);
        }
        unPlanProductCodes = unPlanProductCodes.stream().filter(sourceProductCodes::contains).collect(Collectors.toList());


        if (CollectionUtils.isEmpty(unPlanProductCodes)) {
            return Collections.emptyList();
        }

        Date weekDay = DateUtils.moveDay(planStartTime, 7);
        Date monthDay = DateUtils.moveDay(planStartTime, 30);
        String weekDayStr = DateUtils.dateToString(weekDay, DateUtils.COMMON_DATE_STR3);
        String monthDayStr = DateUtils.dateToString(monthDay, DateUtils.COMMON_DATE_STR3);

        Map<String, Object> params = new HashMap<>();
        params.put("productCodes", unPlanProductCodes);
        params.put("startDateTime", planStartTimeStr);
        params.put("endDateTime", weekDayStr);
        List<DeliveryPlanOverviewVO> weekDeliveryPlanVOS = masterPlanDao.selectDeliveryByCondition(params);

        // sql里分组聚合过数量了
        Map<String, BigDecimal> weekMap = weekDeliveryPlanVOS.stream()
                .collect(Collectors.toMap(DeliveryPlanOverviewVO::getProductCode, DeliveryPlanOverviewVO::getDemandQuantity));

        params.put("endDateTime", monthDayStr);
        List<DeliveryPlanOverviewVO> monthDeliveryPlanVOS = masterPlanDao.selectDeliveryByCondition(params);
        Map<String, BigDecimal> monthMap = monthDeliveryPlanVOS.stream()
                .collect(Collectors.toMap(DeliveryPlanOverviewVO::getProductCode, DeliveryPlanOverviewVO::getDemandQuantity));



        // 库存点数据
        List<NewStockPointVO> newStockPoints = masterPlanInventorySupport.getStockPoint();
        List<String> bcStockPointList = newStockPoints.stream().map(NewStockPointVO::getStockPointCode)
                .collect(Collectors.toList());
        // 成品-半品对应关系
        Map<String, List<BomRoutingStepInputVO>> semiBomMap = masterPlanInventorySupport.getSemiBomMap(unPlanProductCodes);
        // 库存数据
        List<InventoryBatchDetailVO> inventoryBatchDetail = masterPlanInventorySupport.getInventoryBatchDetail(bcStockPointList, unPlanProductCodes);
        // 成品库存
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = masterPlanInventorySupport.getFinishInventoryMap(inventoryBatchDetail, newStockPoints);
        // 工序在制量
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = masterPlanInventorySupport.getOperationInventoryMap(inventoryBatchDetail, newStockPoints);
        // 半品库存
        Map<String, List<InventoryBatchDetailVO>> semiFinishInventoryMap = masterPlanInventorySupport.getSemiFinishInventoryMap(inventoryBatchDetail, newStockPoints);
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = masterPlanInventorySupport.getSubInventoryCargoLocationMap(inventoryBatchDetail);

        Map<String, String> stepMap = masterPlanInventorySupport.getStepMap();

        String specialStockPoint = masterPlanInventorySupport.getSpecialStockPoint();
        String specialStockPoint2 = masterPlanInventorySupport.getSpecialStockPoint2();
        List<String> semiProductCodes = semiBomMap.values().stream().flatMap(Collection::stream)
                .map(BomRoutingStepInputBasicVO::getProductCode).distinct().collect(Collectors.toList());

        List<String> allProductCodes = new ArrayList<>(unPlanProductCodes);
        allProductCodes.addAll(semiProductCodes);
        // 发货计划对应物料
        List<NewProductStockPointVO> deliveryProductStockPoints = mdsFeign.selectByProductCode(scenario, allProductCodes);

        // 生产组织
        List<String> productOrganizations = newStockPoints.stream().filter(e ->
                StringUtils.isNotEmpty(e.getOrganizeType()) &&
                        StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        // 生产组织对应物料
        Map<String, NewProductStockPointVO> productionProductCodeMap = deliveryProductStockPoints.stream()
                .filter(x -> productOrganizations.contains(x.getStockPointCode()))
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(),
                        (v1, v2) -> v1));


        List<Map<String, String>> messageList = new ArrayList<>();
        for (String unPlanProductCode : unPlanProductCodes) {

            NewProductStockPointVO productItem = productionProductCodeMap.get(unPlanProductCode);
            if (productItem == null) {
                log.info("{}没有物料信息信息", unPlanProductCode);
                continue;
            }
            // 成型后工序在制量
            String semiInventory = masterPlanInventorySupport.getSemiInventory(unPlanProductCode, semiBomMap, productionProductCodeMap, semiFinishInventoryMap, cargoLocationMap,
                    stepMap, operationInventoryMap, specialStockPoint, specialStockPoint2, productItem);
            // 成品库存
            BigDecimal finishedInventory = MasterPlanServiceImpl.getFinishInventory(finishInventoryMap.get(unPlanProductCode), cargoLocationMap)
                    .stream().map(t -> new BigDecimal(t.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);

            int weekQty = weekMap.getOrDefault(unPlanProductCode, BigDecimal.ZERO).intValue();
            int monthQty = monthMap.getOrDefault(unPlanProductCode, BigDecimal.ZERO).intValue();
            BigDecimal add = new BigDecimal(semiInventory).add(finishedInventory);
            if (add.intValue() >= monthQty) {
                log.info("{}的库存已经满足月需求，无需通知", unPlanProductCode);
                continue;
            }
            if (weekQty <= 0 && monthQty <= 0) {
                log.info("{}没有需求，无需通知", unPlanProductCode);
                continue;
            }
            Map<String, String> messageMap = new HashMap<>();
            String content = unPlanProductCode + "产品没有排产！请注意！";
            if (weekQty > 0) {
                messageMap.put(RED, content);
            } else {
                messageMap.put(BLACK, content);
            }
            messageList.add(messageMap);
        }
        return messageList;
    }
}
