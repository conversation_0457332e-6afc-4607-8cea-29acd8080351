package com.yhl.scp.mps.domain.algorithm;

import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.algorithm.schedule.output.RzzCalculateTimeParamDTO;
import com.yhl.scp.mps.algorithm.schedule.output.RzzOperationDTO;
import com.yhl.scp.sds.extension.feedback.dto.FeedbackProductionDTO;
import com.yhl.scp.sds.extension.order.dto.OperationDTO;
import com.yhl.scp.sds.extension.order.dto.OperationExtendDTO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;

import java.util.List;
import java.util.Map;

/**
 * <code>CalculateDemandOrSupplyTime</code>
 * <p>
 * 抽取了涉及AMS算法相关的公用逻辑
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-17 11:30:22
 */
public interface RzzAMSSupportService {

    /**
     * 计算需求时间此方法通过分析需求、库存点物品、制造订单、工序、工序输入和计划期间信息，计算出供应所需的时间
     *
     * @param calculateTimeParamDTO
     */
    List<DemandVO> calculateDemandTime(RzzCalculateTimeParamDTO calculateTimeParamDTO);

    /**
     * 计算供应时间此方法通过分析供应、库存点物品、制造订单、工序、工序输出和计划期间信息，计算出供应所需的时间
     *
     * @param calculateTimeParamDTO
     */
    List<SupplyVO> calculateSupplyTime(RzzCalculateTimeParamDTO calculateTimeParamDTO);

    /**
     * 更新demand
     *
     * @param list
     */
    void doDemandUpdateBatchSelective(List<DemandVO> list);

    /**
     * 更新supply
     *
     * @param list
     */
    void doSupplyUpdateBatchSelective(List<SupplyVO> list);

    /**
     * demand和supply计算通用逻辑
     *
     * @param workOrderIdMaps
     * @param operationDTOMapOfId
     * @param parentOperationId
     * @param planningHorizon
     */
    void calculateCommon(Map<String, WorkOrderVO> workOrderIdMaps, Map<String, RzzOperationDTO> operationDTOMapOfId,
                         List<String> parentOperationId, PlanningHorizonVO planningHorizon);

    /**
     * 将operationVO转成RzzOperationDTO
     *
     * @param voList
     * @return
     */
    Map<String, RzzOperationDTO> operationVOToRzzDto(List<OperationVO> voList);

    /**
     * 更新operation数据，同时更新对应扩展表数据
     *
     * @param list
     */
    void doOperationUpdateBatch(List<OperationDTO> list);

    /**
     * 生成operationExtend数据
     *
     * @param operationDTO
     * @param operationExtendDTO
     */
    void operationDtoSplit(OperationDTO operationDTO, OperationExtendDTO operationExtendDTO);

    /**
     * 根据报工数据更新工序和制造订单状态
     *
     * @param list
     */
    void doBatchPlanFeedBackProductionProcess(List<FeedbackProductionDTO> list);


    /**
     * 同产线除最晚的报工数据进入生产反馈算法外其余之前工序全部更新为已完工
     * @param list
     */
    List<FeedbackProductionDTO> doUpdateOtherOperationStatus(PlanningHorizonVO planningHorizon, List<FeedbackProductionDTO> list);
}
