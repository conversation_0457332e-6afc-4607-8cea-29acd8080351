package com.yhl.scp.mps.adjust.support;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.*;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.basic.enums.SubTaskTypeEnum;
import com.yhl.scp.mds.basic.resource.vo.PhysicalResourceBasicVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepInputDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepOutputDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepResourceDO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mps.adjust.dao.AdjustDao;
import com.yhl.scp.mps.adjust.model.AdjustPlanContext;
import com.yhl.scp.mps.adjust.po.PublishQtyCheckResultPO;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.domain.dispatch.support.BaseScheduleSupport;
import com.yhl.scp.mps.domain.sync.process.AbstractOperationInputSync;
import com.yhl.scp.mps.enums.PlanStatusEnum;
import com.yhl.scp.mps.enums.PublishApprovalStatusEnum;
import com.yhl.scp.mps.plan.dto.MasterPlanSplitResultDTO;
import com.yhl.scp.mps.plan.dto.MasterPlanWorkOrderBodyDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.DeliveryPlanOverviewDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.service.MasterPlanService;
import com.yhl.scp.mps.plan.vo.DeliveryPlanGeneralViewDetailVO;
import com.yhl.scp.mps.plan.vo.DeliveryPlanOverviewDetailVO;
import com.yhl.scp.mps.plan.vo.DeliveryPlanOverviewVO;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.published.infrastructure.dao.MasterPlanPublishedApprovalDao;
import com.yhl.scp.mps.published.infrastructure.po.MasterPlanPublishedApprovalPO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.basic.order.infrastructure.po.WorkOrderBasicPO;
import com.yhl.scp.sds.basic.order.vo.OperationBasicVO;
import com.yhl.scp.sds.basic.order.vo.WorkOrderBasicVO;
import com.yhl.scp.sds.basic.pegging.enums.DemandTypeEnum;
import com.yhl.scp.sds.extension.order.infrastructure.po.*;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.infrastructure.po.DemandPO;
import com.yhl.scp.sds.order.infrastructure.dao.OperationDao;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>AdjustPlanSupport</code>
 * <p>
 * AdjustPlanSupport
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/2/12 13:41
 */
@Slf4j
@Component
public class AdjustPlanSupport {
    @Resource
    protected NewMdsFeign mdsFeign;
    @Resource
    protected OperationDao operationDao;
    @Resource
    protected DemandDao demandDao;
    @Resource
    protected OperationTaskExtDao operationTaskExtDao;
    @Resource
    protected DfpFeign dfpFeign;
    @Resource
    protected ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    @Resource
    protected IpsFeign ipsFeign;
    @Resource
    private DeliveryPlanOverviewDao deliveryPlanOverviewDao;
    @Resource
    private MasterPlanService masterPlanService;
    @Resource
    private AdjustDao adjustDao;
    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    @Resource
    private MasterPlanPublishedApprovalDao masterPlanPublishedApprovalDao;
    @Resource
    private WorkOrderDao workOrderDao;
    @Resource
    private IpsNewFeign ipsNewFeign;

    /**
     * 算法调整使用
     *
     * @param rzzAdjustmentParam
     * @return
     */
    public AdjustPlanContext initAdjustPlanContextResource(RzzAdjustmentParam rzzAdjustmentParam) {
        AdjustPlanContext adjustPlanContext = new AdjustPlanContext();
        adjustPlanContext.setAdjustmentParam(rzzAdjustmentParam);
        // 资源
        setResourceMap(rzzAdjustmentParam, adjustPlanContext);
        // 被调整工序
        setOperationAndTaskResource(rzzAdjustmentParam, adjustPlanContext);
        return adjustPlanContext;
    }

    private void setOperationAndTaskResource(RzzAdjustmentParam rzzAdjustmentParam, AdjustPlanContext adjustPlanContext) {
        // 被调整工序
        adjustPlanContext.setAdjustOperation(operationDao.selectByPrimaryKey(rzzAdjustmentParam.getOperationId()));
    }


    private void setResourceMap(RzzAdjustmentParam rzzAdjustmentParam, AdjustPlanContext adjustPlanContext) {
        List<String> resourceIds = new ArrayList<>();
        if (null != rzzAdjustmentParam.getSourceResourceId()) {
            adjustPlanContext.setSourceResourceId(rzzAdjustmentParam.getSourceResourceId());
            resourceIds.add(rzzAdjustmentParam.getSourceResourceId());
        }
        if (null != rzzAdjustmentParam.getTargetResourceId()) {
            adjustPlanContext.setTargetResourceId(rzzAdjustmentParam.getTargetResourceId());
            resourceIds.add(rzzAdjustmentParam.getTargetResourceId());
        }
        resourceIds = operationTaskExtDao.selectPhysicalResourceByOperationTask();

        if (CollectionUtils.isNotEmpty(resourceIds)) {
            List<PhysicalResourceVO> physicalResourceVOS = mdsFeign.selectByPhysicalIds(SystemHolder.getScenario(), resourceIds);

            adjustPlanContext.setPhysicalResourceMap(physicalResourceVOS.stream()
                    .collect(Collectors.toMap(PhysicalResourceVO::getId, Function.identity())));
            List<String> standardResourceIds = physicalResourceVOS.stream()
                    .map(PhysicalResourceBasicVO::getStandardResourceId).distinct().collect(Collectors.toList());
            List<StandardResourceVO> standardResourceVOS = mdsFeign.selectStandardResourceVOSByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", standardResourceIds));
            Map<String, StandardResourceVO> standardResourceVOMap = standardResourceVOS.stream().collect(Collectors.toMap(StandardResourceVO::getId, Function.identity()));
            adjustPlanContext.setStandardResourceVOMap(standardResourceVOMap);
        }
    }

    public static boolean checkHWOperation(RzzAdjustmentParam rzzAdjustmentParam, AdjustPlanContext adjustPlanContext) {
        OperationPO adjustOperation = adjustPlanContext.getAdjustOperation();
        PhysicalResourceVO physicalResourceVO = adjustPlanContext.getPhysicalResourceMap().get(rzzAdjustmentParam.getTargetResourceId());
        StandardResourceVO standardResourceVO = adjustPlanContext.getStandardResourceVOMap().get(physicalResourceVO.getStandardResourceId());
        if (PlanStatusEnum.UNPLAN.getCode().equals(adjustOperation.getPlanStatus())) {
            return false;
        }else {
            // 调整工序是已计划的则判断是否是连续炉之间的调整
            PhysicalResourceVO sourcePhysicalResourceVO = adjustPlanContext.getPhysicalResourceMap().get(rzzAdjustmentParam.getSourceResourceId());
            StandardResourceVO sourceStandardResourceVO = adjustPlanContext.getStandardResourceVOMap().get(sourcePhysicalResourceVO.getStandardResourceId());
            return "S1HW".equals(standardResourceVO.getStandardResourceCode()) && "S1HW".equals(sourceStandardResourceVO.getStandardResourceCode());
        }
    }

    /**
     * 当存在10输出，正好是20的输入，需要重新计算10输入物品的单位输入量
     *
     * @param i
     * @param workOrderOperationList
     * @param routingStepMap
     * @param operationPO
     * @param inputFactor
     * @return
     */
    public static BigDecimal specialCalculateInputQuantity(int i, List<OperationPO> workOrderOperationList, Map<String, RoutingStepDO> routingStepMap, OperationPO operationPO, BigDecimal inputFactor) {
        OperationPO nextOperationPO = workOrderOperationList.get(i + 1);
        RoutingStepDO routingStepDO = routingStepMap.get(operationPO.getRoutingStepId());
        List<RoutingStepOutputDO> routingStepOutputDOList = routingStepDO.getRoutingStepOutputDOList();

        RoutingStepDO nextRoutingStepDO = routingStepMap.get(nextOperationPO.getRoutingStepId());
        List<RoutingStepInputDO> nextRoutingStepDOList = nextRoutingStepDO.getRoutingStepInputDOList();
        Map<String, RoutingStepInputDO> nextRoutingStepInputDOMap = StreamUtils.mapByColumn(nextRoutingStepDOList, RoutingStepInputDO::getInputProductId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(routingStepOutputDOList)) {
            RoutingStepOutputDO routingStepOutputDO = routingStepOutputDOList.get(0);
            String outputProductId = routingStepOutputDO.getOutputProductId();
            if (nextRoutingStepInputDOMap.containsKey(outputProductId)) {
                inputFactor = nextRoutingStepInputDOMap.get(outputProductId).getInputFactor();
            }
        }
        return inputFactor;
    }

    public static void updateTask(OperationPO parentOperation,
                                  Map<String, RoutingStepDO> routingStepMap,
                                  Map<String, List<OperationTaskPO>> operationTaskGroup,
                                  OperationPO subOperation,
                                  Map<String, OperationExtendPO> operationExtendMap,
                                  MasterPlanSplitResultDTO masterPlanSplitResultDTO) {
        List<RoutingStepResourceDO> routingStepResourceDOList = routingStepMap.get(parentOperation.getRoutingStepId()).getRoutingStepResourceDOList();


        OperationExtendPO subOperationExtendPO = operationExtendMap.get(subOperation.getId());
        setOperationExtendLastInfo(subOperationExtendPO);


        Map<String, RoutingStepResourceDO> routingStepResourceMap = routingStepResourceDOList.stream()
                .collect(Collectors.toMap(RoutingStepResourceDO::getPhysicalResourceId, Function.identity(), (v1, v2) -> v1));
        RoutingStepResourceDO routingStepResourceDO = routingStepResourceMap.get(subOperation.getPlannedResourceId());
        BigDecimal processingTime = subOperation.getQuantity()
                .multiply(routingStepResourceDO.getUnitProductionTime());
        Date oldProductionEndTime = subOperationExtendPO.getProductionEndTime();
        subOperationExtendPO.setProductionEndTime(DateUtils.moveCalendar(subOperationExtendPO.getProductionStartTime(),
                Calendar.SECOND, processingTime.intValue()));
        if (subOperationExtendPO.getCleanupStartTime() != null) {
            int cleanupDuration = DateUtils.getSecondInterval(subOperationExtendPO.getCleanupStartTime(),
                    subOperationExtendPO.getCleanupEndTime()).intValue();
            subOperationExtendPO.setCleanupStartTime(subOperationExtendPO.getProductionEndTime());
            subOperationExtendPO.setCleanupEndTime(DateUtils.moveCalendar(subOperationExtendPO.getCleanupStartTime(),
                    Calendar.SECOND, cleanupDuration));
        }

        // 子工序任务信息
        for (OperationTaskPO operationTask : operationTaskGroup.get(subOperationExtendPO.getOperationId())) {
            operationTask.setSetupStartTime(subOperationExtendPO.getSetupStartTime());
            operationTask.setSetupEndTime(subOperationExtendPO.getSetupEndTime());
            operationTask.setProductionStartTime(subOperationExtendPO.getProductionStartTime());
            operationTask.setProductionEndTime(subOperationExtendPO.getProductionEndTime());
            if (operationTask.getCleanupStartTime() != null) {
                operationTask.setCleanupStartTime(subOperationExtendPO.getCleanupStartTime());
                operationTask.setCleanupEndTime(subOperationExtendPO.getCleanupEndTime());
            }
            operationTask.setStartTime(operationTask.getSetupStartTime() != null ? operationTask.getSetupStartTime() : operationTask.getProductionStartTime());
            operationTask.setEndTime(operationTask.getCleanupEndTime() != null ? operationTask.getCleanupEndTime() : operationTask.getProductionEndTime());
        }

        // 子工序
        Date startTime = null;
        Date endTime = null;

        for (int i = 0; i < operationTaskGroup.get(subOperationExtendPO.getOperationId()).size(); i++) {
            OperationTaskPO operationTaskPO = operationTaskGroup.get(subOperationExtendPO.getOperationId()).get(i);
            if (i == 0) {
                startTime = operationTaskPO.getStartTime();
                endTime = operationTaskPO.getEndTime();
            } else {
                if (startTime != null && operationTaskPO.getStartTime().before(startTime)) {
                    startTime = operationTaskPO.getStartTime();
                }
                if (endTime != null && operationTaskPO.getEndTime().after(endTime)) {
                    endTime = operationTaskPO.getEndTime();
                }
            }
        }
        subOperation.setStartTime(startTime);
        subOperation.setEndTime(endTime);
        // 生成拆分数据
        generateSplitData(subOperation, masterPlanSplitResultDTO, subOperationExtendPO, oldProductionEndTime, routingStepResourceDO);
    }

    private static void generateSplitData(OperationPO subOperation, MasterPlanSplitResultDTO masterPlanSplitResultDTO,
                                          OperationExtendPO subOperationExtendPO, Date oldProductionEndTime, RoutingStepResourceDO routingStepResourceDO) {
        if (masterPlanSplitResultDTO != null) {
            masterPlanSplitResultDTO.getUpdateSourceOperationList().add(subOperation);
            masterPlanSplitResultDTO.getUpdateSourceOperationExtendList().add(subOperationExtendPO);

            String subOperationId = UUIDUtil.getUUID();
            OperationExtendPO extendPO = new OperationExtendPO();
            extendPO.setOperationId(subOperationId);
            extendPO.setPlannedMainResourceId(subOperationExtendPO.getPlannedMainResourceId());
            extendPO.setPlannedToolResourceId(subOperationExtendPO.getPlannedToolResourceId());
            Date newProductionStartTime = subOperationExtendPO.getProductionEndTime();
            // 这里要生成的数据它的制造开始时间是改变数量后的制造结束时间，制造结束时间就是原来的制造结束时间
            extendPO.setProductionStartTime(newProductionStartTime);
            extendPO.setProductionEndTime(oldProductionEndTime);
            masterPlanSplitResultDTO.getCreateOperationExtendList().add(extendPO);
            // 设置，和清洗暂时不赋值
            // 生成subOperation,task和subTask
            OperationPO createSubOperation = new OperationPO();
            BeanUtils.copyProperties(subOperation, createSubOperation);
            createSubOperation.setId(subOperationId);
            createSubOperation.setStartTime(newProductionStartTime);
            createSubOperation.setEndTime(oldProductionEndTime);
            masterPlanSplitResultDTO.getCreateOperationList().add(createSubOperation);

            OperationTaskPO createOperationTask = new OperationTaskPO();
            String operationTaskId = UUIDUtil.getUUID();
            createOperationTask.setId(operationTaskId);
            createOperationTask.setOperationId(subOperationId);
            createOperationTask.setStandardResourceId(routingStepResourceDO.getStandardResourceId());
            createOperationTask.setPhysicalResourceId(subOperation.getPlannedResourceId());
            createOperationTask.setStartTime(newProductionStartTime);
            createOperationTask.setEndTime(oldProductionEndTime);
            createOperationTask.setProductionStartTime(newProductionStartTime);
            createOperationTask.setProductionEndTime(oldProductionEndTime);
            masterPlanSplitResultDTO.getCreateOperationTaskList().add(createOperationTask);

            OperationSubTaskPO createOperationSubTask = new OperationSubTaskPO();
            String operationSubTaskId = UUIDUtil.getUUID();
            createOperationSubTask.setId(operationSubTaskId);
            createOperationSubTask.setOperationId(subOperationId);
            createOperationSubTask.setTaskId(operationTaskId);
            createOperationSubTask.setPhysicalResourceId(subOperation.getPlannedResourceId());
            createOperationSubTask.setTaskType(SubTaskTypeEnum.WORK.getCode());
            createOperationSubTask.setStartTime(newProductionStartTime);
            createOperationSubTask.setEndTime(oldProductionEndTime);
            masterPlanSplitResultDTO.getCreateOperationSubTaskList().add(createOperationSubTask);

            masterPlanSplitResultDTO.getInsertOperationIds().add(subOperationId);
            masterPlanSplitResultDTO.getDeleteOperationIds().add(subOperation.getId());
        }
    }

    public static void setOperationExtendLastInfo(OperationExtendPO subOperationExtendPO) {
        subOperationExtendPO.setLastSetupStartTime(subOperationExtendPO.getSetupStartTime());
        subOperationExtendPO.setLastSetupEndTime(subOperationExtendPO.getSetupEndTime());
        subOperationExtendPO.setLastProductionStartTime(subOperationExtendPO.getProductionStartTime());
        subOperationExtendPO.setLastProductionEndTime(subOperationExtendPO.getProductionEndTime());
        subOperationExtendPO.setLastCleanupStartTime(subOperationExtendPO.getCleanupStartTime());
        subOperationExtendPO.setLastCleanupEndTime(subOperationExtendPO.getCleanupEndTime());
        subOperationExtendPO.setLastMainResourceId(subOperationExtendPO.getPlannedMainResourceId());
        subOperationExtendPO.setLastToolResourceId(subOperationExtendPO.getPlannedToolResourceId());
    }

    public static void updateSetParentOperationAndExtend(OperationPO parentOperation, List<OperationPO> subOperationList, Map<String, OperationExtendPO> operationExtendMap) {
        List<OperationExtendPO> subOperationExtendList = new ArrayList<>();
        subOperationList.forEach(subOperation -> subOperationExtendList.add(operationExtendMap.get(subOperation.getId())));
        subOperationExtendList.sort(Comparator.comparing(OperationExtendPO::getProductionStartTime));

        OperationExtendPO parentOperationExtend = operationExtendMap.get(parentOperation.getId());
        AdjustPlanSupport.setOperationExtendLastInfo(parentOperationExtend);
        parentOperationExtend.setSetupStartTime(subOperationExtendList.get(0).getSetupStartTime());
        parentOperationExtend.setSetupEndTime(subOperationExtendList.get(subOperationExtendList.size() - 1).getSetupEndTime());
        parentOperationExtend.setProductionStartTime(subOperationExtendList.get(0).getProductionStartTime());
        parentOperationExtend.setProductionEndTime(subOperationExtendList.get(subOperationExtendList.size() - 1).getProductionEndTime());
        parentOperationExtend.setCleanupStartTime(subOperationExtendList.get(0).getCleanupStartTime());
        parentOperationExtend.setCleanupEndTime(subOperationExtendList.get(subOperationExtendList.size() - 1).getCleanupEndTime());


        subOperationList.sort(Comparator.comparing(OperationPO::getStartTime));
        parentOperation.setStartTime(subOperationList.get(0).getStartTime());
        parentOperation.setEndTime(subOperationList.get(subOperationList.size() - 1).getEndTime());
    }


    public static void updateOperationInputAndDemand(OperationPO t,
                                                     Map<String, RoutingStepDO> routingStepMap,
                                                     Map<String, List<OperationInputPO>> operationInputGroup,
                                                     Map<String, NewProductStockPointVO> productStockPointMap,
                                                     WorkOrderPO topWorkOrder,
                                                     Map<String, List<ProductSubstitutionRelationshipVO>> replaceMap,
                                                     Date dueDate,
                                                     Map<String, List<DemandPO>> demandMap,
                                                     List<DemandPO> semiDemandList,
                                                     BigDecimal inputFactor,
                                                     int i) {
        RoutingStepDO routingStepDO = routingStepMap.get(t.getRoutingStepId());
        List<OperationInputPO> operationInputPOS = operationInputGroup.get(t.getId());
        if (CollectionUtils.isNotEmpty(operationInputPOS)) {
            Map<String, OperationInputPO> operationInputMap = operationInputPOS.stream()
                    .filter(operationInput -> YesOrNoEnum.YES.getCode().equals(operationInput.getEnabled())).collect(
                            Collectors.toMap(OperationInputPO::getProductId, Function.identity(), (t1, t2) -> t1));
            if (CollectionUtils.isNotEmpty(routingStepDO.getRoutingStepInputDOList())) {
                for (RoutingStepInputDO routingStepInputDO : routingStepDO.getRoutingStepInputDOList()) {
                    // 计算主料输入数量
                    BigDecimal masterProductInputQuantity = AbstractOperationInputSync.calculateInputQuantity(t.getQuantity(), routingStepInputDO);
                    // 成品物料
                    NewProductStockPointVO finishProductStockPoint = productStockPointMap.get(topWorkOrder.getProductId());
                    // 主料
                    NewProductStockPointVO mainProductStockPoint = productStockPointMap.get(routingStepInputDO.getInputProductId());

                    // 获取替代关系
                    String key = String.join("&&", finishProductStockPoint.getProductCode(), mainProductStockPoint.getProductCode());
                    if (CollectionUtils.isNotEmpty(replaceMap.get(key))) {
                        List<ProductSubstitutionRelationshipVO> collect = replaceMap.get(key).stream()
                                .filter(item -> null != item.getEffectiveTime() && item.getEffectiveTime().before(dueDate) && null != item.getFailureTime() && item.getFailureTime().after(dueDate))
                                .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect)) {
                            ProductSubstitutionRelationshipVO productSubstitutionRelationshipVO = collect.get(0);
                            BigDecimal substituteQuantity = masterProductInputQuantity.multiply(productSubstitutionRelationshipVO.getUnitSubstitute());
                            if (i == 0) {
                                // 10,20之间如果10存在输出，正好是20的输入，需要特殊逻辑处理
                                substituteQuantity = BigDecimalUtils.multiply(substituteQuantity, inputFactor, 4);
                            }
                            operationInputMap.get(productSubstitutionRelationshipVO.getSubstituteProductId()).setInputQuantity(substituteQuantity);
                            List<DemandPO> demandPOList = demandMap.get(operationInputMap.get(productSubstitutionRelationshipVO.getSubstituteProductId()).getId());
                            if (CollectionUtils.isNotEmpty(demandPOList)) {
                                updateDemand(t, dueDate, substituteQuantity, demandPOList);
                            }
                        }
                    } else {
                        if (Objects.isNull(operationInputMap.get(mainProductStockPoint.getId()))) continue;
                        if (i == 0) {
                            // 10,20之间如果10存在输出，正好是20的输入，需要特殊逻辑处理
                            masterProductInputQuantity = BigDecimalUtils.multiply(masterProductInputQuantity, inputFactor, 4);
                        }
                        operationInputMap.get(mainProductStockPoint.getId()).setInputQuantity(masterProductInputQuantity);
                        List<DemandPO> demandPOList = demandMap.get(operationInputMap.get(mainProductStockPoint.getId()).getId());
                        if (CollectionUtils.isNotEmpty(demandPOList)) {
                            if ("制造".equals(mainProductStockPoint.getSupplyType())) {
                                List<DemandPO> collect = demandPOList.stream().filter(p -> p.getDemandType().equals(DemandTypeEnum.CUSTOMER_ORDER_DEMAND.getCode())).collect(Collectors.toList());
                                if(CollectionUtils.isNotEmpty(collect)){
                                    // 半品需求更新demand，忽略包装工序demand 1对多
                                    updateDemand(t, dueDate, masterProductInputQuantity, collect);
                                    semiDemandList.add(collect.get(0));
                                }
                            }else{
                                // 采购更新demand
                                updateDemand(t, dueDate, masterProductInputQuantity, demandPOList);
                            }
                        }
                    }
                }
            }
        }
    }


    private static void updateDemand(OperationPO t, Date dueDate, BigDecimal substituteQuantity, List<DemandPO> demandPOList) {
        if (demandPOList.size() == 1) {
            demandPOList.get(0).setQuantity(substituteQuantity);
            if (t.getStartTime() != null) {
                demandPOList.get(0).setDemandTime(t.getStartTime());
            } else {
                demandPOList.get(0).setDemandTime(dueDate);
            }
            demandPOList.get(0).setUnfulfilledQuantity(substituteQuantity);
        }
    }

    // 数量调整校验
    public void adjustQtyCheck(AdjustPlanContext adjustPlanContext) {
        BigDecimal qty = adjustPlanContext.getAdjustmentParam().getQty();
        OperationPO adjustOperation = adjustPlanContext.getAdjustOperation();
        Date endTime = adjustOperation.getEndTime();
        List<NewProductStockPointVO> stockPointVOS = mdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(), Collections.singletonList(adjustPlanContext.getAdjustOperation().getProductId()));
        if (CollectionUtils.isEmpty(stockPointVOS)) {
            throw new BusinessException("被调整产品编码不存在");
        }
        NewProductStockPointVO productStockPointVO = stockPointVOS.get(0);
        String productCode = productStockPointVO.getProductCode();

        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS = productAdvanceBatchRuleService.selectAll();
        Map<String, ProductAdvanceBatchRuleVO> batchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getRiskLevel()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(null, Collections.singletonList(productCode));
        Map<String, PartRiskLevelVO> riskLevelVOMap = partRiskLevelVOS.stream()
                .collect(Collectors.toMap(PartRiskLevelVO::getProductCode, v -> v, (k1, k2) -> k1));

        String low = "低";

        PartRiskLevelVO partRiskLevelVO = riskLevelVOMap.get(productStockPointVO.getProductCode());
        ProductAdvanceBatchRuleVO advanceBatchRuleVO;
        if (partRiskLevelVO == null) {
            advanceBatchRuleVO = batchRuleVOMap.get(low);
        } else {
            advanceBatchRuleVO = batchRuleVOMap.get(partRiskLevelVO.getMaterialRiskLevel());
        }
        if (advanceBatchRuleVO == null) return;
        int value = advanceBatchRuleVO.getMaximumNumberDays().intValue();
        // 对应发货计划
        List<String> productCodes = new ArrayList<>();
        productCodes.add(productCode);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("productCodes", productCodes);
        String startTimeStr = DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR3);
        queryParams.put("startDateTime", startTimeStr);
        Date date = DateUtils.moveDay(endTime, value);
        String endTimeStr = DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3);
        queryParams.put("endDateTime", endTimeStr);
        List<DeliveryPlanOverviewDetailVO> detailList = deliveryPlanOverviewDao.selectDetailList(queryParams);
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        detailList = detailList.stream()
                .sorted(Comparator.comparing(DeliveryPlanOverviewDetailVO::getDemandDate)).collect(Collectors.toList());
        BigDecimal sum = detailList.stream().map(DeliveryPlanOverviewDetailVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimalUtils::add);
        if (qty.compareTo(sum) > 0) {
            throw new BusinessException("调整数量不能大于最大合批量" + sum.intValue() + ", 最大合批时间范围为" + startTimeStr + "-" + endTimeStr);
        }
    }

    public String adjustQtyCheck(String productCode, String startTimeStr, BigDecimal quantity, BigDecimal oldQuantity) {
        String result = "校验通过";
        if (StringUtils.isEmpty(productCode)) {
            return result;
        }
        if (startTimeStr == null) {
            return result;
        }
        if (quantity == null) {
            return result;
        }
        if (oldQuantity == null) {
            oldQuantity = BigDecimal.ZERO;
        }
        String vehicleModelCode = operationTaskExtDao.selectVehicleModelCode(productCode);
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS = productAdvanceBatchRuleService.selectAll();
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(null, Collections.singletonList(productCode));

        // 根据不同维度分组提前生产批次规则（物料编码，车型，风险等级）
        Map<String, ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getProductCode()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductCode, v -> v, (k1, k2) -> k1));
        Map<String, ProductAdvanceBatchRuleVO> productTypeAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getProductType()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getProductType, v -> v, (k1, k2) -> k1));
        Map<String, ProductAdvanceBatchRuleVO> riskLevelAdvanceBatchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getRiskLevel()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));
        Map<String, PartRiskLevelVO> partRiskLevelVOMap = partRiskLevelVOS.stream()
                .collect(Collectors.toMap(PartRiskLevelVO::getProductCode, v -> v, (k1, k2) -> k1));

        BigDecimal maximumNumberDays = BaseScheduleSupport.getMaximumNumberDays(
                productCode, vehicleModelCode,
                productAdvanceBatchRuleVOMap,
                productTypeAdvanceBatchRuleVOMap,
                partRiskLevelVOMap,
                riskLevelAdvanceBatchRuleVOMap,
                false);
        if (maximumNumberDays == null) return result;
        // 最大合批天数
        int value = maximumNumberDays.intValue();


        Date startTime = DateUtils.stringToDate(startTimeStr, DateUtils.COMMON_DATE_STR1);
        String startDayStr = DateUtils.dateToString(startTime, DateUtils.COMMON_DATE_STR3);
        Date startDay = DateUtils.stringToDate(startDayStr, DateUtils.COMMON_DATE_STR3);
        Date endDay = DateUtils.moveDay(startDay, value);
        String endDayStr = DateUtils.dateToString(endDay, DateUtils.COMMON_DATE_STR3);
        MasterPlanReq masterPlanReq = new MasterPlanReq();
        String scenario = SystemHolder.getScenario();
        PlanningHorizonVO planningHorizon = mdsFeign.selectPlanningHorizon(scenario);
        Date historyRetrospectStartTime = planningHorizon.getHistoryRetrospectStartTime();
        masterPlanReq.setHistoryRetrospectStartTime(historyRetrospectStartTime);
        masterPlanReq.setProductCode(productCode);

        Map<String, Object> params = new HashMap<>();
        params.put("productCodes", Collections.singletonList(productCode));
        params.put("startDateTime", planningHorizon.getPlanStartTime());
        params.put("endDateTime", planningHorizon.getPlanEndTime());
        params.put("userId", SystemHolder.getUserId());
        List<DeliveryPlanOverviewVO> deliveryPlanOverviews = deliveryPlanOverviewDao.selectByCondition(params);
        List<DeliveryPlanOverviewDetailVO> detailList = deliveryPlanOverviewDao.selectDetailList(params);
        masterPlanService.assembleDeliveryPlanOverviews(deliveryPlanOverviews, detailList, masterPlanReq, planningHorizon, scenario);
        if (CollectionUtils.isEmpty(deliveryPlanOverviews)) {
            result = startDayStr + "~" + endDayStr + "时间段内剩余可新增数量为0, 请确认是否新增";
            return result;
        }
        DeliveryPlanOverviewVO deliveryPlanOverviewVO = deliveryPlanOverviews.get(0);
        List<DeliveryPlanGeneralViewDetailVO> viewDetailVOS = deliveryPlanOverviewVO.getDetailList().stream()
                .filter(t -> !startDay.after(t.getDemandTime())
                        && !endDay.before(t.getDemandTime()))
                .collect(Collectors.toList());
        viewDetailVOS = viewDetailVOS.stream()
                .sorted(Comparator.comparing(DeliveryPlanGeneralViewDetailVO::getDemandTime)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(viewDetailVOS)) {
            result = startDayStr + "~" + endDayStr + "时间段内剩余可新增数量为0, 请确认是否新增";
            return result;
        }
        int demandQuantity = viewDetailVOS.stream().mapToInt(DeliveryPlanGeneralViewDetailVO::getDemandQuantity).sum();
        int supplyQuantity = viewDetailVOS.stream().mapToInt(DeliveryPlanGeneralViewDetailVO::getSupplyQuantity).sum();
        int subtract = demandQuantity - supplyQuantity + oldQuantity.intValue();
        if (quantity.intValue() > subtract) {
            result = startDayStr + "~" + endDayStr + "时间段内剩余可新增数量为" + subtract + ", 请确认是否新增";
        }
        System.out.println(viewDetailVOS.size());
        return result;
    }

    /**
     * 下发时数量校验
     */
    public List<PublishQtyCheckResultPO> publishQtyCheck(List<MasterPlanWorkOrderBodyDTO> workOrderList) {
        List<String> errorMsgList = new ArrayList<>();
        String scenario = SystemHolder.getScenario();
        PlanningHorizonVO planningHorizon = mdsFeign.selectPlanningHorizon(scenario);
        Date planStartTime = planningHorizon.getPlanStartTime();
        Date planLockEndTime = planningHorizon.getPlanLockEndTime();
        List<PublishQtyCheckResultPO> resultPOList = new ArrayList<>();
        List<WorkOrderVO> workOrders;
        List<WorkOrderPO> curWorkOrderPOS;
        // 不是勾选发布，则校验所有锁定期内的工单
        if (CollectionUtils.isEmpty(workOrderList)) {
            workOrders = adjustDao.selectUnPublishWorkOrder();
            curWorkOrderPOS = new ArrayList<>();
        } else {
            List<String> workOrderIds = workOrderList.stream().map(MasterPlanWorkOrderBodyDTO::getWorkOrderId).collect(Collectors.toList());
            workOrders = adjustDao.selectTopWorkOrderByIds(workOrderIds);
            // 当前勾选的数据
            curWorkOrderPOS = workOrderDao.selectByPrimaryKeys(workOrderIds);
        }
        // 针对半品下发时，记录此次下发数据中成品对应半品
        Map<String, WorkOrderPO> subOrderIdMap = curWorkOrderPOS.stream().filter(t -> StringUtils.isNotEmpty(t.getTopOrderId()))
                .collect(Collectors.toMap(WorkOrderBasicPO::getParentId, Function.identity()));
        if (CollectionUtils.isEmpty(workOrders)) {
            throw new BusinessException("没有可以下发的制造工单");
        }
        List<String> productIds = workOrders.stream().map(WorkOrderBasicVO::getProductId).distinct().collect(Collectors.toList());
        // 查对应物品
        List<NewProductStockPointVO> newProductStockPoints = mdsFeign.selectProductStockPointByIds(scenario,
                productIds);
        Map<String, NewProductStockPointVO> productStockPointVOMap = newProductStockPoints.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        if (productIds.size() != workOrderList.size()) {
            // 重复的id
            List<String> collect = workOrders.stream().collect(Collectors.groupingBy(WorkOrderVO::getProductId,
                            Collectors.counting())).entrySet()
                    .stream().filter(t -> t.getValue() > 1)
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());
            List<String> productCodeList = collect.stream().map(t -> productStockPointVOMap.get(t).getProductCode())
                    .collect(Collectors.toList());
            throw new BusinessException("工单中包含重复的物料，请检查工单，物料编码为：" + String.join(",", productCodeList));
        }
        Map<String, WorkOrderVO> workOrderMap = workOrders.stream().collect(Collectors.toMap(WorkOrderBasicVO::getProductId, Function.identity() ));
        List<String> productCodes = productIds.stream().map(t -> productStockPointVOMap.get(t).getProductCode())
                .distinct().collect(Collectors.toList());
        // 查询库存点数据，用于过滤非本厂库存
        List<NewStockPointVO> newStockPoints = mdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of("stockPointType", StockPointTypeEnum.BC.getCode()));
        // 获取本厂销售组织类型的仓库(成品库存点)
        List<String> saleOrganizations = newStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        List<String> bcStockPointList = newStockPoints.stream().map(NewStockPointVO::getStockPointCode)
                .collect(Collectors.toList());
        // 查询库存数据
        List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario,
                productCodes, StockPointTypeEnum.BC.getCode()).stream().filter(p -> bcStockPointList
                .contains(p.getStockPointCode())).collect(Collectors.toList());
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .distinct().collect(Collectors.toList());
        // 查询可用本厂有效货位库存
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = org.apache.commons.collections4.CollectionUtils.isEmpty(spaceList) ?
                new HashMap<>()
                : subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode())
                .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));

        String subInventory = getRangeData();

        // 成品库存
        List<InventoryBatchDetailVO> finishInventoryList = inventoryBatchDetails.stream()
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && subInventory.equals(t.getSubinventory()))
                .collect(Collectors.toList());

        // 查询锁定期内已下发数量
        String startTimeStr = DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1);
        String endTimeStr = DateUtils.dateToString(planLockEndTime, DateUtils.COMMON_DATE_STR1);
        // 锁定期内制造订单id
        List<String> lockWorkOrderIds = adjustDao.selectWorkOrderIdsByTime(startTimeStr, endTimeStr, productIds);
        Map<String, BigDecimal> publishQtyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(lockWorkOrderIds)) {
            List<WorkOrderVO> workOrderVOS = adjustDao.selectSumPublishWorkOrder(lockWorkOrderIds);
            // 锁定期内已下发数量
            publishQtyMap = workOrderVOS.stream()
                    .collect(Collectors.toMap(WorkOrderBasicVO::getProductId,
                            WorkOrderBasicVO::getQuantity));
        }

        // 产品-实时库存
        Map<String, BigDecimal> finishInventory = getFinishInventory(finishInventoryList, cargoLocationMap);

        // 查询最大合批天数
        List<ProductAdvanceBatchRuleVO> productAdvanceBatchRuleVOS = productAdvanceBatchRuleService.selectAll();
        Map<String, ProductAdvanceBatchRuleVO> batchRuleVOMap = productAdvanceBatchRuleVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getRiskLevel()))
                .collect(Collectors.toMap(ProductAdvanceBatchRuleVO::getRiskLevel, v -> v, (k1, k2) -> k1));
        List<PartRiskLevelVO> partRiskLevelVOS = dfpFeign.selectMaterialRiskLeveByProductCodeList(null, productCodes);
        Map<String, PartRiskLevelVO> riskLevelVOMap = partRiskLevelVOS.stream()
                .collect(Collectors.toMap(PartRiskLevelVO::getProductCode, v -> v, (k1, k2) -> k1));

        Map<String, Integer> maximumNumberDayMap = new HashMap<>();
        Map<String, String> riskLevelMap = new HashMap<>();
        String low = "低";
        for (String productId : productIds) {
            NewProductStockPointVO productStockPointVO = productStockPointVOMap.get(productId);
            String productCode = productStockPointVO.getProductCode();
            PartRiskLevelVO partRiskLevelVO = riskLevelVOMap.get(productCode);
            ProductAdvanceBatchRuleVO advanceBatchRuleVO;
            String level;
            if (partRiskLevelVO == null) {
                advanceBatchRuleVO = batchRuleVOMap.get(low);
                level = low;
            } else {
                advanceBatchRuleVO = batchRuleVOMap.get(partRiskLevelVO.getMaterialRiskLevel());
                level = partRiskLevelVO.getMaterialRiskLevel();
            }
            int value = advanceBatchRuleVO.getMaximumNumberDays().intValue();
            maximumNumberDayMap.put(productId, value);
            riskLevelMap.put(productId, level);
        }
        // 查询发货计划
        Map<String, Object> params = new HashMap<>();
        params.put("productCodes", productCodes);
        params.put("startDateTime", planningHorizon.getPlanStartTime());
        params.put("endDateTime", planningHorizon.getPlanEndTime());
        params.put("userId", SystemHolder.getUserId());
        List<DeliveryPlanOverviewDetailVO> detailList = deliveryPlanOverviewDao.selectDetailList(params);
        Map<String, List<DeliveryPlanOverviewDetailVO>> deliveryPlanMap = detailList.stream().collect(Collectors.groupingBy(DeliveryPlanOverviewDetailVO::getProductCode));
        List<String> needAdjustTopWorkOrderIds = new ArrayList<>();
        Map<String, BigDecimal> subtractMap = new HashMap<>();
        // 本次要下发的工单对应的数量
        Map<String, BigDecimal> currurWorkOrderQtyMap = new HashMap<>();
        Map<String, BigDecimal> demandWorkOrderQtyMap = new HashMap<>();
        Map<String, BigDecimal> afterWeekDemandWorkOrderQtyMap = new HashMap<>();
        Map<String, BigDecimal> afterTwoWeekDemandWorkOrderQtyMap = new HashMap<>();
        Map<String, BigDecimal> supplyWorkOrderQtyMap = new HashMap<>();
        Map<String, String> productOfWorkOrderMap = new HashMap<>();
        for (String productId : productIds) {
            PublishQtyCheckResultPO resultPO = new PublishQtyCheckResultPO();
            resultPO.setProductId(productId);
            NewProductStockPointVO productStockPointVO = productStockPointVOMap.get(productId);
            String productCode = productStockPointVO.getProductCode();
            // 产品对应实时库存
            BigDecimal inventory = finishInventory.getOrDefault(productCode, BigDecimal.ZERO);
            // 锁定期内已下发数量
            BigDecimal publishQty = publishQtyMap.getOrDefault(productId, BigDecimal.ZERO);
            // 当前要下发的数量
            WorkOrderVO workOrderVO = workOrderMap.get(productId);
            BigDecimal workOrderQty = workOrderVO == null ? BigDecimal.ZERO : workOrderVO.getQuantity();
            List<DeliveryPlanOverviewDetailVO> deliveryPlanList = deliveryPlanMap.getOrDefault(productCode, new ArrayList<>());
            if (CollectionUtils.isEmpty(deliveryPlanList)) {
                String errorMsg = "当前" + productCode + "产品没有可下发的量";
                errorMsgList.add(errorMsg);
                resultPO.setMessage(errorMsg);
                resultPOList.add(resultPO);
                continue;
            }
            deliveryPlanList = deliveryPlanList.stream()
                    .sorted(Comparator.comparing(DeliveryPlanOverviewDetailVO::getDemandDate))
                    .collect(Collectors.toList());
            // 最大合批天数
            Integer days = maximumNumberDayMap.get(productId);
            String startDay = DateUtils.dateToString(planningHorizon.getPlanStartTime(), DateUtils.COMMON_DATE_STR3);
            Date end = DateUtils.moveDay(planningHorizon.getPlanStartTime(), days - 1);
            String endDay = DateUtils.dateToString(end, DateUtils.COMMON_DATE_STR3);
            BigDecimal demandQty = getDurDemandQty(startDay, endDay, deliveryPlanList);
            Date moveWeekDay = DateUtils.moveDay(end, 7);
            String moveWeekDayStr = DateUtils.dateToString(moveWeekDay, DateUtils.COMMON_DATE_STR3);
            BigDecimal afterDemandQty = getDurDemandQty(endDay, moveWeekDayStr, deliveryPlanList);
            afterWeekDemandWorkOrderQtyMap.put(productId, afterDemandQty);

            Date moveTwoWeekDay = DateUtils.moveDay(end, 14);
            String moveTwoWeekDayStr = DateUtils.dateToString(moveTwoWeekDay, DateUtils.COMMON_DATE_STR3);
            BigDecimal afterTwoDemandQty = getDurDemandQty(endDay, moveTwoWeekDayStr, deliveryPlanList);
            afterTwoWeekDemandWorkOrderQtyMap.put(productId, afterTwoDemandQty);

            // 后一周的需求量
            BigDecimal totalQty = inventory.add(publishQty).add(workOrderQty);
            if (totalQty.compareTo(demandQty) > 0) {
                BigDecimal subtract = demandQty.subtract(inventory).subtract(publishQty);
                String errorMsg = "当前" + productCode + "下发计划数量已超最大合批限制，剩余可下发数量为:"+ subtract.intValue() + ",请确认下发数量";
                errorMsgList.add(errorMsg);
                resultPO.setMessage(errorMsg);
                if (workOrderVO != null) {
                    String orderId = workOrderVO.getId();
                    needAdjustTopWorkOrderIds.add(orderId);
                    subtractMap.put(orderId, subtract);
                    productOfWorkOrderMap.put(productId, orderId);
                    currurWorkOrderQtyMap.put(orderId, workOrderQty);
                    demandWorkOrderQtyMap.put(orderId, demandQty);
                    supplyWorkOrderQtyMap.put(orderId, inventory.add(publishQty));
                }
            }
            resultPOList.add(resultPO);

        }
        Map<String, PublishQtyCheckResultPO> resultPOMap = resultPOList
                .stream().collect(Collectors.toMap(PublishQtyCheckResultPO::getProductId, Function.identity()));
        // 构建数量调整参数
        if (CollectionUtils.isNotEmpty(needAdjustTopWorkOrderIds)) {
            List<OperationVO> operationVOS = adjustDao.selectKeyOperationByWorkOrderIds(needAdjustTopWorkOrderIds);
            Map<String, OperationVO> workOrderOfOperationMap = operationVOS.stream()
                    .collect(Collectors.toMap(OperationBasicVO::getOrderId, Function.identity()));

            List<MasterPlanPublishedApprovalPO> approvalPOS = masterPlanPublishedApprovalDao.selectByParams(
                    ImmutableMap.of("workOrderIds", needAdjustTopWorkOrderIds));
            Map<String, MasterPlanPublishedApprovalPO> approvalMap = approvalPOS.stream()
                    .collect(Collectors.toMap(MasterPlanPublishedApprovalPO::getWorkOrderId, Function.identity()));

            for (Map.Entry<String, String> entry : productOfWorkOrderMap.entrySet()) {
                String productId = entry.getKey();
                String workOrderId = entry.getValue();
                WorkOrderVO workOrderVO = workOrderMap.get(productId);
                PublishQtyCheckResultPO resultPO = resultPOMap.get(productId);
                NewProductStockPointVO productStockPointVO = productStockPointVOMap.get(productId);
                Integer maximunNumberDay = maximumNumberDayMap.get(productId);
                String riskLevel = riskLevelMap.get(productId);
                BigDecimal afterWeekDemandQty = afterWeekDemandWorkOrderQtyMap.get(productId);
                BigDecimal afterTwoWeekDemandQty = afterTwoWeekDemandWorkOrderQtyMap.get(productId);

                if (subOrderIdMap.containsKey(workOrderId)) {
                    WorkOrderPO subWorkOrder = subOrderIdMap.get(workOrderId);
                    resultPO.setProductId(subWorkOrder.getProductId());
                }
                if (!"LC".equals(workOrderVO.getOrderType())) {
                    resultPO.setMessage(null);
                    continue;
                }
                // 调整数量
                BigDecimal qty = subtractMap.get(workOrderId);
                OperationVO operation = workOrderOfOperationMap.get(workOrderId);
                if (workOrderOfOperationMap.containsKey(workOrderId) &&
                        qty.compareTo(BigDecimal.ZERO) > 0) {
                    RzzAdjustmentParam adjustmentParam = new RzzAdjustmentParam();
                    adjustmentParam.setOperationId(operation.getId());
                    adjustmentParam.setQty(qty);
                    adjustmentParam.setTargetResourceId(operation.getPlannedResourceId());
                    adjustmentParam.setSourceResourceId(operation.getPlannedResourceId());
                    resultPO.setRzzAdjustmentParam(adjustmentParam);
                }
                BigDecimal supplyQty = supplyWorkOrderQtyMap.get(workOrderId);
                BigDecimal demandQty = demandWorkOrderQtyMap.get(workOrderId);
                BigDecimal workOrderQty = currurWorkOrderQtyMap.get(workOrderId);
                // 存在审批记录
                MasterPlanPublishedApprovalPO approvalPO;
                if (approvalMap.containsKey(workOrderId)) {
                    approvalPO = approvalMap.get(workOrderId);
                    // 审批通过的下发数量
                    BigDecimal quantity = approvalPO.getQuantity();
                    // 此次要下发的数量等于审批的数量，并且审批状态是已通过
                    if (quantity.compareTo(workOrderQty) == 0 &&
                            PublishApprovalStatusEnum.PASSED.getCode().equals(approvalPO.getApprovalStatus())) {
                        // 清空掉提示信息，可以下发
                        resultPO.setMessage(null);
                    }else {
                        approvalPO.setQuantity(workOrderQty);
                        approvalPO.setDemandQuantity(demandQty);
                        approvalPO.setSupplyQuantity(supplyQty);
                        approvalPO.setApprovalStatus(PublishApprovalStatusEnum.PENDING.getCode());
                    }
                }else {
                    // 生成一条待审批记录
                    approvalPO = new MasterPlanPublishedApprovalPO();
                    approvalPO.setWorkOrderId(workOrderId);
                    approvalPO.setProductId(productId);
                    approvalPO.setQuantity(workOrderQty);
                    approvalPO.setDemandQuantity(demandQty);
                    approvalPO.setSupplyQuantity(supplyQty);
                    approvalPO.setApprovalStatus(PublishApprovalStatusEnum.PENDING.getCode());
                    approvalPO.setApprovalReason(resultPO.getMessage());
                }
                approvalPO.setStartTime(operation.getStartTime());
                approvalPO.setVehicleModelCode(productStockPointVO.getVehicleModelCode());
                approvalPO.setMaximumNumberDay(new BigDecimal(maximunNumberDay));
                approvalPO.setMaterialRiskLevel(riskLevel);
                approvalPO.setAfterWeekDemandQuantity(afterWeekDemandQty);
                approvalPO.setAfterTwoWeekDemandQuantity(afterTwoWeekDemandQty);
                resultPO.setMasterPlanPublishedApprovalPO(approvalPO);
            }
        }

        return resultPOList;
    }

    /**
     * 根据时间范围汇总需求量
     * @param startDay
     * @param endDay
     * @param deliveryPlanList
     * @return
     */
    private static BigDecimal getDurDemandQty(String startDay, String endDay, List<DeliveryPlanOverviewDetailVO> deliveryPlanList) {
        List<String> dateStrings = DateUtils.getIntervalDates(
                        DateUtils.stringToDate(startDay, DateUtils.COMMON_DATE_STR3),
                        DateUtils.stringToDate(endDay, DateUtils.COMMON_DATE_STR3))
                .stream()
                .map(DateUtils::dateToString)
                .collect(Collectors.toList());
        List<DeliveryPlanOverviewDetailVO> deliveryPlanOverviewDetailVOS = deliveryPlanList.stream().filter(t->dateStrings.contains(t.getDemandDate()))
                .collect(Collectors.toList());

        // 以计划开始时间作为开始，经过最大合批天数内的需求量
        return deliveryPlanOverviewDetailVOS.stream().map(DeliveryPlanOverviewDetailVO::getDemandQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 同时执行校验
     * 防止同一个人同时执行多个算法任务
     */
    public List<AlgorithmLog> checkPermission() {
        String userId = SystemHolder.getUserId();
        log.info("校验产线组运行权限开始，用户ID:{}", userId);
        List<AlgorithmLog> algorithmLogs = ipsFeign.selectTaskIsNotFail(Collections.singletonList(ModuleCodeEnum.MPS.getCode()));
        List<AlgorithmLog> userRunningLog = algorithmLogs.stream()
                .filter(p -> userId.equals(p.getCreator()))
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(userRunningLog)) {
            throw new BusinessException("您的排产任务正在运行，请等待结束后再操作");
        }
        return algorithmLogs;
    }


    private Map<String, BigDecimal> getFinishInventory(List<InventoryBatchDetailVO> inventoryBatchDetailVOS,
                                                       Map<String, SubInventoryCargoLocationVO> cargoLocationVOMap) {
        if (CollectionUtils.isEmpty(inventoryBatchDetailVOS)) {
            return new HashMap<>();
        }
        Map<String, BigDecimal> finishInventoryMap = inventoryBatchDetailVOS.stream().filter(p -> {
            String freightSpace = p.getFreightSpace();
            SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationVOMap.get(freightSpace);
            if (null == subInventoryCargoLocationVO) {
                return false;
            }
            return YesOrNoEnum.YES.getCode().equals(subInventoryCargoLocationVO.getEnabled());
        }).collect(Collectors.groupingBy(
                InventoryBatchDetailVO::getProductCode, Collectors.mapping(
                        p -> new BigDecimal(p.getCurrentQuantity()),
                        Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                )));
        return finishInventoryMap;
    }
    private  String getRangeData() {
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(SystemHolder.getScenario(), "SUB_INVENTORY", "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        return rangeData;
    }
}
