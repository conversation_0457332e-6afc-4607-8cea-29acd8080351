package com.yhl.scp.mps.capacityBalance.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO;
import com.yhl.scp.mps.capacityBalance.vo.BeatAverageVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>CapacitySupplyRelationshipDao</code>
 * <p>
 * 产能供应关系DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 15:41:29
 */
public interface CapacitySupplyRelationshipDao extends BaseDao<CapacitySupplyRelationshipPO, CapacitySupplyRelationshipVO> {


    /**
     * 查询最新数据
     * @return
     */
    List<CapacitySupplyRelationshipPO> selectLatestData(@Param("lockStatus") String lockStatus);

    /**
     * 查询锁定和委外数据
     * @return
     */
    List<CapacitySupplyRelationshipVO> selectLockOrOutData();



    List<CapacitySupplyRelationshipVO> selectLatestCode();

    /**
     * 删除未发布数据
     */
    void deleteUnpublishedBatch();

    /**
     * 删除周产能平衡数据
     */
    void deleteWeekData(@Param("type") String type);

    /**
     * 根据需求时间范围查询
     * @param productCode
     * @param operationCode
     * @param startTime
     * @param endTime
     * @return
     */
    List<CapacitySupplyRelationshipPO> selectForecastTime(@Param("productCode") String productCode,
                                                        @Param("operationCode") String operationCode,
                                                        @Param("startTime")Date startTime,
                                                        @Param("endTime")Date endTime);

	List<CapacitySupplyRelationshipPO> selectForSupplyCalculate(@Param("params") Map<String, Object> paramMap);


    int updateVersionIds(@Param("versionId")String versionId, @Param("ids") List<String> ids);

    /**
     * 汇总数据查询，按月汇总，结果集中的id为随机生成的不对应表中真实id
     * @param params
     * @return
     */
    List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipCollect(@Param("params") Map<String, Object> params);

    List<CapacitySupplyRelationshipVO> selectCapacitySupplyRelationshipCollectNew(@Param("params") Map<String, Object> params);

    List<BeatAverageVO> selectBeatAverage(@Param("params") Map<String, Object> params);

    /**
     * 根据当前最新产能供应关系，汇总月份-资源-供应量-占用产能
     * @return
     */
    List<CapacityLoadVO> selectLoad();

    /**
     * 查询需要删除的id
     * @return
     */
    List<String> selectNeedDeleteIds(@Param("versionIds") List<String> versionIds);

    /**
     * 查询最新的计划周期
     * @return
     */
    String selectLatestPlanPeriod();

    /**
     * 汇总数据查询，按月汇总，结果集中的id为随机生成的不对应表中真实id
     * @param params
     * @return
     */
    List<CapacitySupplyRelationshipVO> selectCollectByParamsNew(@Param("params") Map<String, Object> params);


    void saveCapacitySupplyRelationshipOnVersion(@Param("versionId") String versionId,
                                                 @Param("startId") String startId,
                                                 @Param("date") Date date,
                                                 @Param("userId") String userId);

    CapacitySupplyRelationshipVO selectLastVersion();

}
