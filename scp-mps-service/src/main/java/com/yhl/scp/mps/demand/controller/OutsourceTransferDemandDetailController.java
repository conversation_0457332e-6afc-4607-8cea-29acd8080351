package com.yhl.scp.mps.demand.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.demand.dto.OutsourceTransferDemandDetailDTO;
import com.yhl.scp.mps.demand.service.OutsourceTransferDemandDetailService;
import com.yhl.scp.mps.demand.vo.OutsourceTransferDemandDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OutsourceTransferDemandDetailController</code>
 * <p>
 * 委外转产材料需求明细控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-24 18:47:46
 */
@Slf4j
@Api(tags = "委外转产材料需求计算明细控制器")
@RestController
@RequestMapping("outsourceTransferDemandDetail")
public class OutsourceTransferDemandDetailController extends BaseController {

    @Resource
    private OutsourceTransferDemandDetailService outsourceTransferDemandDetailService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OutsourceTransferDemandDetailVO>> page() {
        List<OutsourceTransferDemandDetailVO> outsourceTransferDemandDetailList = outsourceTransferDemandDetailService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OutsourceTransferDemandDetailVO> pageInfo = new PageInfo<>(outsourceTransferDemandDetailList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OutsourceTransferDemandDetailDTO outsourceTransferDemandDetailDTO) {
        return outsourceTransferDemandDetailService.doCreate(outsourceTransferDemandDetailDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OutsourceTransferDemandDetailDTO outsourceTransferDemandDetailDTO) {
        return outsourceTransferDemandDetailService.doUpdate(outsourceTransferDemandDetailDTO);
    }

    @ApiOperation(value = "批量修改")
    @PostMapping(value = "batchUpdate")
    public BaseResponse<Void> batchUpdate(@RequestBody List<OutsourceTransferDemandDetailDTO> list) {
        outsourceTransferDemandDetailService.doUpdateBatch(list);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        outsourceTransferDemandDetailService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OutsourceTransferDemandDetailVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, outsourceTransferDemandDetailService.selectByPrimaryKey(id));
    }

}
