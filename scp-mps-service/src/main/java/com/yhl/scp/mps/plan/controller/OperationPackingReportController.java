package com.yhl.scp.mps.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.plan.dto.OperationPackingReportDTO;
import com.yhl.scp.mps.plan.service.OperationPackingReportService;
import com.yhl.scp.mps.plan.vo.OperationPackingReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OperationPackingReportController</code>
 * <p>
 * OperationPackingReportController
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:05
 */
@Slf4j
@Api(tags = "包装工序完工表控制器")
@RestController
@RequestMapping("operationPackingReport")
public class OperationPackingReportController extends BaseController {

    @Resource
    private OperationPackingReportService operationPackingReportService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OperationPackingReportVO>> page() {
        List<OperationPackingReportVO> operationPackingReportList = operationPackingReportService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OperationPackingReportVO> pageInfo = new PageInfo<>(operationPackingReportList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "保存批量报工数据")
    @PostMapping(value = "createBatch")
    public BaseResponse<Void> createBatch(@RequestBody List<OperationPackingReportDTO> operationPackingReportDTO) {
        return operationPackingReportService.doBatchCreate(operationPackingReportDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OperationPackingReportDTO operationPackingReportDTO) {
        return operationPackingReportService.doUpdate(operationPackingReportDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        operationPackingReportService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OperationPackingReportVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationPackingReportService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据工序id查询")
    @GetMapping(value = "selectByOperationId/{id}")
    public BaseResponse<List<OperationPackingReportVO>> selectByOperationId(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationPackingReportService.selectByOperationId(id));
    }

}
