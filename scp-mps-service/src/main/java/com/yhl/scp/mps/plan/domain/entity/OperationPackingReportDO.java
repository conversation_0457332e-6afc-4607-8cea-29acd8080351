package com.yhl.scp.mps.plan.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.*;
import lombok.experimental.SuperBuilder;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OperationPackingReportDO</code>
 * <p>
 * OperationPackingReportDO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:05
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationPackingReportDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 823234630419655098L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 制造订单ID
     */
    private String orderId;
    /**
     * 工序id
     */
    private String operationId;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 完工数量
     */
    private BigDecimal finishedQuantity;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 流水线时间
     */
    private Integer lineTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否启用
     */
    private String enabled;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改人
     */
    private String modifier;
    /**
     * 修改时间
     */
    private Date modifyTime;

}
