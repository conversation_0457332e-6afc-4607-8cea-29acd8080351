package com.yhl.scp.mps.manualAdjust.support;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.SpringContextUtils;
import com.yhl.scp.ams.extension.schedule.dto.AdjustmentParam;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.enums.RedisKeyManageEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustSource;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustTarget;
import com.yhl.scp.mps.manualAdjust.service.impl.ManualAdjustAlgorithmService;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.service.MasterPlanService;
import com.yhl.scp.mps.plan.vo.MasterPlanWorkOrderBodyVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.async.DeferredResult;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 手工调整处理器
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ManualAdjustHandler {

    private final Map<String, ManualAdjustSupport> strategyMap = MapUtil.newHashMap();

    @Resource
    private MasterPlanService masterPlanService;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private ManualAdjustAlgorithmService manualAdjustAlgorithmService;

    @Resource
    private CacheSetService cacheSetService;

    @Autowired
    public ManualAdjustHandler(Map<String, ManualAdjustSupport> strategyMap) {
        strategyMap.forEach((k, v) -> this.strategyMap.put(v.getCommand(), v));
    }

    /**
     * 手工调整
     *
     * @param param
     * @return
     */
    public BaseResponse<Void> handleBackEnd(ManualAdjustParam param) {
        ManualAdjustSupport manualAdjustSupport = strategyMap.get(param.getAdjustType());
        if (Objects.isNull(manualAdjustSupport)) {
            log.error("manual adjust type is not exist");
            return BaseResponse.error("manual adjust type is not exist");
        }
        // 校验操作合法性
        BaseResponse<Void> verifyManualAdjust = this.verifyManualAdjust(param, manualAdjustSupport);
        if (!verifyManualAdjust.getSuccess()) {
            return BaseResponse.error(verifyManualAdjust.getMsg());
        }
        return manualAdjustSupport.executeBackEnd(param);
    }

    /**
     * 调用算法
     *
     * @param pagination
     * @param planningHorizon
     * @param params
     * @param deferredResult
     * @return
     */
    public void handleAlgorithm(
            Pagination pagination,
            PlanningHorizonVO planningHorizon,
            List<ManualAdjustParam> params,
            DeferredResult<BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>>> deferredResult,
            int requestQuantity) {
        if (CollectionUtils.isEmpty(params)) {
            deferredResult.setResult(BaseResponse.success("无算法调整需执行"));
            return;
        }
        // 算法执行锁
        String algorithmLockKey =
                RedisKeyManageEnum.ALGORITHM_RUNNING_LOCK_KEY_PREFIX.getKey().replace("{userId}", SystemHolder.getUserId());
        // 加锁5分钟
        redisUtil.set(algorithmLockKey, SystemHolder.getUserId(), 5 * 60);
        List<AdjustmentParam> adjustmentParams = Lists.newArrayList();
        try {
            ManualAdjustHandler manualAdjustHandler = SpringBeanUtils.getBean("manualAdjustHandler");
            manualAdjustHandler.execute(planningHorizon, params, adjustmentParams);
        } catch (Exception ex) {
            log.error("算法执行失败：{}", ex.getMessage(), ex);
        } finally {
            redisUtil.delete(algorithmLockKey);
            // 产线释放锁
            resetResourceLock(adjustmentParams, Lists.newArrayList());
        }
        // 如果后续还存在请求，deferredResult修改result结果
        String requestQuantityKey =
                RedisKeyManageEnum.REQUEST_QUANTITY_LOCK_KEY_PREFIX.getKey().replace("{userId}", SystemHolder.getUserId());
        int firstRequestQuantity = (int) redisUtil.get(requestQuantityKey);
        if (requestQuantity < firstRequestQuantity) {
            deferredResult.setResult(BaseResponse.success("系统正在执行算法，请等待"));
        } else {
            try {
                // 刷新workOrder相关缓存
                String scenario = SystemHolder.getScenario();
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    // 刷新workOrder缓存
                    SpringContextUtils.getBean(CacheSetService.class).refreshWorkOrderCache(scenario);
                });
                // 等待缓存刷新完成
                future.join();

                ManualAdjustParam lastParam = params.get(params.size() - 1);
                PageInfo<MasterPlanWorkOrderBodyVO> masterPlanWorkOrder =
                        masterPlanService.masterPlanWorkOrder(pagination, lastParam.getReq(),new ArrayList<>());
                BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>> response =
                        BaseResponse.success(masterPlanWorkOrder);
                int secondRequestQuantity = (int) redisUtil.get(requestQuantityKey);
                if (requestQuantity < secondRequestQuantity) {
                    deferredResult.setResult(BaseResponse.success("系统正在执行算法，请等待"));
                } else {
                    deferredResult.setResult(response);
                }
            } catch (Exception ex) {
                log.error("算法执行后查询异常", ex);
                deferredResult.setResult(BaseResponse.error("查询异常"));
            }
        }
    }

    public void resetResourceLock(
            List<AdjustmentParam> oldParams, List<ManualAdjustParam> newParams) {
        Set<String> oldResourceIds = Sets.newHashSet();
        Set<String> newResourceIds = Sets.newHashSet();
        if (CollectionUtils.isNotEmpty(oldParams)) {
            for (AdjustmentParam param : oldParams) {
                String sourceResourceId = param.getSourceResourceId();
                if (StringUtils.isNotBlank(sourceResourceId)) {
                    oldResourceIds.add(sourceResourceId);
                }
                String targetResourceId = param.getTargetResourceId();
                if (StringUtils.isNotBlank(targetResourceId)) {
                    oldResourceIds.add(targetResourceId);
                }
            }
            if (CollectionUtils.isNotEmpty(oldResourceIds)) {
                for (String oldResourceId : oldResourceIds) {
                    String deleteResourceKey =
                            String.format(RedisKeyManageEnum.RESOURCE_LOCK_KEY_PREFIX.getKey(), oldResourceId);
                    redisUtil.delete(deleteResourceKey);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(newParams)) {
            for (ManualAdjustParam param : newParams) {
                ManualAdjustSource sourceInfo = param.getSourceInfo();
                if (Objects.nonNull(sourceInfo)) {
                    if (StringUtils.isNotBlank(sourceInfo.getResourceId())) {
                        newResourceIds.add(sourceInfo.getResourceId());
                    }
                }
                ManualAdjustTarget targetInfo = param.getTargetInfo();
                if (Objects.nonNull(targetInfo)) {
                    if (StringUtils.isNotBlank(targetInfo.getResourceId())) {
                        newResourceIds.add(targetInfo.getResourceId());
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(newResourceIds)) {
                for (String newResourceId : newResourceIds) {
                    String insertResourceKey =
                            String.format(RedisKeyManageEnum.RESOURCE_LOCK_KEY_PREFIX.getKey(), newResourceId);
                    redisUtil.set(insertResourceKey, SystemHolder.getUserId());
                }
            }
        }
    }

    /**
     * 校验请求信息
     *
     * @param param
     * @return
     */
    private BaseResponse<Void> verifyManualAdjust(
            ManualAdjustParam param, ManualAdjustSupport manualAdjustSupport) {
        if (Objects.isNull(param)) {
            return BaseResponse.error("参数错误");
        }
        String adjustType = param.getAdjustType();
        if (StringUtils.isBlank(adjustType)) {
            return BaseResponse.error("调整类型参数错误");
        }
        BaseResponse<Void> verify = manualAdjustSupport.verify(param);
        if (!verify.getSuccess()) {
            return verify;
        }
        ManualAdjustSource sourceInfo = param.getSourceInfo();
        ManualAdjustTarget targetInfo = param.getTargetInfo();
        String sourceInfoResourceId = Objects.nonNull(sourceInfo) ? sourceInfo.getResourceId() : null;
        String targetInfoResourceId = Objects.nonNull(targetInfo) ? targetInfo.getResourceId() : null;
        // 1.校验产线组运行权限
        BaseResponse<Void> resourcePermission =
                this.checkResourcePermission(sourceInfoResourceId, targetInfoResourceId);
        if (!resourcePermission.getSuccess()) {
            return resourcePermission;
        }
        // 当前用户ID
        String currentUserId = SystemHolder.getUserId();
        if (StringUtils.isNotBlank(sourceInfoResourceId)) {
            String sourceResourceKey =
                    String.format(RedisKeyManageEnum.RESOURCE_LOCK_KEY_PREFIX.getKey(), sourceInfoResourceId);
            Object sourceResourceUser = redisUtil.get(sourceResourceKey);
            if (Objects.nonNull(sourceResourceUser)) {
                String sourceUserId = sourceResourceUser.toString();
                if (!sourceUserId.equals(currentUserId)) {
                    return BaseResponse.error("产线正在运行排产任务，请稍后再试");
                }
            }
        }
        if (StringUtils.isNotBlank(targetInfoResourceId)) {
            String targetResourceKey =
                    String.format(RedisKeyManageEnum.RESOURCE_LOCK_KEY_PREFIX.getKey(), targetInfoResourceId);
            Object targetResourceUser = redisUtil.get(targetResourceKey);
            if (Objects.nonNull(targetResourceUser)) {
                String targetUserId = targetResourceUser.toString();
                if (!targetUserId.equals(currentUserId)) {
                    return BaseResponse.error("产线正在运行排产任务，请稍后再试");
                }
            }
        }
        if (StringUtils.isNotBlank(sourceInfoResourceId)) {
            String sourceResourceKey =
                    String.format(RedisKeyManageEnum.RESOURCE_LOCK_KEY_PREFIX.getKey(), sourceInfoResourceId);
            redisUtil.set(sourceResourceKey, currentUserId, 5 * 60);
        }
        if (StringUtils.isNotBlank(targetInfoResourceId)) {
            String targetResourceKey =
                    String.format(RedisKeyManageEnum.RESOURCE_LOCK_KEY_PREFIX.getKey(), targetInfoResourceId);
            redisUtil.set(targetResourceKey, currentUserId, 5 * 60);
        }
        return BaseResponse.success();
    }

    /**
     * 校验资源权限
     *
     * @param sourceInfoResourceId
     * @param targetInfoResourceId
     * @return
     */
    private BaseResponse<Void> checkResourcePermission(
            String sourceInfoResourceId, String targetInfoResourceId) {
        if (StringUtils.isBlank(sourceInfoResourceId) && StringUtils.isBlank(targetInfoResourceId)) {
            return BaseResponse.error("资源ID不能为空");
        }
        // 校验是否相关产线正在自动排程
        List<AlgorithmLog> algorithmLogs =
                ipsFeign.selectTaskIsNotFail(Collections.singletonList(ModuleCodeEnum.MPS.getCode()));
        if (CollectionUtils.isEmpty(algorithmLogs)) {
            return BaseResponse.success();
        }
        List<String> runningProductLines = Lists.newArrayList();
        for (AlgorithmLog algorithmLog : algorithmLogs) {
            String lineGroup = algorithmLog.getProductLine();
            if (StringUtils.isBlank(lineGroup)) {
                continue;
            }
            runningProductLines.addAll(Arrays.asList(lineGroup.split(",")));
        }
        if (CollectionUtils.isEmpty(runningProductLines)) {
            return BaseResponse.success();
        }
        // 目标资源和源资源对应的产线
        ArrayList<String> resourceIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(sourceInfoResourceId)) {
            resourceIds.add(sourceInfoResourceId);
        }
        if (StringUtils.isNotBlank(targetInfoResourceId)) {
            resourceIds.add(targetInfoResourceId);
        }
        List<String> productLines = operationTaskExtDao.selectRunResources(resourceIds);
        if (CollectionUtils.isEmpty(productLines)) {
            return BaseResponse.success();
        }
        List<String> intersection = CollectionUtils.getInterSection(productLines, runningProductLines);
        if (CollectionUtils.isEmpty(intersection)) {
            return BaseResponse.success();
        }
        return BaseResponse.error("产线正在运行排产任务，请稍后再试：" + intersection);
    }

    @Transactional(rollbackFor = Exception.class)
    public void execute(PlanningHorizonVO planningHorizon, List<ManualAdjustParam> params, List<AdjustmentParam> adjustmentParams) {
        for (ManualAdjustParam param : params) {
            ManualAdjustSupport manualAdjustSupport = strategyMap.get(param.getAdjustType());
            if (Objects.isNull(manualAdjustSupport)) {
                log.error("manual adjust type is not exist");
                continue;
            }
            List<AdjustmentParam> adjustments =
                    manualAdjustSupport.executeAlgorithm(planningHorizon, param);
            if (CollectionUtils.isEmpty(adjustments)) {
                log.error("无算法调整需执行：{}", JSONObject.toJSONString(param));
                continue;
            }
            adjustmentParams.addAll(adjustments);
        }
        // 执行算法
        manualAdjustAlgorithmService.doHandworkScheduleBatch(planningHorizon, adjustmentParams);
    }
}
