package com.yhl.scp.mps.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.req.PlanNodeTrackingReq;
import com.yhl.scp.mps.plan.res.PlanNodeTrackingRes;
import com.yhl.scp.mps.plan.service.PlanNodeTrackingService;
import com.yhl.scp.mps.plan.vo.FulfillDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.util.BaseReaderUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>PlanNodeTrackingController</code>
 * <p>
 * 计划节点跟踪控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-14 10:08:56
 */
@Slf4j
@Api(tags = "计划节点跟踪")
@RestController
@RequestMapping("planNodeTracking")
public class PlanNodeTrackingController extends BaseController {

    @Resource
    private PlanNodeTrackingService planNodeTrackingService;

    @ApiOperation(value = "分页查询")
    @PostMapping(value = "page")
    public BaseResponse<PageInfo<PlanNodeTrackingRes>> getPlanNodeTrackingList(@RequestBody PlanNodeTrackingReq planNodeTrackingReq) {
        return BaseResponse.success(planNodeTrackingService.getPlanNodeTrackingList(planNodeTrackingReq));
    }

    @ApiOperation(value = "分配明细查询")
    @GetMapping(value = "fulfillDetails")
    public BaseResponse<List<FulfillDetailVO>> fulfillDetails(@RequestParam(value = "orderId") String orderId){
        List<FulfillDetailVO> fulfillDetailVOS = planNodeTrackingService.fulfillDetails(orderId);
        return BaseResponse.success(fulfillDetailVOS);
    }

    @ApiOperation(value = "交期更新")
    @PostMapping(value = "dueDateUpdate")
    public BaseResponse<Void> dueDateUpdate(@RequestBody MasterPlanReq masterPlanReq) {
        planNodeTrackingService.doDueDateUpdate(masterPlanReq);
        return BaseResponse.success();
    }


}
