package com.yhl.scp.mps.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.plan.convertor.OperationPackingReportConvertor;
import com.yhl.scp.mps.plan.domain.entity.OperationPackingReportDO;
import com.yhl.scp.mps.plan.domain.service.OperationPackingReportDomainService;
import com.yhl.scp.mps.plan.dto.OperationPackingReportDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationPackingReportDao;
import com.yhl.scp.mps.plan.infrastructure.po.OperationPackingReportPO;
import com.yhl.scp.mps.plan.service.OperationPackingReportService;
import com.yhl.scp.mps.plan.vo.OperationPackingReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>OperationPackingReportServiceImpl</code>
 * <p>
 * OperationPackingReportServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:05
 */
@Slf4j
@Service
public class OperationPackingReportServiceImpl extends AbstractService implements OperationPackingReportService {

    @Resource
    private OperationPackingReportDao operationPackingReportDao;

    @Resource
    private OperationPackingReportDomainService operationPackingReportDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(OperationPackingReportDTO operationPackingReportDTO) {
        // 0.数据转换
        OperationPackingReportDO operationPackingReportDO = OperationPackingReportConvertor.INSTANCE.dto2Do(operationPackingReportDTO);
        OperationPackingReportPO operationPackingReportPO = OperationPackingReportConvertor.INSTANCE.dto2Po(operationPackingReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        operationPackingReportDomainService.validation(operationPackingReportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(operationPackingReportPO);
        operationPackingReportDao.insert(operationPackingReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OperationPackingReportDTO operationPackingReportDTO) {
        // 0.数据转换
        OperationPackingReportDO operationPackingReportDO = OperationPackingReportConvertor.INSTANCE.dto2Do(operationPackingReportDTO);
        OperationPackingReportPO operationPackingReportPO = OperationPackingReportConvertor.INSTANCE.dto2Po(operationPackingReportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        operationPackingReportDomainService.validation(operationPackingReportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(operationPackingReportPO);
        operationPackingReportDao.update(operationPackingReportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OperationPackingReportDTO> list) {
        List<OperationPackingReportPO> newList = OperationPackingReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        operationPackingReportDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<OperationPackingReportDTO> list) {
        List<OperationPackingReportPO> newList = OperationPackingReportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        operationPackingReportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return operationPackingReportDao.deleteBatch(idList);
        }
        return operationPackingReportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OperationPackingReportVO selectByPrimaryKey(String id) {
        OperationPackingReportPO po = operationPackingReportDao.selectByPrimaryKey(id);
        return OperationPackingReportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sds_ord_operation_packing_report")
    public List<OperationPackingReportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sds_ord_operation_packing_report")
    public List<OperationPackingReportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OperationPackingReportVO> dataList = operationPackingReportDao.selectByCondition(sortParam, queryCriteriaParam);
        OperationPackingReportServiceImpl target = SpringBeanUtils.getBean(OperationPackingReportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OperationPackingReportVO> selectByParams(Map<String, Object> params) {
        List<OperationPackingReportPO> list = operationPackingReportDao.selectByParams(params);
        return OperationPackingReportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OperationPackingReportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<OperationPackingReportVO> invocation(List<OperationPackingReportVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> doBatchCreate(List<OperationPackingReportDTO> operationPackingReportDTO) {
        if(CollectionUtils.isEmpty(operationPackingReportDTO)){
            return BaseResponse.error("请维护报工数据");
        }
        String operationId = operationPackingReportDTO.get(0).getOperationId();
        operationPackingReportDao.deleteByOperationId(operationId);
        List<OperationPackingReportPO> newList = OperationPackingReportConvertor.INSTANCE.dto2Pos(operationPackingReportDTO);
        BasePOUtils.insertBatchFiller(newList);
        operationPackingReportDao.insertBatch(newList);
        return BaseResponse.success();
    }

    @Override
    public List<OperationPackingReportVO> selectByOperationId(String operationId) {
        List<OperationPackingReportVO> operationPackingReportVOS = operationPackingReportDao.selectVOByParams(ImmutableMap.of("operationId", operationId))
                .stream().sorted(Comparator.comparing(OperationPackingReportVO::getStartTime)).collect(Collectors.toList());
        return operationPackingReportVOS;
    }
}
