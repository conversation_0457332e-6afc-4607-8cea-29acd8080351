package com.yhl.scp.mps.service.impl;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.biz.common.constants.CacheConstants;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.dto.ResourceCalendarQueryDTO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.cache.service.CacheGetService;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.vo.MasterPlanTaskVO;
import com.yhl.scp.mps.plan.vo.OperationPlanVO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.basic.order.vo.OperationTaskBasicVO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CacheServiceImpl</code>
 * <p>
 * CacheServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 21:44:23
 */
@Service
@Slf4j
public class CacheGetServiceImpl implements CacheGetService {

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;

    @Resource
    private MasterPlanExtDao operationExtDao;

    @Resource
    private MoldChangeTimeService moldChangeTimeService;

    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;

    @Resource
    @SuppressWarnings("unused")
    private CacheManager cacheManager;

    @Override
    @Cacheable(value = CacheConstants.WORK_ORDER_MAP, key = "#scenario")
    public Map<String, WorkOrderPO> getWorkOrderMap(String scenario) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<WorkOrderPO> workOrders = workOrderDao.selectByParams(new HashMap<>());
        DynamicDataSourceContextHolder.clearDataSource();
        return workOrders.stream().collect(Collectors.toMap(WorkOrderPO::getId, Function.identity(),
                (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.COMPREHENSIVE_YIELD_MAP, key = "#scenario")
    public Map<String, BigDecimal> getOrderId2ComprehensiveYieldMap(List<WorkOrderPO> workOrders, String scenario) {
        List<String> routingIds = workOrders.stream().map(WorkOrderPO::getRoutingId)
                .filter(routingId -> !StringUtils.isBlank(routingId)).distinct().collect(Collectors.toList());
        List<RoutingStepVO> routingStepByRoutingIds = newMdsFeign.getRoutingStepByRoutingIds(scenario, routingIds);
        Map<String, List<RoutingStepVO>> routingStepMap = routingStepByRoutingIds.stream()
                .collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));
        return workOrders.stream().filter(workOrderPO ->
                        !StringUtils.isBlank(workOrderPO.getRoutingId()))
                .collect(Collectors.toMap(WorkOrderPO::getId,
                        x -> routingStepMap.getOrDefault(x.getRoutingId(), new ArrayList<>())
                                .stream().map(RoutingStepVO::getYield).reduce(BigDecimal.ONE, BigDecimal::multiply)
                                .setScale(4, RoundingMode.HALF_UP), (v1, v2) -> v2));
    }

    @Override
    @Cacheable(value = CacheConstants.HW_LIMIT_STAND_RESOURCE_CODE, key = "#scenario")
    public String getHwLimitStandResourceCode(String scenario) {
        String collectionCode = "HW_LIMIT_STAND_RESOURCE_CODE";
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<CollectionValueVO> collections = ipsFeign.getByCollectionCode(collectionCode);
        DynamicDataSourceContextHolder.clearDataSource();
        return Optional.ofNullable(collections).filter(CollectionUtils::isNotEmpty)
                .map(hwResource -> hwResource.get(0).getCollectionValue()).orElse("");
    }

    @Override
    @Cacheable(value = CacheConstants.TOOL_RESOURCE_MAP, key = "#scenario")
    public Map<String, PhysicalResourceVO> getToolResourceMap(String scenario) {
        return newMdsFeign.selectPhysicalResourceByParams(scenario,
                ImmutableMap.of("resourceCategory", "TOOL")).stream().collect(Collectors
                .toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.STANDARD_STEP_MAP, key = "#scenario")
    public Map<String, String> getStandardStepMap(String scenario) {
        return newMdsFeign.selectStandardStepAll(scenario).stream().collect(Collectors
                .toMap(p -> p.getStockPointCode() + p.getStandardStepName(),
                        StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.NEW_STOCK_POINT_LIST, key = "#scenario")
    public List<NewStockPointVO> getNewStockPoints(String scenario) {
        return newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of("stockPointType", StockPointTypeEnum.BC.getCode()));
    }

    @Override
    @Cacheable(value = CacheConstants.STANDARD_RESOURCE_MAP, key = "#scenario + '_' + #userId")
    public Map<String, StandardResourceVO> getStandardResourceMap(String scenario, String userId,
                                                                  Map<String, PhysicalResourceVO> mainResourceMap) {
        List<String> standardResourceIds = mainResourceMap.values().stream()
                .map(PhysicalResourceVO::getStandardResourceId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(standardResourceIds)) {
            return new HashMap<>();
        }
        List<StandardResourceVO> standardResources = newMdsFeign.selectStandardResourceVOSByParams(scenario,
                ImmutableMap.of("ids", standardResourceIds));
        return StreamUtils.mapByColumn(standardResources, StandardResourceVO::getId);
    }

    @Override
    @Cacheable(value = CacheConstants.OPERATION_ID_2_TOOL_RESOURCE_ID_MAP, key = "#scenario + '_' + #userId")
    public Map<String, String> getOperationId2ToolResourceIdMap(String scenario, String userId,
                                                                List<String> operationIds) {
        if (CollectionUtils.isEmpty(operationIds)) {
            return new HashMap<>();
        }
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<MasterPlanTaskVO> masterPlanTasks = operationTaskExtDao.selectToolOperationTasks(operationIds);
        DynamicDataSourceContextHolder.clearDataSource();
        return CollectionUtils.isEmpty(operationIds) ? new HashMap<>() : masterPlanTasks.stream()
                .collect(Collectors.toMap(OperationTaskBasicVO::getOperationId,
                        OperationTaskBasicVO::getPhysicalResourceId, (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.OPERATION_MAP, key = "#scenario + '_' + #userId")
    public Map<String, OperationPO> getOperationMap(String scenario, String userId, List<String> operationIds) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<OperationPO> operations = CollectionUtils.isEmpty(operationIds)
                ? new ArrayList<>() : operationExtDao.selectByIds(operationIds);
        DynamicDataSourceContextHolder.clearDataSource();
        return operations.stream().collect(Collectors.toMap(OperationPO::getId, Function.identity(),
                (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.ROUTING_STEP_RESOURCE_MAP, key = "#scenario + '_' + #userId")
    public Map<String, RoutingStepResourceVO> getStepResourceMap(String scenario, String userId,
                                                                 Map<String, OperationPO> operationMap) {
        List<String> stepIds = operationMap.values().stream().map(OperationPO::getRoutingStepId)
                .distinct().collect(Collectors.toList());
        return CollectionUtils.isEmpty(stepIds)
                ? new HashMap<>() : newMdsFeign.selectRoutingStepResourceByRoutingStepIds(scenario, stepIds)
                .stream().collect(Collectors.toMap(p ->
                                String.join("&", p.getRoutingStepId(), p.getPhysicalResourceId()),
                        Function.identity(), (v1, v2) -> v1));
    }

    @Override
    // @Cacheable(value = CacheConstants.PRODUCT_MAP, key = "#scenario + '_' + #userId")
    public Map<String, NewProductStockPointVO> getProductMap(String scenario, String userId,
                                                             Map<String, OperationPO> operationMap,
                                                             Map<String, WorkOrderPO> workOrderMap,
                                                             List<OperationPlanVO> unPlanOperations) {
        List<String> unPlanProductIds = unPlanOperations.stream().map(OperationPlanVO::getProductId)
                .distinct().collect(Collectors.toList());
        List<String> productIds = operationMap.values().stream().map(OperationPO::getProductId)
                .distinct().collect(Collectors.toList());
        productIds.addAll(unPlanProductIds);
        productIds.addAll(workOrderMap.values().stream().map(WorkOrderPO::getProductId).collect(Collectors.toList()));
        productIds = productIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productIds)) {
            return new HashMap<>();
        }
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<NewProductStockPointVO> newProductStockPoints = operationTaskExtDao.selectProductCascadeByIds(productIds);
        DynamicDataSourceContextHolder.clearDataSource();
        return newProductStockPoints.stream().collect(Collectors.toMap(NewProductStockPointVO::getId,
                Function.identity(), (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.PRODUCT_STOCK_POINT_BASE_MAP, key = "#scenario + '_' + #userId")
    public Map<String, MdsProductStockPointBaseVO> getProductStockPointBaseMap(String scenario, String userId,
                                                                               Map<String, NewProductStockPointVO> productMap) {
        List<String> productCodes = productMap.values().stream().map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(productCodes)) {
            return new HashMap<>();
        }
        return newMdsFeign.selectProductStockPointBaseByParams(scenario,
                        ImmutableMap.of("productCodeList", productCodes)).stream()
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode,
                        Function.identity(), (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.MOLD_CHANGE_TIME_MAP, key = "#scenario + '_' + #userId")
    public Map<String, MoldChangeTimeVO> getMoldChangeTimeMap(String scenario, String userId,
                                                              Map<String, NewProductStockPointVO> productMap,
                                                              Map<String, PhysicalResourceVO> mainResourceMap) {
        List<String> mainResourceCodes = mainResourceMap.values().stream()
                .map(PhysicalResourceVO::getPhysicalResourceCode).distinct().collect(Collectors.toList());
        List<String> productCodes = productMap.values().stream().map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mainResourceCodes) || CollectionUtils.isEmpty(productCodes)){
            return new HashMap<>();
        }
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<MoldChangeTimeVO> moldChangeTimes = moldChangeTimeService.selectByParams(ImmutableMap
                .of("resourceCodes", mainResourceCodes, "productCodes", productCodes));
        DynamicDataSourceContextHolder.clearDataSource();
        return moldChangeTimes.stream().filter(p -> null != p.getDieChangeTime())
                .collect(Collectors.toMap(p ->
                        String.join("&", p.getOperationCode(), p.getResourceCode(),
                                p.getProductCode()), Function.identity(), (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.INVENTORY_BATCH_DETAIL_LIST, key = "#scenario + '_' + #userId")
    public List<InventoryBatchDetailVO> getInventoryBatchDetails(String scenario, String userId,
                                                                 Map<String, NewProductStockPointVO> productMap) {
        List<String> productCodes = productMap.values().stream().map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            return new ArrayList<>();
        }
        return dfpFeign.selectInventoryDataByProductCodes(scenario, productCodes, StockPointTypeEnum.BC.getCode());
    }

    @Override
    @Cacheable(value = CacheConstants.INVENTORY_BATCH_DETAIL_LIST, key = "#scenario + '_' + #userId")
    public List<InventoryBatchDetailVO> getInventoryBatchDetails(String scenario, String userId, List<String> productCodes) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return new ArrayList<>();
        }
        return dfpFeign.selectInventoryDataByProductCodes(scenario, productCodes, StockPointTypeEnum.BC.getCode());
    }

    @Override
    @Cacheable(value = CacheConstants.CARGO_LOCATION_MAP, key = "#scenario + '_' + #userId")
    public Map<String, SubInventoryCargoLocationVO> getCargoLocationMap(String scenario, String userId,
                                                                        List<InventoryBatchDetailVO> inventoryBatchDetails) {
        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(spaceList)) {
            return new HashMap<>();
        }
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<SubInventoryCargoLocationVO> subInventoryCargoLocations =
                subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode());
        DynamicDataSourceContextHolder.clearDataSource();
        return subInventoryCargoLocations
                .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.DELIVERY_PLAN_MAP, key = "#scenario + '_' + #userId")
    public Map<String, List<DeliveryPlanVO2>> getDeliveryPlanMap(String scenario, String userId,
                                                                 Map<String, NewProductStockPointVO> productMap,
                                                                 PlanningHorizonVO planningHorizon) {
        List<String> productCodes = productMap.values().stream().map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            return new HashMap<>();
        }
        return getDeliveryPlanResult(scenario, productCodes, planningHorizon);
    }

    private Map<String, List<DeliveryPlanVO2>> getDeliveryPlanResult(String scenario, List<String> productCodes, PlanningHorizonVO planningHorizon) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return new HashMap<>();
        }
        Date planStartTime = planningHorizon.getPlanStartTime();
        Date planEndTime = planningHorizon.getPlanEndTime();
        return dfpFeign.selectDeliveryPlanPublishedByParams(scenario, ImmutableMap.of("productCodes", productCodes,
                        "startTimeStr", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1),
                        "endTimeStr", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR1), "useCoordination", true))
                .stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode,
                        Collectors.collectingAndThen(Collectors.toList(), list ->
                                list.stream().sorted(Comparator.comparing(DeliveryPlanVO2::getDemandTime))
                                        .collect(Collectors.toList()))));
    }

    @Override
    @Cacheable(value = CacheConstants.DELIVERY_PLAN_MAP, key = "#scenario + '_' + #userId")
    public Map<String, List<DeliveryPlanVO2>> getDeliveryPlanMap(String scenario, String userId, List<String> productCodes, PlanningHorizonVO planningHorizon) {
        return getDeliveryPlanResult(scenario, productCodes, planningHorizon);
    }

    @Override
    @Cacheable(value = CacheConstants.SAFETY_STOCK_LEVEL_LIST, key = "#scenario + '_' + #userId")
    public List<SafetyStockLevelVO> getSafetyStockLevels(String scenario, String userId,
                                                         Map<String, NewProductStockPointVO> productMap) {
        List<String> productCodes = productMap.values().stream().map(NewProductStockPointVO::getProductCode)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            return new ArrayList<>();
        }
        return dfpFeign.selectSafetyStockLevelByProductCodeList(scenario, productCodes);
    }

    @Override
    @Cacheable(value = CacheConstants.PRODUCT_CANDIDATE_RESOURCE_MAP, key = "#scenario + '_' + #userId")
    public Map<String, ProductCandidateResourceVO> getProductCandidateResourceMap(String scenario, String userId,
                                                                                  Map<String, NewProductStockPointVO> productMap,
                                                                                  Map<String, PhysicalResourceVO> mainResourceMap) {
        List<String> productIds = new ArrayList<>(productMap.keySet());
        List<String> mainResourceIds = new ArrayList<>(mainResourceMap.keySet());
        if (CollectionUtils.isEmpty(productIds) || CollectionUtils.isEmpty(mainResourceIds)) {
            return new HashMap<>();
        }
        return newMdsFeign.selectProductCandidateResourceByParams(scenario, ImmutableMap
                        .of("productIds", productIds, "physicalResourceIds", mainResourceIds))
                .stream().collect(Collectors.toMap(p ->
                                String.join("&", p.getProductId(), p.getStandardStepId(), p.getPhysicalResourceId()),
                        Function.identity(), (v1, v2) -> v1));
    }

    @Override
//    @Cacheable(value = CacheConstants.ABNORMAL_RESOURCE_CALENDAR_LIST, key = "#scenario + '_' + #userId")
    public List<ResourceCalendarVO> getAbnormalResourceCalendar(String scenario, String userId,
                                                                List<String> standardResourceIds,
                                                                List<String> physicalResourceIds,
                                                                Date startDate, Date endDate) {
        ResourceCalendarQueryDTO resourceCalendarQueryDTO = new ResourceCalendarQueryDTO();
        resourceCalendarQueryDTO.setStandardResourceIds(standardResourceIds);
        resourceCalendarQueryDTO.setPhysicalResourceIds(physicalResourceIds);
        resourceCalendarQueryDTO.setCalendarType("ABNORMAL");
        resourceCalendarQueryDTO.setStartDate(startDate);
        resourceCalendarQueryDTO.setEndDate(endDate);
        return newMdsFeign.selectByResourceIdsAndDate(scenario, resourceCalendarQueryDTO);
    }

    @Override
    @CacheEvict(value = CacheConstants.ABNORMAL_RESOURCE_CALENDAR_LIST, key = "#scenario + '_' + #userId")
    public void evictAbnormalResourceCalendarCache(String scenario, String userId) {
        // 这个方法只需要执行缓存清除操作，不需要实际逻辑
    }

    @Override
    public List<BomRoutingStepInputVO> getBomTreeNews(String scenario,
                                                      String userId,
                                                      Map<String, NewProductStockPointVO> productMap) {
        List<String> productCodeList = productMap.values().stream()
                .map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodeList)) {
            return new ArrayList<>();
        }
        return newMdsFeign.selectBomRoutingStepInputByParams(scenario,
                ImmutableMap.of("productCodeList", productCodeList, "productType", ProductTypeEnum.SA.getCode()));
    }
}