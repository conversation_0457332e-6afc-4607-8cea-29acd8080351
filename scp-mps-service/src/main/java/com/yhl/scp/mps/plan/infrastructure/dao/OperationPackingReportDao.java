package com.yhl.scp.mps.plan.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mps.plan.infrastructure.po.OperationPackingReportPO;
import com.yhl.scp.mps.plan.vo.OperationPackingReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>OperationPackingReportDao</code>
 * <p>
 * OperationPackingReportDao
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:42:05
 */
public interface OperationPackingReportDao extends BaseDao<OperationPackingReportPO, OperationPackingReportVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link OperationPackingReportVO}
     */
    List<OperationPackingReportVO> selectVOByParams(@Param("params") Map<String, Object> params);

    void deleteByOperationId(@Param("operationId") String operationId);
}
