package com.yhl.scp.mps.plan.service;

import com.github.pagehelper.PageInfo;
import com.yhl.scp.mps.plan.req.PlanNodeTrackingReq;
import com.yhl.scp.mps.plan.res.PlanNodeTrackingRes;
import com.yhl.scp.mps.plan.vo.FulfillDetailVO;

import java.util.List;

/**
 * <code>PlanNodeTrackingService</code>
 * <p>
 * PlanNodeTrackingService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-14 10:13:37
 */
public interface PlanNodeTrackingService {

    PageInfo<PlanNodeTrackingRes> getPlanNodeTrackingList(PlanNodeTrackingReq planNodeTrackingReq);

    List<FulfillDetailVO> fulfillDetails(String orderId);

}
