package com.yhl.scp.mps.cache.service;

import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.routing.vo.BomRoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.plan.vo.OperationPlanVO;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import com.yhl.scp.sds.extension.order.infrastructure.po.OperationPO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;

import java.math.BigDecimal;
import java.util.*;

/**
 * <code>CacheGetService</code>
 * <p>
 * CacheGetService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 21:39:33
 */
public interface CacheGetService {

    Map<String, WorkOrderPO> getWorkOrderMap(String scenario);

    Map<String, BigDecimal> getOrderId2ComprehensiveYieldMap(List<WorkOrderPO> workOrders, String scenario);

    String getHwLimitStandResourceCode(String scenario);

    Map<String, PhysicalResourceVO> getToolResourceMap(String scenario);

    Map<String, String> getStandardStepMap(String scenario);

    List<NewStockPointVO> getNewStockPoints(String scenario);

    // 按需缓存 ///////////////////////////////////////////////////////
    Map<String, StandardResourceVO> getStandardResourceMap(String scenario, String userId,
                                                           Map<String, PhysicalResourceVO> mainResourceMap);
    Map<String, String> getOperationId2ToolResourceIdMap(String scenario, String userId, List<String> operationIds);
    Map<String, OperationPO> getOperationMap(String scenario, String userId, List<String> operationIds);
    Map<String, RoutingStepResourceVO> getStepResourceMap(String scenario, String userId,
                                                          Map<String, OperationPO> operationMap);
    Map<String, NewProductStockPointVO> getProductMap(String scenario, String userId,
                                                      Map<String, OperationPO> operationMap,
                                                      Map<String, WorkOrderPO> workOrderMap,
                                                      List<OperationPlanVO> unPlanOperations);
    Map<String, MdsProductStockPointBaseVO> getProductStockPointBaseMap(String scenario, String userId,
                                                                        Map<String, NewProductStockPointVO> productMap);
    Map<String, MoldChangeTimeVO> getMoldChangeTimeMap(String scenario, String userId,
                                                       Map<String, NewProductStockPointVO> productMap,
                                                       Map<String, PhysicalResourceVO> mainResourceMap);
    List<InventoryBatchDetailVO> getInventoryBatchDetails(String scenario, String userId,
                                                          Map<String, NewProductStockPointVO> productMap);
    List<InventoryBatchDetailVO> getInventoryBatchDetails(String scenario, String userId,
                                                          List<String> productCode);
    Map<String, SubInventoryCargoLocationVO> getCargoLocationMap(String scenario, String userId,
                                                                 List<InventoryBatchDetailVO> inventoryBatchDetails);
    Map<String, List<DeliveryPlanVO2>> getDeliveryPlanMap(String scenario, String userId,
                                                          Map<String, NewProductStockPointVO> productMap,
                                                          PlanningHorizonVO planningHorizon);
    Map<String, List<DeliveryPlanVO2>> getDeliveryPlanMap(String scenario, String userId,
                                                          List<String> productCodes,
                                                          PlanningHorizonVO planningHorizon);
    List<SafetyStockLevelVO> getSafetyStockLevels(String scenario, String userId,
                                                  Map<String, NewProductStockPointVO> productMap);
    Map<String, ProductCandidateResourceVO> getProductCandidateResourceMap(String scenario, String userId,
                                                                           Map<String, NewProductStockPointVO> productMap,
                                                                           Map<String, PhysicalResourceVO> mainResourceMap);
    List<ResourceCalendarVO> getAbnormalResourceCalendar(String scenario, String userId,
                                                         List<String> standardResourceIds, List<String> physicalResourceIds,
                                                         Date startDate, Date endDate);

    List<BomRoutingStepInputVO> getBomTreeNews(String scenario, String userId,
                                               Map<String, NewProductStockPointVO> productMap);

    void evictAbnormalResourceCalendarCache(String scenario, String userId);

}