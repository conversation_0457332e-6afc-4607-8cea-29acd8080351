package com.yhl.scp.mps.dispatch.mps.input;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ProductionPlannedInput</code>
 * <p>
 * ProductionIntervalInput
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024/12/5 9:46
 */
public class ProductionIntervalInput implements Serializable {

    private static final long serialVersionUID = 7880944799780259158L;
    private String productionIntervalId; // 生产批量id
    private String productStockPointId; // 输出库存点物品id
    private String standardResourceId; // 标准资源id
    private String startPeriod; // 开始周期id
    private String endPeriod; // 结束周期id
    private BigDecimal qty; // 计划生产数量
    private String fixed; // 是否固定
    private String startTime; // 计划开始
    private String endTime; // 计划结束

    private Date start;
    private Date end;

    public ProductionIntervalInput() {
    }

    public ProductionIntervalInput(String start, String end,String productionIntervalId) {
        this.startPeriod = start;
        this.endPeriod = end;
        this.productionIntervalId = productionIntervalId;
    }

    public Date getStart() {
        return start;
    }

    public void setStart(Date start) {
        this.start = start;
    }

    public Date getEnd() {
        return end;
    }

    public void setEnd(Date end) {
        this.end = end;
    }

    // Getters and Setters
    public String getProductionIntervalId() {
        return productionIntervalId;
    }

    public void setProductionIntervalId(String productionIntervalId) {
        this.productionIntervalId = productionIntervalId;
    }

    public String getProductStockPointId() {
        return productStockPointId;
    }

    public void setProductStockPointId(String productStockPointId) {
        this.productStockPointId = productStockPointId;
    }

    public String getStandardResourceId() {
        return standardResourceId;
    }

    public void setStandardResourceId(String standardResourceId) {
        this.standardResourceId = standardResourceId;
    }

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public String getFixed() {
        return fixed;
    }

    public void setFixed(String fixed) {
        this.fixed = fixed;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }
}
