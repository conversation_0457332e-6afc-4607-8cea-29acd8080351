package com.yhl.scp.mps.plan.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.plan.dto.OperationPackingReportDTO;
import com.yhl.scp.mps.plan.vo.OperationPackingReportVO;

import java.util.List;

/**
 * <code>OperationPackingReportService</code>
 * <p>
 * OperationPackingReportService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-15 17:41:16
 */
public interface OperationPackingReportService extends BaseService<OperationPackingReportDTO, OperationPackingReportVO> {

    /**
     * 查询所有
     *
     * @return list {@link OperationPackingReportVO}
     */
    List<OperationPackingReportVO> selectAll();

    BaseResponse<Void> doBatchCreate(List<OperationPackingReportDTO> operationPackingReportDTO);

    List<OperationPackingReportVO> selectByOperationId(String operationId);
}
