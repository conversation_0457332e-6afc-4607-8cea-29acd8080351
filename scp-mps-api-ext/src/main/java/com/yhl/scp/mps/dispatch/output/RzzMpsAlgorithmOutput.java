package com.yhl.scp.mps.dispatch.output;

import com.yhl.scp.mps.dispatch.mps.input.FinishedHalfProductMapping;
import com.yhl.scp.mps.dispatch.mps.input.ProductionPlannedMergeMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @ClassName MpsAlgorithmOutputRzz
 * @Description TODO
 * @Date 2024-12-07 10:58:55
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Data
public class RzzMpsAlgorithmOutput {

    @ApiModelProperty("需求")
    private List<RzzDemandOutput> demandOutputDataList;

    @ApiModelProperty("供应")
    private List<RzzSupplyOutput> supplyOutputDataList;

    @ApiModelProperty("分配关系")
    private List<RzzFulfillmentOutput> fulfillmentOutputDataList;

    @ApiModelProperty("生产计划量")
    private List<RzzProductionPlannedOutput> productionPlannedOutputDataList;

    @ApiModelProperty("生产计划量输入物品")
    private List<RzzProductionPlannedInputOutput> productionPlannedInputOutputDataList;

    @ApiModelProperty("生产计划量输出物品")
    private List<RzzProductionPlannedOutputOutput> productionPlannedOutputOutputDataList;

    @ApiModelProperty("生产计划量资源")
    private List<RzzProductionPlannedResourceOutput> productionPlannedResourceOutputDataList;

    @ApiModelProperty("生产批量")
    private List<RzzProductionIntervalOutput> productionIntervalOutputDataList;

    @ApiModelProperty("生产批量与计划量关系")
    private List<RzzProductionPlannedInIntervalOutput> productionPlannedInIntervalOutputDataList;

    private List<ProductionPlannedMergeMapping> productionPlannedMergeMappingList;

    private List<FinishedHalfProductMapping> finishedHalfProductMappingList;

    @ApiModelProperty("需要重新同步的制造订单id")
    private List<String> workOrderIds;

    @ApiModelProperty("需要固定数量的工艺路径步骤id")
    private Map<String, BigDecimal> routingStepOfQtyMap;

    private Boolean roundToNearestFiveFlag = Boolean.FALSE;
}
