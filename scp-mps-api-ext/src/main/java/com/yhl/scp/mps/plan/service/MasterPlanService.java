package com.yhl.scp.mps.plan.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mps.plan.dto.MasterPlanDTO;
import com.yhl.scp.mps.plan.dto.UpdateDueDateDTO;
import com.yhl.scp.mps.plan.dto.UpdateRemarkDTO;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.vo.*;
import com.yhl.scp.mps.util.LabelValueThree;

import java.util.List;
import java.util.Map;

/**
 * <code>MasterPlanService</code>
 * <p>
 * 主生产计划表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 13:53:16
 */
public interface MasterPlanService extends BaseService<MasterPlanDTO, MasterPlanVO> {

    /**
     * 查询所有
     *
     * @return list {@link MasterPlanVO}
     */
    List<MasterPlanVO> selectAll();

    void doBatchClose(List<String> ids);

    PageInfo<MasterPlanWorkOrderBodyVO> masterPlanWorkOrder(Pagination pagination, MasterPlanReq masterPlanReq,List<MasterPlanWorkOrderBodyVO> masterPlanWorkOrderBodyVOS);

    List<LabelValue<String>> selectStandardResourceDropdown(String organizationCode);

    List<LabelValue<String>> selectPhysicalResourceDropdown(String organizationCode, String standardResourceId);

    List<LabelValue<String>> listResourceDropDown02();

    List<LabelValue<String>> org();

    List<LabelValue<String>> standardResource();

    List<LabelValue<String>> physicalResource(List<String> standardResourceIds);

    List<LabelValue<String>> ruleOrg();

    void doBatchCancelPlan(List<String> ids);

    /**
     * 发货计划总览查询
     *
     * @param masterPlanReq 查询参数
     * @return java.util.List<com.yhl.scp.mps.plan.vo.DeliveryPlanGeneralViewVO>
     */
    List<DeliveryPlanGeneralViewVO> getDeliveryPlanGeneralView(MasterPlanReq masterPlanReq);

    List<LabelValue<String>> resourceDropDown(String productId, String standardStepId);

    List<LabelValue<String>> operationStep();

	List<LabelValue<String>> queryListResourceDropDown();

    List<MasterPlanWorkOrderBodyVO> getMasterPlan(MasterPlanReq masterPlanReq);

    List<LabelValue<String>> selectOperationDropdown(Map<String, Object> params);

    List<LabelValueThree<String>> selectStandardResourceDropdown2(String organizationCode);

    List<LabelValueThree<String>> selectPhysicalResourceDropdown2(String organizationCode, String standardResourceId);

    List<LabelValue<String>> operationStepTwo();

    PageInfo<MasterPlanWorkOrderVO> masterPlanWorkOrder2(Pagination pagination, MasterPlanReq masterPlanReq);

    List<DeliveryPlanOverviewVO> selectDeliveryPlanOverviewByPage(Pagination pagination, String sortParam,
                                                                  String queryCriteriaParam, MasterPlanReq masterPlanReq);

    void assembleDeliveryPlanOverviews(List<DeliveryPlanOverviewVO> deliveryPlanOverviews,
                                  List<DeliveryPlanOverviewDetailVO> detailList,
                                  MasterPlanReq masterPlanReq, PlanningHorizonVO planningHorizon, String scenario);

    void doUpdateRemark(UpdateRemarkDTO updateRemarkDTO);

    void updateDueDate(UpdateDueDateDTO updateDueDateDTO);

}