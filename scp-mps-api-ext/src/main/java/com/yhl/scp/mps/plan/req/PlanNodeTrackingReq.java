package com.yhl.scp.mps.plan.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <code>PlanNodeTrackingReq</code>
 * <p>
 * req
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-14 14:12:46
 */
@Data
public class PlanNodeTrackingReq {

    private String oem;

    private String vehicleModelCode;

    private String productCode;

    private String standardStepType;

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "排序字段")
    private String orderBy;

    private String scenario;
    private String userId;
    private List<String> productIds;
}
