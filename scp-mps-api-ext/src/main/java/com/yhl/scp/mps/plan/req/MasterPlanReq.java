package com.yhl.scp.mps.plan.req;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <code>MasterPlanReq</code>
 * <p>
 * MasterPlanReq
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 21:31:52
 */
@Data
public class MasterPlanReq implements Serializable {

    private static final long serialVersionUID = -1713590944510413465L;

    @ApiModelProperty(value = "组织")
    private String orgId;

    private String scenario;
    private String userId;

    @ApiModelProperty(value = "本厂编码")
    private String productCode;

    @ApiModelProperty(value = "发货开始日期")
    private Date deliverStartTime;

    @ApiModelProperty(value = "发货结束日期")
    private Date deliverEndTime;

    @ApiModelProperty(value = "计划状态")
    private String planStatus;

    @ApiModelProperty(value = "齐套状态")
    private String killStatus;

    @Deprecated
    @ApiModelProperty(value = "资源")
    private String resourceId;

    @ApiModelProperty(value = "排产工序")
    private String planOperation;

    @ApiModelProperty(value = "历史展望开始日期")
    private Date historyRetrospectStartTime;

    @ApiModelProperty(value = "页码")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页条数")
    private Integer pageSize = 10;
    
    @ApiModelProperty(value = "本厂编码")
    private List<String> productCodes;

    @ApiModelProperty(value = "产线组ID")
    private String standardResourceId;

    @ApiModelProperty(value = "排产资源ID列表")
    private List<String> physicalResourceIds;

    @ApiModelProperty(value = "排序数据")
    private String orderBy;

    @ApiModelProperty(value = "资源编码")
    private String resourceCode;

    @ApiModelProperty(value = "工序编码")
    private String operationCode;

    @ApiModelProperty(value = "最新发布id")
    private String lastPublishedLogId;

    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;

    @ApiModelProperty(value = "物理资源列表")
    private List<PhysicalResourceVO> physicalResources;

    @ApiModelProperty(value = "使用缓存")
    private Boolean useCache = Boolean.FALSE;

    @ApiModelProperty(value = "展示异常班次")
    private Boolean showAbnormalShift = Boolean.FALSE;

}