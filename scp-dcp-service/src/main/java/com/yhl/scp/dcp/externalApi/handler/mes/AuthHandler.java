package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.lang.Assert;
import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesToken;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.handler.TokenHandler;
import com.yhl.scp.dcp.token.Token;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName SyncCustomerHandler
 * @Description TODO
 * @Date 2024-08-27 11:10:53
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Slf4j
@Component("authHandler4MES")
public class AuthHandler extends TokenHandler {

    @Override
    protected Token getToken(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始请求MES接口TOKEN:{},{}", apiConfigVO, params);
        }
        String url = apiConfigVO.getApiUri();
//        String systemNumber = apiConfigVO.getSystemNumber();
//        String url = apiUri + "/" + systemNumber + "/" + sequenceService.getSuffix(systemNumber, this.getRedisKey(), 5);
        // 设置请求体
        String apiBody = apiConfigVO.getApiBody();
        String requestBody = getBody(apiBody);
        if (log.isInfoEnabled()) {
            log.info("url={}",url);
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 设置请求头
        String apiHeaders = apiConfigVO.getApiHeaders();
        JSONObject headerObject = JSONObject.parseObject(apiHeaders);
        headerObject.keySet().stream().forEach(key -> httpHeaders.set(key, headerObject.getString(key)));

        HttpEntity httpEntity = new HttpEntity(requestBody, httpHeaders);
        String responseData = restTemplate.postForObject(url, httpEntity, String.class);
        log.info("请求MES接口TOKEN完成,返回数据:{}!", responseData);

        MesToken mesToken = JSONObject.parseObject(responseData, MesToken.class);
        String token = mesToken.getAccessToken();
        Assert.isTrue(StringUtils.isNotBlank(token), "请求MES获取token返回错误");
        return Token.builder()
                .token(token)
                .expiresIn(mesToken.getExpiresIn())
                .build();
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.AUTH.getCode());
    }

    /**
     * 获取body参数
     *
     * @param apiBody
     * @return
     */
    private String getBody(String apiBody) {
        // 假设 body 是一个 JSON 对象，将其转换为 x-www-form-urlencoded 格式
        JSONObject bodyJson = JSONObject.parseObject(apiBody);
        StringBuilder formUrlEncoded = new StringBuilder();
        for (String key : bodyJson.keySet()) {
            String value = bodyJson.getString(key);
            formUrlEncoded.append(key).append("=").append(value).append("&");
        }
        // 移除最后一个 "&"
        if (formUrlEncoded.length() > 0) {
            formUrlEncoded.deleteCharAt(formUrlEncoded.length() - 1);
        }
        return formUrlEncoded.toString();
    }
}
