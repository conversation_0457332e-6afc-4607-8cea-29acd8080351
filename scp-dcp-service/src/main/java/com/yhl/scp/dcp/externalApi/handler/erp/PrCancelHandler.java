package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <code>PrCancelHandler</code>
 * <p>
 * PrCancelHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-09 11:10:53
 */
@Component
@Slf4j
public class PrCancelHandler extends SyncDataHandler<ErpResponse> {
    @Resource
    private MrpFeign mrpFeign;

    @Resource
    private AuthHandler authHandler;

    @Override
    protected ErpResponse convertData(String body) {
        return JSON.parseObject(body, ErpResponse.class);
    }

    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param response
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, ErpResponse response) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (Boolean.FALSE.equals(response.getSuccess())) {
            log.error("PR取消失败");
            throw new BusinessException(response.getMessage());
        }
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        log.info("开始取消PR");
        mrpFeign.handlePrCancel(scenario.getData(), response);
        log.info("结束取消PR");
        this.saveSyncCtrl(apiConfigVO, params, response);
        return null;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始ERP_PR取消:{},{}", apiConfigVO, params);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String url = apiUri + "?token=" + erpToken;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            String currentUrl = url + "&reqNum=" + params.get("reqNum")+ "&lineNum=" + params.get("lineNum")+ "&orgId=" + params.get("orgId")+ "&cancelReason=" + params.get("cancelReason")+ "&erpUser=" + params.get("erpUser");
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},url={}", erpToken, apiUri
                        , currentUrl);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(currentUrl ,String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("上传ERP_PR创建请求失败,HTTP状态码:{}!", statusCodeValue));
            }

            String body = responseEntity.getBody();
            int applyCount=1;
            mainLog.setApplyCount(applyCount);
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            log.info("上传ERP_PR取消处理完成,返回数据:{}!", body);
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.PR_CANCEL.getCode());
    }
}
