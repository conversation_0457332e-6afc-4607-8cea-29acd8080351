package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpSupplier;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName SupplierHandler
 * @Description TODO
 * @Date 2024-11-26 11:10:53
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class SupplierHandler extends SyncDataHandler<List<ErpSupplier>> {

    @Resource
    private AuthHandler authHandler;

    @Override
    protected List<ErpSupplier> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("ERP获取供应商基础数据为空");
            return null;
        }
        ErpResponse erpResponse = JSON.parseObject(body, ErpResponse.class);
        Object data = erpResponse.getData();
        return JSON.parseArray(data.toString(), ErpSupplier.class);
    }


    /**
     * 获取最新的更新时间
     *
     * @param erpSuppliers
     * @return
     */
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<ErpSupplier> erpSuppliers) {
        Date lastUpdateDate = erpSuppliers.stream().map(ErpSupplier::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    /**
     * 获取同步分组
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("orgId");
    }

    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param erpSuppliers
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpSupplier> erpSuppliers) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(erpSuppliers)) {
            log.error("供应商基础数据为空");
            return null;
        }
        erpSuppliers = erpSuppliers.stream()
                .filter(supplier -> params.get("orgId").toString().equals(supplier.getOrgId()))
                .filter(supplier -> {
                    String code = supplier.getSupplierCode();
                    return code != null && (code.startsWith("N") || code.startsWith("W"));
                })
                .collect(Collectors.collectingAndThen(
                Collectors.toMap(
                        ErpSupplier::getErpSupplierId,
                        dto -> dto,
                        (dto1, dto2) -> dto1.getLastUpdateDate().after(dto2.getLastUpdateDate()) ? dto1 : dto2
                ),
                map -> new ArrayList<>(map.values())
        ));
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        this.newMdsFeign.handleSupplier(scenario.getData(), erpSuppliers);
        this.saveSyncCtrl(apiConfigVO, params, erpSuppliers);
        return null;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始同步ERP供应商基础数据:{},{}", apiConfigVO, params);
        try {
            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDate, DateUtils.COMMON_DATE_STR3);
            lastUpdateDate = DateUtils.dateToString(calculateDate);
            String url = apiUri + "?token=" + erpToken
                    + "&lastUpdateDate=" + lastUpdateDate
                    + "&orgId=" + params.get("orgId").toString();

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            params.put("lastUpdateDate", lastUpdateDate);
            String bodyStr = JSONObject.toJSONString(params);
            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},systemNumber={},lastUpdateDate={},url={},bodyStr={}", erpToken, apiUri
                        , systemNumber, lastUpdateDate, url, bodyStr);
            }

            ResponseEntity<String> responseEntity = restTemplate.getForEntity(url, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP供应商基础数据请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();
            log.info("同步ERP供应商基础数据处理完成,返回数据:{}!", body);
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(), ApiCategoryEnum.SUPPLIER.getCode());
    }
}
