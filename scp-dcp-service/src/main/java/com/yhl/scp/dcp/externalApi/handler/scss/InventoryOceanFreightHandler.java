package com.yhl.scp.dcp.externalApi.handler.scss;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.scss.ScssInventoryOceanFreight;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightShippedMapVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @ClassName 浮法海运
 * @Description TODO
 * @Date 2024-11-26 11:10:53
 * <AUTHOR>
 * @Copyright 哈哈哈
 * @Version 1.0
 */
@Component
@Slf4j
public class InventoryOceanFreightHandler extends SyncDataHandler<List<InventoryOceanFreightShippedMapVO>> {
    @Resource
    private MrpFeign mrpFeign;
    @Resource
    private AuthHandler authHandler;

    private int delivery=5;

    @Override
    protected List<InventoryOceanFreightShippedMapVO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("SCSS获取浮法海运为空");
            return null;
        }
        return JSON.parseArray(body, InventoryOceanFreightShippedMapVO.class);
    }


    /**
     * 获取最新的更新时间
     *
     * @param o
     * @return
     */
    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<InventoryOceanFreightShippedMapVO> o) {
        String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
        Date calculateDate = DateUtils.stringToDate(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
        return DateUtils.dateToString(calculateDate, DateUtils.COMMON_DATE_STR1);
    }


    /**
     * 处理报文体
     *
     * @param apiConfigVO
     * @param params
     * @param o
     * @return
     */
    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                List<InventoryOceanFreightShippedMapVO> o) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(o)) {
            log.error("浮法海运接口为空");
            return null;
        }

        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        mrpFeign.handleInventoryOceanFreight(scenario.getData(), o);
        this.saveSyncCtrl(apiConfigVO, params, o);
        return null;
    }

    /**
     * 调用后台请求
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("开始同步SCSS浮法海运接口:{},{}", apiConfigVO, params);
        try {
            String scssToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String url = apiUri ;
            String listMap = params.get("list").toString();
            List<InventoryFloatGlassShippedDetailVO> list = JSON.parseArray(listMap,
                    InventoryFloatGlassShippedDetailVO.class);
            List<InventoryOceanFreightShippedMapVO> result = Lists.newArrayList();
            List<InventoryFloatGlassShippedDetailVO> filteredList = list.stream()
                    .collect(Collectors.toMap(
                            vo -> vo.getContainerNumber() + "-" + vo.getCarrier() + "-" + vo.getBillNo(),
                            vo -> vo,
                            (existing, replacement) -> existing // 保留第一个出现的值
                    ))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + scssToken);
            HashMap<Object, Object> paramMap = MapUtil.newHashMap();
            int poolSize = Math.min(filteredList.size(), 10); // 最大并发数不超过 10
            ExecutorService executor = Executors.newFixedThreadPool(poolSize);
            List<CompletableFuture<InventoryOceanFreightShippedMapVO>> futures = new ArrayList<>();

            List<InventoryFloatGlassShippedDetailVO> detailVosSourceErrorList =new ArrayList<>();
            LocalDate now = LocalDate.now();
            List<CollectionValueVO> deliveryResult =  ipsFeign.getByCollectionCode("DELIVERY_TIME_INVENTORY_OCEAN");
            if (CollectionUtils.isNotEmpty(deliveryResult)){
                List<String> deliveryResultList =
                        deliveryResult.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
                 delivery = Integer.valueOf(deliveryResultList.get(0));
            }
            for (InventoryFloatGlassShippedDetailVO vo : filteredList) {
                if (!isValid(vo)) {
                    log.info("同步SCSS浮法海运缺少字段:", paramMap);
                    continue;
                }
                paramMap.put("billNo", vo.getBillNo());
                paramMap.put("carrierCode", vo.getCarrier());
                paramMap.put("containerNo", vo.getContainerNumber());

                String bodyStr = JSONObject.toJSONString(paramMap);
                if (log.isInfoEnabled()) {
                    log.info("SCSSToken={},apiUri={},url={},bodyStr={}", scssToken, apiUri
                            , url, bodyStr);
                }

                HttpEntity<String> httpEntity = new HttpEntity<>(bodyStr, httpHeaders);
                CompletableFuture<InventoryOceanFreightShippedMapVO> future = CompletableFuture.supplyAsync(() -> {
                    try {
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                int statusCodeValue = responseEntity.getStatusCodeValue();
                if (HttpStatus.OK.value() != statusCodeValue) {
                    extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    throw new BusinessException(StrUtil.format("同步SCSS浮法海运接口请求失败,HTTP状态码:{}!", statusCodeValue));
                }
                extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
                String body = responseEntity.getBody();
                ErpResponse erpResponse = JSON.parseObject(body, ErpResponse.class);
                String key = vo.getContainerNumber() + "-" + vo.getCarrier() + "-" + vo.getBillNo();
                List<InventoryFloatGlassShippedDetailVO> detailVosSourceList = list.stream()
                        .filter(detailVO -> (detailVO.getContainerNumber() + "-" + detailVO.getCarrier() + "-" + detailVO.getBillNo()).equals(key))
                        .collect(Collectors.toList());
                if (erpResponse.getSuccess()) {
                    List<ScssInventoryOceanFreight> oceanFreight = JSON.parseArray(erpResponse.getData().toString(),
                            ScssInventoryOceanFreight.class);
                    if (CollectionUtils.isEmpty(oceanFreight)) {
                        log.info("记录同步SCSS浮法海运接口返回为空，body:{},vo:{}",body,vo);
                    }else {
                    for (InventoryFloatGlassShippedDetailVO detailVosSource : detailVosSourceList) {
                        InventoryOceanFreightShippedMapVO mapVO = new InventoryOceanFreightShippedMapVO();
                        mapVO.setScssInventoryOceanFreight(oceanFreight.get(0));
                        mapVO.setDetailVO(detailVosSource);
                        result.add(mapVO);
                    }
                    }
                    log.info("同步SCSS浮法海运接口处理完成,返回数据:{}!", body);
                } else {
                    for (InventoryFloatGlassShippedDetailVO detailVosSource : detailVosSourceList) {
                        if(detailVosSource.getDeliveryTime()!=null) {
                            LocalDate deliveryTime = detailVosSource.getDeliveryTime().toInstant()
                                    .atZone(ZoneId.systemDefault()).toLocalDate();
                            LocalDate fiveDaysLater = deliveryTime.plusDays(delivery);
                            if (!fiveDaysLater.isBefore(now)) {
                                continue;
                            }
                        }

                        detailVosSource.setRemark(erpResponse.getMessage());
                        detailVosSourceErrorList.add(detailVosSource);
                    }

                    log.info("同步SCSS浮法海运接口处理失败,返回数据:{}!","请求参数:"+bodyStr+"返回结果:"+ body);
                }
                    } catch (Exception e) {
                        log.error("调用 SCSS 浮法海运接口异常，vo={}", vo, e);
                        detailVosSourceErrorList.add(vo);
                    }
                    return null;
                }, executor);

                futures.add(future);
            }
            // 等待所有异步任务完成
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );
            allFutures.join(); // 阻塞直到所有请求完成

            // 收集结果
            // List<InventoryOceanFreightShippedMapVO> result = futures.stream()
            //         .map(future -> {
            //             try {
            //                 return future.get(); // 获取返回值
            //             } catch (Exception e) {
            //                 return null;
            //             }
            //         })
            //         .filter(Objects::nonNull)
            //         .collect(Collectors.toList());
            if(!detailVosSourceErrorList.isEmpty()){
            mainLog.setResponseBody(JSON.toJSONString(detailVosSourceErrorList));
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            }
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }


    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SCSS.getCode(),
                ApiCategoryEnum.INVENT_ORY_OCEAN_FREIGHT.getCode());
    }

    public static boolean isValid(InventoryFloatGlassShippedDetailVO po) {
        // 检查 carrier 是否为空
        if (po.getCarrier() == null || po.getCarrier().isEmpty()) {
            return false;
        }

        // 检查 remark 和 containerNumber 是否至少有一个不为空
        String remark = po.getRemark();
        String containerNumber = po.getContainerNumber();

        if ((remark == null || remark.isEmpty()) && (containerNumber == null || containerNumber.isEmpty())) {
            return false;
        }

        return true;
    }
}
