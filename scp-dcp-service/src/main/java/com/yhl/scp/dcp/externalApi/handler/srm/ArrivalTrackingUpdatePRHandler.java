package com.yhl.scp.dcp.externalApi.handler.srm;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dcp.externalApi.handler.erp.AuthHandler;
import com.yhl.scp.dcp.sync.vo.SyncCtrlVO;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.mrp.material.plan.result.UpdatePRTrackResult;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <code>ArrivalTrackingUpdatePRHandler</code>
 * <p>
 * 到货跟踪-更新单（PR模式）
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 15:42:40
 */

@Component
@Slf4j
public class ArrivalTrackingUpdatePRHandler extends SyncDataHandler<List<UpdatePRTrackResult>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private MrpFeign mrpFeign;

    @SneakyThrows
    @Override
    protected List<UpdatePRTrackResult> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("到货跟踪-更新单（要货模式）返回body为空");
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        // 将 JSON 字符串解析为 JsonNode
        JsonNode rootNode = objectMapper.readTree(body);
        // 获取 "Service" -> "Data" -> "Response" 节点
        JsonNode responseNode = rootNode.path("Service").path("Data").path("Response");
        // 确保 "UpdatePRTrackResult" 字段存在且是一个有效的字符串
        String UpdatePRTrackResult = responseNode.path("UpdatePRTrackResult").asText(null);

        if (com.yhl.platform.common.utils.StringUtils.isEmpty(UpdatePRTrackResult)) {
            // 处理字段不存在的情况
            throw new BusinessException("到货跟踪-更新单（要货模式）接口响应对象（UpdatePRTrackResult）返回异常");
        }
        // 解析嵌套的 JSON 数组
        return objectMapper.readValue(UpdatePRTrackResult,
                objectMapper.getTypeFactory().constructCollectionType(List.class, UpdatePRTrackResult.class));
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<UpdatePRTrackResult> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (Objects.isNull(list)) {
            log.error("到货跟踪-更新单（要货模式）数据为空");
            return null;
        }
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MRP.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        this.saveSyncCtrl(apiConfigVO, params, list);
        return JSONObject.toJSONString(list);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        log.info("到货跟踪-更新单（要货模式）:{},{}", apiConfigVO, params);
        try {
            String srmToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            String lastUpdateDate = this.getSyncRefValue(apiConfigVO, params);
            params.put("lastUpdateDate", lastUpdateDate);
            String bodyStr = JSONObject.toJSONString(params);
            log.info("到货跟踪-更新单（要货模式）请求参数为{}", bodyStr);
            if (log.isInfoEnabled()) {
                log.info("srmToken={},apiUri={},systemNumber={},lastUpdateDate={},url={},bodyStr={}", srmToken, apiUri
                        , systemNumber, lastUpdateDate, url, bodyStr);
            }
            HttpEntity httpEntity = new HttpEntity(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("到货跟踪-更新单（要货模式）请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();
            log.info("到货跟踪-更新单（要货模式）处理完成,返回数据:{}!", body);
            return body;
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.SRM.getCode(), ApiCategoryEnum.MATERIAL_ARRIVAL_TRACKING_UPDATE_PR.getCode());
    }
    @Override
    protected String getSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        SyncCtrlVO syncCtrlVO = syncCtrlService.getSyncCtrl(apiConfigVO, this.getSyncGroupValue(apiConfigVO, params));
        return ObjUtil.isNotNull(syncCtrlVO) ? syncCtrlVO.getReferValue() : DateUtil.offsetDay(DateTime.now(), -7).toString(DatePattern.PURE_DATE_PATTERN);
    }

    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("materialPlanTracking");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<UpdatePRTrackResult> UpdatePRTrackResults) {
        Date lastUpdateDate = new Date();
        return DateUtils.dateToString(lastUpdateDate, DatePattern.PURE_DATE_PATTERN);
    }
}
