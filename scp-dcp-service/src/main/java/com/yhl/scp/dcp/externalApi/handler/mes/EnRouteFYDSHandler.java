package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.fyds.FYDSResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.fyds.FYDSResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.warehouse.dto.EnRouteDTO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * description: 获取FYDS在途数据
 * author：李杰
 * email: <EMAIL>
 * date: 2024/12/4
 */
@Component
@Slf4j
public class EnRouteFYDSHandler extends SyncDataHandler<List<EnRouteDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步FYDS在途数据:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            //获取MES的token
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            //mes收发货记录地址接口
            String apiUri = apiConfigVO.getApiUri();
            //获取params参数
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri + apiParams;
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={}", mesToken, apiUri, apiParams, url);
            }
            //请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("interfaceCode", "tarzan-interface.shipment-message.sync");
            List<Object> arrayList = new ArrayList<>();
            List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS =
                    JSONObject.parseArray(JSONObject.toJSONString(params.get("list")), WarehouseReleaseRecordVO.class);
            for (WarehouseReleaseRecordVO dto : warehouseReleaseRecordVOS) {
                HashMap<String, Object> map = MapUtil.newHashMap();
                map.put("id", dto.getId());
                map.put("reqNum", dto.getReqNum());
                map.put("containerCode", dto.getContainerNum());
                map.put("shippingAgent", dto.getShipCompany());
                paramMap.put("data", map);
                String jsonString = JSONObject.toJSONString(paramMap);
                log.info("FYDS数据同步时，数据参数={}，请求头={}", jsonString,httpHeaders);
                HttpEntity httpEntity = new HttpEntity(jsonString, httpHeaders);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                int statusCodeValue = responseEntity.getStatusCodeValue();
                if (HttpStatus.OK.value() == statusCodeValue) {
                    String body = responseEntity.getBody();
                    log.info("同步FYDS在途数据完成,返回数据:{}!", body);
                    FYDSResponse fydsResponse = JSON.parseObject(body, FYDSResponse.class);
                    //判断数据是否正常
                    if (-1 != fydsResponse.getCode()) {
                        Object data = Objects.requireNonNull(fydsResponse).getData();
                        FYDSResponseData fydsResponseData = JSONObject.parseObject(JSONObject.toJSONString(data), FYDSResponseData.class);
                        Map<String, Object> message = fydsResponseData.getMessage();
                        Object object = message.get("responseData");
                        if (Objects.nonNull(object)) {
                            arrayList.add(object);
                        }
                    }
                }
            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, arrayList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSONObject.toJSONString(arrayList);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<EnRouteDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        return JSONObject.parseArray(body, EnRouteDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> paramMap, List<EnRouteDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, paramMap);
        if (CollectionUtils.isEmpty(list)) {
            log.error("MES获取FYDS在途数据为空");
            return null;
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        dfpFeign.syncFYDSData(scenario, list);
        return "同步成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.EN_ROUTE.getCode());
    }
}
