package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesProductCandidateResourceTime;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.feign.dto.HandleProductCandidateResourceTimeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ShanghaiMesProductCandidateResourceTimeHandler</code>
 * <p>
 * 同步产品资源生产关系（优先级，生产节拍）
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-18 17:15:03
 */
@Component
@Slf4j
public class ProductCandidateResourceTimeHandler extends SyncDataHandler<List<MesProductCandidateResourceTime>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private MpsFeign mpsFeign;

    @Override
    protected List<MesProductCandidateResourceTime> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES获取产品资源生产关系数据为空");
            return null;
        }
        return JSONArray.parseArray(body, MesProductCandidateResourceTime.class);
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesProductCandidateResourceTime> mesProductCandidateResourceTimes) {
        Date lastUpdateDate = mesProductCandidateResourceTimes.stream().map(MesProductCandidateResourceTime::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesProductCandidateResourceTime> mesProductCandidateResourceTimeList) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(mesProductCandidateResourceTimeList)) {
            log.error("产品资源生产关系数据为空");
            return null;
        }
        //保留最新数据
        Map<String, MesProductCandidateResourceTime> map = new HashMap<>();
        for (MesProductCandidateResourceTime mesProductCandidateResourceTime : mesProductCandidateResourceTimeList) {
            String key = mesProductCandidateResourceTime.getItemCode() + "-" + mesProductCandidateResourceTime.getSequenceCode() + "-" + mesProductCandidateResourceTime.getProdLine();
            if (!map.containsKey(key) || map.get(key).getLastUpdateDate().after(mesProductCandidateResourceTime.getLastUpdateDate())) {
                map.put(key, mesProductCandidateResourceTime);
            }
        }
        List<MesProductCandidateResourceTime> list = new ArrayList<>(map.values());

        log.info("产品资源生产关系数据,数据条数{}", list.size());
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        List<NewStockPointVO> newStockPointVOList = newMdsFeign.selectAllStockPoint(scenario.getData());
        scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MPS.getCode(), TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenario.getSuccess(), scenario.getMsg());
        Map<String, NewStockPointVO> newStockPointVOMap = newStockPointVOList.stream().collect(Collectors.toMap(NewStockPointVO::getStockPointCode, Function.identity()));
        HandleProductCandidateResourceTimeDTO dto = new HandleProductCandidateResourceTimeDTO();
        dto.setO(list);
        dto.setNewStockPointVOMap(newStockPointVOMap);
        // mpsFeign.handleProductCandidateResourceTime(scenario.getData(), dto);
        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES产品资源生产关系:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            //获取MES TOKEN
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri + apiParams;
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={}", mesToken, apiUri, apiParams,
                        url);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            String reqCode = params.get("reqCode").toString();
            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                paramMap.put("reqCode", reqCode);
                paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                Date endTime = org.apache.commons.lang3.time.DateUtils.addDays(beginTime, calculatePeriod);
                paramMap.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                beginTime = endTime;
                if (log.isInfoEnabled()) {
                    log.info("request paramMap={}", paramMap);
                }
                // 创建子日志
                ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                        httpHeaders.toString(),
                        JSONObject.toJSONString(paramMap));
                HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(paramMap), httpHeaders);
                ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                int statusCodeValue = responseEntity.getStatusCodeValue();
                if (HttpStatus.OK.value() != statusCodeValue) {
                    extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                }
                Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步产品资源生产关系失败！");
                String body = responseEntity.getBody();
                log.info("同步MES产品资源生产关系完成,返回数据:{}!", body);
                MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                MesResponseData data = mesResponse.getData();
                extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                result.addAll(data.getMessage());
            }
            log.info("产品资源生产关系数据,数据条数{}", result.size());
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSONObject.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.PRODUCT_CANDIDATE_RESOURCE.getCode());
    }
}
