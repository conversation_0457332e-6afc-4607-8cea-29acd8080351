package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.mds.baseResource.dto.PhysicalResourceLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * description:mes获取主数据
 * author：李杰
 * email: <EMAIL>
 * date: 2024/11/26
 */
@Component
@Slf4j
public class PhysicalResourceHandler extends SyncDataHandler<List<PhysicalResourceLogDTO>> {
    @Resource
    private AuthHandler authHandler;

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<PhysicalResourceLogDTO> standardResourceDTO) {
        Date lastUpdateDate = standardResourceDTO.stream().map(PhysicalResourceLogDTO::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map params) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            if (log.isInfoEnabled()) {
                log.info("开始同步MES主数据数据:{},{}", apiConfigVO, params);
            }
            //获取MES的token
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            //mes收发货记录地址接口
            String apiUri = apiConfigVO.getApiUri();
            String systemNumber = apiConfigVO.getSystemNumber();
            //获取params参数
            String apiParams = apiConfigVO.getApiParams();
            String url = apiUri + apiParams;
            Date beginDate;
            Date currentDate;
            if (Objects.nonNull(params.get("triggerType"))) {
                //由于MES设置前后时间不可超过七天，所以将时间分段调用
                beginDate = DateUtils.stringToDate(params.get("lastUpdateDate").toString(), DateUtils.COMMON_DATE_STR1);
                currentDate = DateUtils.stringToDate(params.get("endDate").toString(), DateUtils.COMMON_DATE_STR1);
            } else {
                beginDate = DateUtils.stringToDate(this.getSyncRefValue(apiConfigVO, params), DateUtils.COMMON_DATE_STR1);
                currentDate = new Date();
            }
            if (beginDate.compareTo(currentDate) == 1) {
                log.error("时间错误，lastUpdateDate不能大于endDate");
                return null;
            }
            if (log.isInfoEnabled()) {
                log.info("mesToken={},apiUri={},apiParams={},url={},lastUpdateDateStr={},currentDate={}", mesToken, apiUri, apiParams,
                        url, beginDate, currentDate);
            }
            //请求头
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            //请求体
            int size = Objects.isNull(apiConfigVO.getOffsetSize()) ? 5000 : apiConfigVO.getOffsetSize();
            HashMap<String, Object> paramMap = new HashMap<>();
            paramMap.put("reqCode", "FY_PRO_LINE_FOR_BPIM");
            paramMap.put("pageSize", size);

            int period = (int) DateUtil.between(beginDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            List<Object> arrayList = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                for (int page = 1; ; page++) {
                    paramMap.put("currentPage", page);
                    paramMap.put("beginTime", DateUtils.dateToString(beginDate, DateUtils.COMMON_DATE_STR1));
                    Date endDate = org.apache.commons.lang3.time.DateUtils.addDays(beginDate, calculatePeriod);
                    if (endDate.compareTo(currentDate) == 1) {
                        endDate = currentDate;
                    }
                    paramMap.put("endTime", DateUtils.dateToString(endDate, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    ExtApiLogDTO secondLog = extApiLogService.createLog(apiConfigVO, params, mainLog, httpHeaders.toString(), JSONObject.toJSONString(paramMap));

                    HttpEntity httpEntity = new HttpEntity(JSONObject.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() == statusCodeValue) {
                        String body = responseEntity.getBody();
                        log.info("同步MES主资源数据完成,返回数据:{}!", body);
                        MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                        MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                        if (Objects.nonNull(data)) {
                            arrayList.addAll(data.getMessage());
                            extApiLogService.updateResponse(secondLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                            if (data.getCurrentPage() >= data.getTotalPage()) {
                                beginDate = endDate;
                                break;
                            }
                        } else {
                            extApiLogService.updateResponse(secondLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                        }
                    } else {
                        extApiLogService.updateResponse(secondLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                }
            }
            extApiLogService.updateResponse(mainLog, null, arrayList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            this.postProcess(apiConfigVO, params);
            return JSONObject.toJSONString(arrayList);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    protected List<PhysicalResourceLogDTO> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            return null;
        }
        return JSONObject.parseArray(body, PhysicalResourceLogDTO.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> paramMap, List<PhysicalResourceLogDTO> list) {
        this.saveSyncCtrlSyncTime(apiConfigVO, paramMap);
        if (CollectionUtils.isEmpty(list)) {
            log.error("MES获取主资源数据为空");
            return "MES获取主资源数据为空";
        }
        //筛选出对应组织的数据
        Object object = paramMap.get("organizations");
        List<String> orgList = JSONObject.parseObject(JSONObject.toJSONString(object), List.class);
        list = list.stream().filter(x-> orgList.contains(x.getPlantCode())).collect(Collectors.toList());
        log.info("筛选该{}下的数据，条数{}条",orgList.toString(),list.size());
        log.info("筛选后的数据= {}",list);
        if (list.isEmpty()){
            log.error("筛选后的主资源数据为空");
            return "筛选后的主资源数据为空";
        }
        //获取模块标识
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        list.stream().forEach(
                x -> x.setEnableFlag(StringUtils.isEmpty(x.getEnableFlag()) ? YesOrNoEnum.NO.getCode() :
                        "Y".equals(x.getEnableFlag()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
        );
        newMdsFeign.syncPhysicalResourceData(scenario, list);
        if (Objects.isNull(paramMap.get("triggerType"))) {
            this.saveSyncCtrl(apiConfigVO, paramMap, list);
        }
        return "同步成功";
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(), ApiCategoryEnum.PHYSICAL_RESOURCE.getCode());
    }

    @Override
    public String postProcess(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        BaseResponse<String> defaultScenario1 = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        String scenario = defaultScenario1.getData();
        newMdsFeign.completePhysicalResourceData(scenario);
        return "补齐旧数据的标准资源ID成功";
    }
}
