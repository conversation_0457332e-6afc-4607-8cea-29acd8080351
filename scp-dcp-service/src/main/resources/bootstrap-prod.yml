nacos-server-addr: nacos-headless.nacos.svc.cluster.local:8848
spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos-server-addr}
        namespace: bpim
      config:
        server-addr: ${nacos-server-addr}
        namespace: bpim
        group: DEFAULT_GROUP
        prefix: scp-dcp-service
        file-extension: yaml
        shared-configs:
          - dataId: datasource.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: mongodb.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: redis.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: minio.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: rabbitmq.yaml
            group: DEFAULT_GROUP
            refresh: true
          - dataId: logging.yaml
            group: DEFAULT_GROUP
            refresh: true