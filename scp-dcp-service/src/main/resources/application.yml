spring:
  config:
    import: classpath:enum-extension.yml
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  security:
    user:
      name: admin
      password: admin
    oauth2:
      client:
        registration:
          SRM:
            client-id: srm
            client-secret: secret
            scope: srm.read,srm.write
            authorization-grant-type: client_credentials
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
          MES:
            client-id: mes
            client-secret: secret
            scope: mes.read, mes.write
            authorization-grant-type: client_credentials
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
          GRP:
            client-id: grp
            client-secret: secret
            scope: grp.read,grp.write
            authorization-grant-type: client_credentials
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"

server:
  port: 8769