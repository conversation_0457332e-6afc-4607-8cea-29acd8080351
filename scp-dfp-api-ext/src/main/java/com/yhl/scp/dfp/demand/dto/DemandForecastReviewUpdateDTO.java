package com.yhl.scp.dfp.demand.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>DemandForecastReviewUpdateDTO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 09:36:27
 */
@ApiModel(value = "业务预测评审编辑DTO")
@Data
public class DemandForecastReviewUpdateDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 391320689521634207L;

    @ApiModelProperty(value="数据ID")
    private String dataId;

    @ApiModelProperty(value = "版本ID")
    private String forecastVersionId;

    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;

    @ApiModelProperty(value = "备注")
    private String remark;
    
    @ApiModelProperty(value="主机厂编码")
    private String oemCode;
    
    @ApiModelProperty(value="年月")
    private String forecastTime;
    
    @ApiModelProperty(value="主机厂车型编码")
    private String productCode;
    
    @ApiModelProperty(value="需求类型")
    private String demandCategory;
}
