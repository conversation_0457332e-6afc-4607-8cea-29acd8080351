package com.yhl.scp.dfp.oem.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <code>OemInventorySubmissionExitDTO</code>
 * <p>
 * 中转库与主机厂库存提报(出口)DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-31 14:51:50
 */
@ApiModel(value = "中转库与主机厂库存提报(出口)DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OemInventorySubmissionExitDTO extends OemInventorySubmissionExitBasicDTO {


}
