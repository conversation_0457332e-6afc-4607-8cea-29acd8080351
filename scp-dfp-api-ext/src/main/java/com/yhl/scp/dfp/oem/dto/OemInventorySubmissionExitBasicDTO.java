package com.yhl.scp.dfp.oem.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import com.yhl.scp.common.excel.ExcelPropertyCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OemInventorySubmissionExitBasicDTO</code>
 * <p>
 * 中转库与主机厂库存提报(出口)DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 15:17:27
 */
@ApiModel(value = "中转库与主机厂库存提报(出口)DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class OemInventorySubmissionExitBasicDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 206421313597344862L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 中转库编码
     */
    @ApiModelProperty(value = "中转库编码")
    @ExcelProperty(value = "中转库编码*")
    @ExcelPropertyCheck(required = true)
    private String stockPointCode;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @ExcelProperty(value = "主机厂编码*")
    @ExcelPropertyCheck(required = true)
    private String oemCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @ExcelProperty(value = "产品编码*")
    @ExcelPropertyCheck(required = true)
    private String productCode;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    @ExcelProperty(value = "零件号")
    private String partNumber;
    /**
     * 零件名称
     */
    @ApiModelProperty(value = "零件名称")
    @ExcelProperty(value = "零件名称")
    private String partName;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @ExcelProperty(value = "日期*")
    @ExcelPropertyCheck(required = true)
    private Date submissionDate;
    
    /**
     * 发货清单号(国内)
     */
    @ApiModelProperty(value = "发货清单号(国内)")
    @ExcelProperty(value = "发货清单号(国内)")
    private String ShippingListNumber;
    
    /**
     * 柜号(海外)
     */
    @ApiModelProperty(value = "柜号(海外)")
    @ExcelProperty(value = "柜号(海外)")
    private String containerNumber;
    
    /**
     * 中转库发货数量
     */
    @ApiModelProperty(value = "中转库发货数量")
    @ExcelProperty(value = "中转库发货数量-发货")
    private BigDecimal transitDeliverQuantity;
    /**
     * 中转库入库数量
     */
    @ApiModelProperty(value = "中转库入库数量")
    @ExcelProperty(value = "中转库入库数量")
    private BigDecimal transitEnterQuantity;
    /**
     * 中转库待接收量
     */
    @ApiModelProperty(value = "中转库待接收量")
    @ExcelProperty(value = "中转库待接收量-在途数量")
    private BigDecimal transitWaitQuantity;
    /**
     * 中转库库存数量
     */
    @ApiModelProperty(value = "中转库库存数量")
    @ExcelProperty(value = "中转库库存数量-结存量")
    private BigDecimal stockInventoryQuantity;
    /**
     * 主机厂库存数量
     */
    @ApiModelProperty(value = "主机厂库存数量")
    @ExcelProperty(value = "主机厂库存数量")
    private BigDecimal oemInventoryQuantity;
    /**
     * 退货
     */
    @ApiModelProperty(value = "退货")
    @ExcelProperty(value = "退货")
    private String returnGoods;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

}
