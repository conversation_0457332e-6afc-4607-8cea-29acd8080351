package com.yhl.scp.dfp.demand.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastVersionDTO;
import com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <code>DemandForecastVersionService</code>
 * <p>
 * 业务预测版本应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 11:00:34
 */
public interface DemandForecastVersionService extends BaseService<DemandForecastVersionDTO, DemandForecastVersionVO> {

    /**
     * 查询所有
     *
     * @return list {@link DemandForecastVersionVO}
     */
    List<DemandForecastVersionVO> selectAll();

    /**
     * 批量删除
     *
     * @param versionDTOList {@link RemoveVersionDTO}
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    /**
     * 分级查询
     *
     * @return java.util.List<com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO>
     */
    List<DemandForecastVersionVO> treeQuery();

    /**
     * 版本新建
     *
     * @param versionDTO {@link DemandForecastVersionDTO}
     */
    void createDemandForecastVersion(DemandForecastVersionDTO versionDTO);

    /**
     * 业务预测版本发布
     *
     * @param versionId 版本ID
     */
    void publishVersion(String versionId);

    /**
     * 业务预测版本详情查询
     *
     * @param versionCode 版本代码
     * @return com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO
     */
    DemandForecastVersionVO selectForecastVersion(String versionCode);


    /**
     * 根据发布状态查询版本号
     *
     * @param publishStatus 发布状态
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.String>>
     */
    List<Map<String, String>> selectDistinctVersionCode(String publishStatus);

    /**
     * 查询第二三层级已发布版本信息
     *
     * @return java.util.List<com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO>
     */
    List<DemandForecastVersionVO> selectPublishVersionDetail();

    /**
     * 根据第二层级id获取该版本下的所有主机厂编码
     *
     * @param versionId 版本ID
     * @return java.util.Set<java.lang.String>
     */
    Set<String> selectOemCodeListByVersionId(String versionId);

    /**
     * 目标需求版本
     *
     * @param planPeriod 计划周期
     * @return java.util.List<com.yhl.platform.common.LabelValue < java.lang.String>>
     */
    List<LabelValue<String>> targetVersion(String planPeriod);

    List<LabelValue<String>> selectLastVersionCodes(List<String> planPeriods);

    String selectByRollingVersionId(String rollingVersionId);

    DemandForecastVersionVO selectVersionsNewByParams(Map<String, Object> extMap2);

    void doCopyByCleanForecastData(String rollingVersionId);

    /**
     * 获取计划期间内的最新版本
     * @param planPeriod
     * @return
     */
    DemandForecastVersionVO selectLastVersionByPlanPeriod(String planPeriod);


    String selectLatestVersionId();

    DemandForecastVersionVO selectLatestVersion();

}
