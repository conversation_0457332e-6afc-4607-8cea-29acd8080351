package com.yhl.scp.dfp.stock.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>InventoryBatchDetailVO</code>
 * <p>
 * 库存批次明细VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 09:53:03
 */
@ApiModel(value = "库存批次明细VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBatchDetailVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -63567568789363927L;
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @FieldInterpretation(value = "主键ID")
    private String id;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @FieldInterpretation(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 物料名称
     */
    @ApiModelProperty(value = "物料名称")
    @FieldInterpretation(value = "物料名称")
    private String productName;
    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @FieldInterpretation(value = "单位")
    private String measurementUnit;
    /**
     * 工序
     */
    @ApiModelProperty(value = "工序")
    @FieldInterpretation(value = "工序")
    private String operationCode;
    /**
     * 子库存
     */
    @ApiModelProperty(value = "子库存")
    @FieldInterpretation(value = "子库存")
    private String subinventory;
    /**
     * 子库存描述
     */
    @ApiModelProperty(value = "子库存描述")
    @FieldInterpretation(value = "子库存描述")
    private String subinventoryDescription;
    /**
     * 货位
     */
    @ApiModelProperty(value = "货位")
    @FieldInterpretation(value = "货位")
    private String freightSpace;
    /**
     * 货位描述
     */
    @ApiModelProperty(value = "货位描述")
    @FieldInterpretation(value = "货位描述")
    private String freightSpaceDescription;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @FieldInterpretation(value = "批次")
    private String batch;
    /**
     * 条码号
     */
    @ApiModelProperty(value = "条码号")
    @FieldInterpretation(value = "条码号")
    private String barCode;
    /**
     * 现有量
     */
    @ApiModelProperty(value = "现有量")
    @FieldInterpretation(value = "现有量")
    private String currentQuantity;
    /**
     * 客户号
     */
    @ApiModelProperty(value = "客户号")
    @FieldInterpretation(value = "客户号")
    private String customerNum;
    /**
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    @FieldInterpretation(value = "零件号")
    private String partNum;
    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @FieldInterpretation(value = "入库时间")
    private String assignedTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private String lastUpdateDate;
    /**
     * 库龄
     */
    @ApiModelProperty(value = "库龄")
    @FieldInterpretation(value = "库龄")
    private String stockAge;
    /**
     * 库龄天数
     */
    @ApiModelProperty(value = "库龄天数")
    @FieldInterpretation(value = "库龄天数")
    private String stockAgeDay;
    /**
     * 保质期
     */
    @ApiModelProperty(value = "保质期")
    @FieldInterpretation(value = "保质期")
    private String warrantyDate;
    /**
     * 距离失效时间
     */
    @ApiModelProperty(value = "距离失效时间")
    @FieldInterpretation(value = "距离失效时间")
    private String distanceEnableDate;
    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    @FieldInterpretation(value = "来源类型")
    private String sourceType;
    /**
     * 原始报文组织ID
     */
    @ApiModelProperty(value = "原始报文组织ID")
    @FieldInterpretation(value = "原始报文组织ID")
    private String originalOrgId;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @FieldInterpretation(value = "版本号")
    private Integer versionValue;
    /**
     * 物品分类
     */
    @ApiModelProperty(value = "物品分类")
    @ExcelProperty(value = "物品分类")
    private String productClassify;
    /**
     * 物料id
     */
    @ApiModelProperty(value = "物料id")
    @FieldInterpretation(value = "物料id")
    private String productId;
    /**
     * 库存点id
     */
    @ApiModelProperty(value = "物料id")
    @FieldInterpretation(value = "物料id")
    private String stockPointId;

    /**
     * 装车位置
     */
    @ApiModelProperty(value = "物品分类")
    private String loadingPosition;
    /**
     * 超期天数
     */
    @ApiModelProperty(value = "超期天数")
    @FieldInterpretation(value = "超期天数")
    private Integer overdueDays;
    /**
     * 物料类型
     */
    @ApiModelProperty(value = "物料类型")
    @FieldInterpretation(value = "物料类型")
    private String productType;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @FieldInterpretation(value = "厚度")
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String productColor;
    /**
     * 分配状态
     */
    @ApiModelProperty(value = "分配状态")
    @FieldInterpretation(value = "分配状态")
    private String allocationStatus;

    /**
     * 总现存件数
     * 现有量 * 片/箱
     */
    @ApiModelProperty(value = "总现存件数")
    @FieldInterpretation(value = "总现存件数")
    private BigDecimal currentQuantitySum;
    /**
     * 原始物料编码
     */
    @ApiModelProperty(value = "原始物料编码")
    @FieldInterpretation(value = "原始物料编码")
    private String originalProductCode;


    /**
     * 物料长度
     */
    @ApiModelProperty(value = "物料长度")
    @FieldInterpretation(value = "物料长度")
    private BigDecimal productLength;

    /**
     * 物料厚度
     */
    @ApiModelProperty(value = "物料厚度")
    @FieldInterpretation(value = "物料厚度")
    private BigDecimal productWidth;

    /**
     * 物料面积
     */
    @ApiModelProperty(value = "物料面积")
    @FieldInterpretation(value = "物料面积")
    private BigDecimal area;

    /**
     * 物料重量
     */
    @ApiModelProperty(value = "物料重量")
    @FieldInterpretation(value = "物料重量")
    private BigDecimal weight;
    /**
     * 质检状态
     */
    @ApiModelProperty(value = "质检状态")
    @FieldInterpretation(value = "质检状态")
    private String qcStatus;

    @Override
    public void clean() {

    }

}
