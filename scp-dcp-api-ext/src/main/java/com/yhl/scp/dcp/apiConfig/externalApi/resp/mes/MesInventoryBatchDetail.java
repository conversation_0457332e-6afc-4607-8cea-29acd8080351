package com.yhl.scp.dcp.apiConfig.externalApi.resp.mes;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

/**
 * <code>MesInventoryBatchDetail</code>
 * <p>
 * MES库存批次明细返回体
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 11:27:15
 */
@Data
public class MesInventoryBatchDetail {

    /**
     * 最后修改日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateDate;

    /**
     * 现有量
     */
    private Integer quantity;

    /**
     * 客户号
     */
    private String supplierId;

    /**
     * 条码号
     */
    private String code;

    /**
     * 货位表描述
     */
    private String locatorDesc;

    /**
     * 组织
     */
    private String plantId;

    /**
     * 子库存描述
     */
    private String warehouseDesc;

    /**
     * 批次
     */
    private String lotNumber;

    /**
     * 子库存
     */
    private String warehouseCode;

    /**
     * 零件号
     */
    private String partNum;

    /**
     * 类型编码
     */
    private String typeCode;

    /**
     * 本厂编号id
     * 与物料表的inventory_item_id字段关联
     */
    private String itemId;
    /***
     * 本厂编号
     */
    private String itemCode;

    /**
     * 货位
     */
    private String locatorCode;

    /**
     * 入库时间
     */
    private String assignedTime;
    /**
     * qc状态
     */
    private String qcStatus;
    /**
     * 是否B类物料
     */
    private String isb;

}
