package com.yhl.scp.mds.newproduct.convertor;

import com.yhl.scp.mds.newproduct.domain.entity.NewProductStockPointDO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointExcelDTO;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>ProductStockPointConvertor</code>
 * <p>
 * 物品转换器
 * </p>
 *
 * @version 1.0
 * @since 2024-07-30 16:02:09
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface NewProductStockPointConvertor {

    NewProductStockPointConvertor INSTANCE = Mappers.getMapper(NewProductStockPointConvertor.class);

    NewProductStockPointDO dto2Do(NewProductStockPointDTO obj);

    NewProductStockPointDTO do2Dto(NewProductStockPointDO obj);

    List<NewProductStockPointDO> dto2Dos(List<NewProductStockPointDTO> list);

    List<NewProductStockPointDTO> do2Dtos(List<NewProductStockPointDO> list);

    NewProductStockPointVO do2Vo(NewProductStockPointDO obj);

    NewProductStockPointVO po2Vo(NewProductStockPointPO obj);

    List<NewProductStockPointVO> po2Vos(List<NewProductStockPointPO> list);

    NewProductStockPointPO do2Po(NewProductStockPointDO obj);

    NewProductStockPointDO po2Do(NewProductStockPointPO obj);

    NewProductStockPointPO dto2Po(NewProductStockPointDTO obj);

    List<NewProductStockPointPO> dto2Pos(List<NewProductStockPointDTO> obj);

    NewProductStockPointDTO po2Dto(NewProductStockPointPO obj);

    List<NewProductStockPointDTO> po2Dtos(List<NewProductStockPointPO> list);

    List<NewProductStockPointExcelDTO> po2ExportDtos(List<NewProductStockPointVO> productStockPointVOList);
}
