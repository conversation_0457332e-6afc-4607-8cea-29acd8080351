package com.yhl.scp.mds.bom.controller;

import java.util.List;

import javax.annotation.Resource;

import com.yhl.scp.mds.bom.vo.ProductBomAlternativeVO;
import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mds.bom.dto.ProductBomDTO;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>MdsProductBomController</code>
 * <p>
 * 物品BOM控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18 17:23:03
 */
@Slf4j
@Api(tags = "Mds物品BOM控制器")
@RestController
@RequestMapping("mdsProductBom")
public class MdsProductBomController extends BaseController {

    @Resource
    private MdsProductBomService mdsProductBomService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<ProductBomVO>> page() {
        List<ProductBomVO> productBomList = mdsProductBomService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ProductBomVO> pageInfo = new PageInfo<>(productBomList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ProductBomDTO productBomDTO) {
        return mdsProductBomService.doCreate(productBomDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ProductBomDTO productBomDTO) {
        return mdsProductBomService.doUpdate(productBomDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
    	mdsProductBomService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<ProductBomVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, mdsProductBomService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "查询替代料")
    @GetMapping(value = "selectAlternative")
    public BaseResponse<PageInfo<ProductBomAlternativeVO>> selectAlternative(@RequestParam("productCode") String productCode,
                                                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, mdsProductBomService.selectAlternative(productCode, pageNum, pageSize));
    }
}
